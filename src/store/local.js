import { defineStore } from "pinia"

const useLocalStore = defineStore("local", {
    state: () => {
        return {
            local: {
                userId: null,
                shareOption: {},
                share: null
            }
        }
    },
    getters: {
        examineList(state) {
            return state.local.examineList
        },
        agreeFace(state) {
            return state.local.agreeFace
        },
        share(state) {
            return state.local.share
        },
        shareOption(state) {
            return state.local.shareOption
        }
    },
    actions: {
        setExamineList(list) {
            this.local.examineList = list
        },
        setAgreeFace() {
            this.local.agreeFace = true
        },
        // 是否为分享页面
        setShare(share, option) {
            this.local.share = share
            this.local.shareOption = option
        },
        clear() {
            this.local = {}
            this.stackPage = {
                pages: [],
                num: 0
            }
        }
    },

    persist: {
        key: "yd-mobile-local",
        paths: ["local"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useLocalStore

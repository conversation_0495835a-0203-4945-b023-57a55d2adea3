import { defineStore } from "pinia"
import { existingApp } from "@/utils/existingApp"

import http from "@/utils/http"

const useHomeStore = defineStore("home", {
    state: () => {
        return {
            home: {
                // 快捷应用数据
                quickList: []
            }
        }
    },
    getters: {
        quickList(state) {
            return state.home.quickList
        }
    },
    actions: {
        async queryQuickList() {
            try {
                const res = await http.get("/app/appCenter/getAppHome")
                this.home.quickList = res.data?.filter((i) => existingApp(i.apps))
            } catch (error) {
                console.log("获取快捷应用数据失败", error)
            }
        }
    },

    persist: {
        key: "yd-mobile-home",
        paths: ["home"],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useHomeStore

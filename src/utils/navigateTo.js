function serializeToQueryString(obj) {
    if (!obj) {
        return "v=1"
    }
    const params = []
    for (const [key, value] of Object.entries(obj)) {
        const encodedKey = encodeURIComponent(key)
        const encodedValue = encodeURIComponent(value)
        params.push(`${encodedKey}=${encodedValue}`)
    }
    return params.join("&")
}

/**
 * 路由跳转方法
 */
const navigateTo = ({ url, query, ...option }) => {
    if (getCurrentPages().length >= 10) {
        uni.redirectTo({ url: `${url}?${serializeToQueryString(query)}`, ...option })
    } else {
        uni.navigateTo({ animationType: "pop-in", animationDuration: 200, url: `${url}?${serializeToQueryString(query)}`, ...option })
    }
}

export default navigateTo

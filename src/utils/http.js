import { getToken } from "@/utils/storageToken.js"
// #ifdef H5-WEIXIN || H5
import { EventSourceParserStream } from "eventsource-parser/stream"
// #endif
const host = import.meta.env.VITE_BASE_API
const myToken = () => {
    let token = getToken()
    if (token?.indexOf("Bearer") == -1) {
        token = "Bearer " + token
    }
    return token
}
const toast = {
    start: () => {
        uni.showToast({
            title: "加载中..",
            duration: 10000,
            icon: "loading"
        })
    },
    error: (msg) => {
        uni.showToast({
            title: msg,
            icon: "none",
            duration: 2000
        })
    },
    end: () => {
        uni.hideToast()
    }
}

// 需要跳转到登录页的code
const toLoginCode = [401, 403, 1001001001, 1002002012, 1002002013, 1002002014, 1002002024, 1002017013]

// 清空跳转到登录页面
const errorHandle = (message) => {
    toast.error(message)
    setTimeout(() => {
        uni.clearStorageSync()
        uni.reLaunch({ url: "/pages/login/index" })
    }, 1000)
}

function get(url, data) {
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            data,
            header: {
                Authorization: myToken()
            },
            success: (res) => {
                const resdata = res.data
                if (resdata.status == 404) {
                    toast.error("服务器异常")
                    reject(resdata)
                } else if (resdata.code == 0) {
                    resolve(resdata)
                } else if (toLoginCode.includes(resdata?.code)) {
                    errorHandle(resdata?.message)
                    reject(resdata)
                } else {
                    toast.error(resdata.message)
                    reject(resdata)
                }
            },
            fail: (error) => {
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}
function post(url, data, option = {}) {
    let header = {
        Authorization: myToken(),
        ...option
    }
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            method: "POST",
            data,
            header,
            success: (res) => {
                const resdata = res.data
                if (resdata.code == 0) {
                    resolve(resdata)
                } else if (toLoginCode.includes(resdata?.code)) {
                    errorHandle(resdata?.message)
                    reject(resdata)
                } else {
                    toast.error(resdata.message)
                    reject(resdata)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}

function uploadFile(url, path, data, file) {
    return new Promise((resolve, reject) => {
        const obj = {}
        if (path) {
            obj.filePath = path
        } else if (file) {
            obj.file = file
        }
        uni.uploadFile({
            header: {
                Authorization: myToken()
            },
            url: host + url,
            ...obj,
            formData: data,
            name: "file",
            success: (res) => {
                const r = JSON.parse(res.data)
                console.log(r)
                if (r.code == 0) {
                    resolve(r.data[0].url)
                } else {
                    uni.showToast({
                        title: r.data.detailMessage || r.data.message,
                        icon: "none",
                        duration: 2000
                    })
                    reject(r.data)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}

// 无需token等参数的API
function _fetch(url, data, method = "POST", header = {}) {
    return new Promise((resolve, reject) => {
        uni.request({
            url: host + url,
            data,
            method,
            header,
            success(res) {
                if (res.data.code == 0) {
                    resolve(res.data)
                } else {
                    toast.error(res.data.message)
                    reject(res.data)
                }
            },
            fail: (error) => {
                console.log(error)
                toast.error(error.errMsg)
                reject(error)
            }
        })
    })
}
let controller = null // 存储当前控制器
// 终止请求
function cancelRequest() {
    if (controller) {
        controller.abort()
        controller = null
        console.log("已终止请求")
    }
}
async function fetchSSE(url, data, onMessage) {
    // 终止之前的请求（如果存在）
    if (controller) {
        controller.abort()
    }
    // 创建新控制器
    controller = new AbortController()
    const response = await fetch(`${host}${url}`, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
            Authorization: myToken(),
            "Content-Type": "application/json"
        },
        signal: controller.signal // 绑定中止信号
    })
    const res = response.clone().json()
    // #ifdef H5-WEIXIN || H5
    const reader = response?.body?.pipeThrough(new TextDecoderStream()).pipeThrough(new EventSourceParserStream()).getReader()
    // #endif
    // #ifdef MP-WEIXIN
    const reader = response?.body?.pipeThrough(new TextDecoderStream()).pipeThrough().getReader()
    // #endif
    while (true) {
        const x = await reader?.read()
        if (x) {
            const { done, value } = x
            try {
                const val = value?.data ? JSON.parse(value.data) : ""
                if (!["ping"].includes(val?.event)) {
                    onMessage({
                        ...val
                    })
                }
            } catch (e) {
                console.warn(e)
            }
            if (done) {
                console.info("done")
                break
            }
        }
    }
}

export default { get, post, uploadFile, _fetch, fetchSSE, cancelRequest }

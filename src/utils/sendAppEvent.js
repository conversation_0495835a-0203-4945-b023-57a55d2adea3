// #ifdef H5 || H5-WEIXIN
import * as dd from "dingtalk-jsapi"
// #endif
import { setToken } from "@/utils/storageToken.js"

// ios滚动条高度
function IOSHeightFn() {
    ;/iphone|ipod|ipad/i.test(navigator.userAgent) &&
        document.addEventListener(
            "blur",
            (event) => {
                // 当页面没出现滚动条时才执行，因为有滚动条时，不会出现这问题
                // input textarea 标签才执行，因为 a 等标签也会触发 blur 事件
                if (document.documentElement.offsetHeight <= document.documentElement.clientHeight && ["input", "textarea"].includes(event.target.localName)) {
                    document.body.scrollIntoView() // 回顶部
                }
            },
            true
        )
}

// 判断当前在哪个环境下
export function checkPlatform() {
    IOSHeightFn()
    // #ifdef H5 || H5-WEIXIN
    const ua = navigator.userAgent.toLowerCase()
    if (dd.env.platform !== "notInDingTalk") {
        console.log("当前环境 ——— 钉钉环境")
        return "dingding"
    }
    if (ua.match(/wxwork/i) == "wxwork") {
        console.warn("当前环境 ——— 企业微信环境")
        return "wxwork"
    }
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
        console.warn("当前环境 ——— 微信浏览器环境")
        return "wx-miniprogram"
    }
    if (navigator.userAgent === "yide-ios-app") {
        console.warn("当前环境 ——— IOSApp")
        return "yide-ios-app"
    }
    if (navigator.userAgent === "yide-android-app") {
        console.warn("当前环境 ——— AndroidApp")
        return "yide-android-app"
    }

    if (navigator.userAgent === "yide-Harmony-app") {
        console.warn("当前环境 ——— HarmonyApp------checkPlatform")
        alert("当前环境 ——— HarmonyApp----checkPlatform")
        return "yide-Harmony-app"
    }

    console.warn("当前环境 ——— 普通浏览器")
    return "browser"
    // #endif

    // #ifdef MP-WEIXIN
    console.log("当前环境 ——— 微信小程序")
    return "mp-weixin"
    // #endif
}

// 给IOS和Android发送事件
export const sendAppEvent = (enentName, params = {}) => {
    // 调用原生App事件
    switch (checkPlatform()) {
        case "wx-miniprogram":
            document.addEventListener("UniAppJSBridgeReady", () => {
                //传递的消息信息，必须写在 data 对象中。
                uni.postMessage({
                    enentName,
                    data: params
                })
            })
        case "yide-android-app":
            // 处理Android逻辑
            return (
                window.android &&
                window.android.postMessage &&
                window.android.postMessage(
                    JSON.stringify({
                        enentName,
                        params
                    })
                )
            )
        case "yide-ios-app":
            // 处理IOS逻辑
            return window.webkit?.messageHandlers.call.postMessage(
                JSON.stringify({
                    enentName,
                    params
                })
            )
        case "yide-Harmony-app":
            // 处理Harmony逻辑
            alert("当前环境 ——— HarmonyApp---sendAppEvent")
            uni.showToast({
                title: "222222t",
                duration: 100
            })
            // HarmonyOS 使用 window.NativeApp 调用原生方法
            if (!window.NativeApp) {
                uni.showToast({
                    title: `${enentName}-4444444444444444`,
                    duration: 10000
                })
                console.error("NativeApp interface not available")
                return null
            }
            if (enentName === "getToken") {
                uni.showToast({
                    title: `${enentName}-getToken`,
                    duration: 10000
                })
                try {
                    const token = window.NativeApp.getToken() // 同步调用
                    return token
                } catch (error) {
                    console.error("Error calling getToken:", error)
                    return null
                }
            } else if (enentName === "backApp") {
                uni.showToast({
                    title: `${enentName}-backApp`,
                    duration: 10000
                })
                try {
                    window.NativeApp.backApp()
                    return true
                } catch (error) {
                    console.error("Error calling backApp:", error)
                    return false
                }
            } else {
                uni.showToast({
                    title: `${enentName}-nullApp`,
                    duration: 10000
                })
                console.error("Unsupported eventName:", enentName)
                return null
            }
        default:
            break
    }
}

// 存储IOS-App给的数据
function getIosData() {
    const token = window.prompt("getToken")
    setToken(token)
    const userInfo = window.prompt("getUserInfo")
    if (userInfo) {
        console.warn(JSON.parse(userInfo), token, "IosData")
    }
}

// 存储Android-App给的数据
function getAndroidData() {
    const token = sendAppEvent("getToken", {})
    setToken(token)
    const userInfo = sendAppEvent("getUserInfo", {})
    if (userInfo) {
        console.warn(JSON.parse(userInfo), token, "IosData")
    }
}
// 存储Harmony-App给的数据
function getHarmonyData() {
    const token = sendAppEvent("getToken", {})
    setToken(token)
    const userInfo = sendAppEvent("getUserInfo", {})
    if (userInfo) {
        console.warn(JSON.parse(userInfo), token, "IosData")
    }
}
export function browserEnvironment() {
    switch (checkPlatform()) {
        case "yide-android-app":
            // 获取Android数据
            getAndroidData()

        case "yide-ios-app":
            // 获取IOS数据
            getIosData()

        case "yide-Harmony-app":
            // 获取Harmony数据
            // alert("当前环境 ——— HarmonyApp=----browserEnvironment")
            getHarmonyData()

        default:
            false
            break
    }
}

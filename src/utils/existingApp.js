// 已有的应用筛选
const appShow = ["sanctions", "antiBullying", "attendance", "campusStyle", "clubManage", "dormManage", "evalActivity", "Intelligence", "leaveSchool", "moralEducationEvaluation", "myStudent", "notice", "patrol", "punchTheClock", "registration", "schedule", "schoolAssignment", "schoolTable", "scoreManage", "siteBooking", "studentAttendance", "teacherAttendance", "traffic", "videoAlbum", "visitorSystem", "weeklyPublication", "library", "canteenMachine", "oaApprove"]

function existingApp(apps) {
    if (apps && apps.length > 0) {
        const flag = apps.find((i) => appShow.includes(i.routePath))
        return flag ? true : false
    }
    return false
}

export { existingApp, appShow }

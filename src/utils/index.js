import { Base64 } from "js-base64"
/*
 * @Description: 工具函数
 * @Date: 2024-10-09 10:20:12
 * @LastEditors: lhq
 * @LastEditTime: 2024-11-12 18:00:50
 * @FilePath: \code\cloud-mobile\src\utils\index.js
 */

// 日期时间格式转换
export function formatDateTime(str = new Date(), type) {
    var date = new Date(str)
    var y = date.getFullYear()
    var m = date.getMonth() + 1
    m = m < 10 ? "0" + m : m
    var d = date.getDate()
    d = d < 10 ? "0" + d : d
    var h = date.getHours()
    h = h < 10 ? "0" + h : h
    var minute = date.getMinutes()
    minute = minute < 10 ? "0" + minute : minute
    var second = date.getSeconds()
    second = second < 10 ? "0" + second : second
    if (type == "d") {
        return y + "-" + m + "-" + d
    } else if (type == "minute") {
        return y + "-" + m + "-" + d + " " + h + ":" + minute
    } else if (type == 2) {
        return y + "." + m + "." + d + " " + h + ":" + minute + ":" + second
    } else if (type == 3) {
        return m + "." + d + " " + h + ":" + minute
    } else if (type == 4) {
        return h + ":" + minute + ":" + second
    } else if (type == 5) {
        return y + "." + m + "." + d
    } else {
        return y + "-" + m + "-" + d + " " + h + ":" + minute + ":" + second
    }
}

export function debounce(fn, delay = 500) {
    let timer = null
    return function () {
        const context = this
        const args = arguments
        if (timer) {
            clearTimeout(timer)
        }
        timer = setTimeout(() => {
            fn && fn.apply(context, args)
        }, delay)
    }
}

/**
 * 毫秒转时分秒
 * @param {*} milliseconds
 * @returns
 */
export function formatDuration(milliseconds) {
    // 检查输入是否为数字
    if (typeof milliseconds !== "number" || isNaN(milliseconds)) {
        return "00:00"
    }

    // 检查输入是否为非负数
    if (milliseconds < 0) {
        return "00:00"
    }

    // 将毫秒转换为秒
    let totalSeconds = Math.floor(milliseconds / 1000)

    // 计算小时数
    const hours = Math.floor(totalSeconds / 3600)
    // 计算剩余的秒数
    totalSeconds %= 3600
    // 计算分钟数
    const minutes = Math.floor(totalSeconds / 60)
    // 剩余的就是秒数
    const seconds = totalSeconds % 60

    // 使用padStart方法确保每部分都是两位数
    const formattedHours = String(hours).padStart(2, "0")
    const formattedMinutes = String(minutes).padStart(2, "0")
    const formattedSeconds = String(seconds).padStart(2, "0")

    // 返回格式化后的字符串
    // 如果小时为0，则不显示小时部分
    console.log("hours", hours)
    if (hours > 0) {
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`
    } else {
        return `${formattedMinutes}:${formattedSeconds}`
    }
}

/**
 * @description: 复制链接
 * @return {string} "00"
 */
export function copyUrl(url) {
    uni.setClipboardData({
        data: url,
        success: function () {
            uni.showToast({
                title: "复制成功",
                duration: 2000,
                icon: "none"
            })
        },
        fail: function (err) {
            uni.showToast({
                title: "复制失败",
                duration: 2000,
                icon: "none"
            })
        }
    })
}

// 手机号中间四位用*号代替
export function hideMiddleFour(phoneNumber) {
    if (phoneNumber && phoneNumber.length >= 4) {
        // 截取前三位和后四位，中间用*号连接
        return phoneNumber.slice(0, 3) + "****" + phoneNumber.slice(-4)
    } else {
        // 如果电话号码长度不足，直接返回原号码或处理错误
        return phoneNumber // 或者可以抛出错误提示，如：throw new Error('电话号码过短！');
    }
}

export function beikeGroup(arr, subGroupLength) {
    const array = [...arr]
    if (Array.isArray(array)) {
        let index = 0
        const newArray = []
        while (index < array.length) {
            newArray.push(array.slice(index, (index += subGroupLength)))
        }
        return newArray
    }
    return []
}

export function previewFileByUrl(url) {
    const codeUrL = encodeURIComponent(Base64.encode(url))
    window.open(`${import.meta.env.VITE_BASE_FILE_PREVIEW}/onlinePreview?url=${codeUrL}`)
}

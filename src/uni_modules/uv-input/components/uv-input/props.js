export default {
    props: {
        value: {
            type: [String, Number],
            default: ""
        },
        modelValue: {
            type: [String, Number],
            default: ""
        },
        // 输入框类型
        // number-数字输入键盘，app-vue下可以输入浮点数，app-nvue和小程序平台下只能输入整数
        // idcard-身份证输入键盘，微信、支付宝、百度、QQ小程序
        // digit-带小数点的数字键盘，App的nvue页面、微信、支付宝、百度、头条、QQ小程序
        // text-文本输入键盘
        type: {
            type: String,
            default: "text"
        },
        // 是否禁用输入框
        disabled: {
            type: Boolean,
            default: false
        },
        // 禁用状态时的背景色
        disabledColor: {
            type: String,
            default: "#f5f7fa"
        },
        // 是否显示清除控件
        clearable: {
            type: Boolean,
            default: false
        },
        // 是否密码类型
        password: {
            type: Boolean,
            default: false
        },
        // 最大输入长度，设置为 -1 的时候不限制最大长度
        maxlength: {
            type: [String, Number],
            default: -1
        },
        // 	输入框为空时的占位符
        placeholder: {
            type: String,
            default: null
        },
        // 指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/
        placeholderClass: {
            type: String,
            default: "input-placeholder"
        },
        // 指定placeholder的样式
        placeholderStyle: {
            type: [String, Object],
            default: "color: #c0c4cc"
        },
        // 设置右下角按钮的文字，有效值：send|search|next|go|done，兼容性详见uni-app文档
        // https://uniapp.dcloud.io/component/input
        // https://uniapp.dcloud.io/component/textarea
        confirmType: {
            type: String,
            default: "done"
        },
        // 点击键盘右下角按钮时是否保持键盘不收起，H5无效
        confirmHold: {
            type: Boolean,
            default: false
        },
        // focus时，点击页面的时候不收起键盘，微信小程序有效
        holdKeyboard: {
            type: Boolean,
            default: false
        },
        // 自动获取焦点
        // 在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点
        focus: {
            type: Boolean,
            default: false
        },
        // 键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效
        autoBlur: {
            type: Boolean,
            default: false
        },
        // 指定focus时光标的位置
        cursor: {
            type: [String, Number],
            default: -1
        },
        // 输入框聚焦时底部与键盘的距离
        cursorSpacing: {
            type: [String, Number],
            default: 30
        },
        // 光标起始位置，自动聚集时有效，需与selection-end搭配使用
        selectionStart: {
            type: [String, Number],
            default: -1
        },
        // 光标结束位置，自动聚集时有效，需与selection-start搭配使用
        selectionEnd: {
            type: [String, Number],
            default: -1
        },
        // 键盘弹起时，是否自动上推页面
        adjustPosition: {
            type: Boolean,
            default: true
        },
        // 输入框内容对齐方式，可选值为：left|center|right
        inputAlign: {
            type: String,
            default: "left"
        },
        // 输入框字体的大小
        fontSize: {
            type: [String, Number],
            default: "14px"
        },
        // 输入框字体颜色
        color: {
            type: String,
            default: "#303133"
        },
        // 输入框前置图标
        prefixIcon: {
            type: String,
            default: ""
        },
        // 前置图标样式，对象或字符串
        prefixIconStyle: {
            type: [String, Object],
            default: ""
        },
        // 输入框后置图标
        suffixIcon: {
            type: String,
            default: ""
        },
        // 后置图标样式，对象或字符串
        suffixIconStyle: {
            type: [String, Object],
            default: ""
        },
        // 边框类型，surround-四周边框，bottom-底部边框，none-无边框
        border: {
            type: String,
            default: "surround"
        },
        // 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会
        readonly: {
            type: Boolean,
            default: false
        },
        // 输入框形状，circle-圆形，square-方形
        shape: {
            type: String,
            default: "square"
        },
        // 用于处理或者过滤输入框内容的方法
        formatter: {
            type: [Function, null],
            default: null
        },
        // 是否忽略组件内对文本合成系统事件的处理
        ignoreCompositionEvent: {
            type: Boolean,
            default: true
        },
        ...uni.$uv?.props?.input
    }
}

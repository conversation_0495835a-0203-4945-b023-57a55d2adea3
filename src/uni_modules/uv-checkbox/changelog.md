## 1.0.14（2023-11-04）
1. 修复label文字较多不分行的问题
## 1.0.13（2023-10-11）
1. 优化同类问题：https://gitee.com/climblee/uv-ui/issues/I872VD
## 1.0.12（2023-09-22）
1. 修复change回调中v-model值不更新的BUG
## 1.0.11（2023-09-01）
1. 修复点击空隙处无效的问题
2. label支持插槽下可点击
## 1.0.10（2023-08-27）
1. 修复label设置布尔值不生效的BUG
## 1.0.9（2023-08-16）
1. 解决数据多不换行的BUG
## 1.0.8（2023-07-13）
1. 修复  uv-checkbox设置value属性不生效的BUG
## 1.0.7（2023-07-05）
修复vue3模式下，动态修改v-model绑定的值无效的BUG
## 1.0.6（2023-06-29）
1. 增加label插槽，与radio保持一致
2. 优化文档
## 1.0.5（2023-06-12）
1. 修复1.0.4改出的问题
## 1.0.4（2023-06-08）
1. 复选框修复全局设置不生效的BUG
## 1.0.3（2023-06-06）
1. uv-checkbox-group 兼容自定义样式customStyle，方便通过样式调整整体位置等；
2. .uv-checkbox-group--row增加flex-wrap: wrap;允许换行
## 1.0.2（2023-05-30）
1. 修复error报错的BUG
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-checkbox 复选框

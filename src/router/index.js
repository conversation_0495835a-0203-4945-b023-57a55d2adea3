import { getToken } from "@/utils/storageToken.js"
import useStore from "@/store"
export default () => {
    // 免登录白名单
    const whiteList = []
    const routerFn = ["navigateTo", "redirectTo", "reLaunch"]

    routerFn.forEach((name) => {
        uni.addInterceptor(name, {
            invoke(e) {
                const { system } = useStore()
                const arr = system.tabBarList.filter((item) => e.url.includes(item.pagePath))
                if (arr && arr.length) {
                    system.switchTab({
                        id: arr[0].id
                    })
                }
                e.url = e.url.replace(/\?+/g, "?")
                if (e.url.endsWith("?")) {
                    e.url = e.url.substring(0, e.url.length - 1)
                }
                if (whiteList.includes(e.url)) {
                    return true
                }
                const isLogin = getToken()
                // TODO: 其余特殊情况待完善
                if (isLogin) {
                    return true
                } else {
                    return true
                }
            },
            success(args) {
                // console.log('--success--', args.eventChannel)
            },
            fail(err) {
                uni.showToast({
                    title: "暂无权限!",
                    icon: "none"
                })
            }
        })
    })
}

import { getCurrentInstance, ref, onBeforeUnmount, } from 'vue';


/**
 * useShowLoading 交互状态
 */
export function useShowLoading() {
    const loading = ref(false)
    return {
        loading: loading,
        startLoading(title = "Loading...") {
            const config = {}
            if (typeof title == "string") {
                config["title"] = title
            }
            if (Object.prototype.toString.call(title) === "[object Object]") {
                config = {
                    ...title
                }
            }
            uni.showLoading(config)
            loading.value = true
        },
        stopLoading() {
            uni.hideLoading()
            loading.value = false
        }
    }
}

// 角色选择页面中 图片背景色枚举
export function useRole() {
    const roleImg = computed(() => {
        return (role) => {
            if (!role) return "/ordinaryTeacher.png"
            const roleUrl = {
                ordinaryTeacher: "/ordinaryTeacher.png", // 普通教职工
                eltern: "/eltern.png", //家长
                registrar: "/registrar.png", //教务主任
                headmaster: "/headmaster.png", //班主任
                teacher: "/teacher.png", //任课老师
                dorm_admin: "/dormAdmin.png", //宿舍管理员
                student: "/ordinaryTeacher.png" // 学生
            }
            return roleUrl[role]
        }
    })
    const roleBgColor = computed(() => {
        return (role) => {
            if (!role) return "#FFF6EB"
            const roleBg = {
                ordinaryTeacher: "#FFF6EB", // 普通教职工
                eltern: "#ECFFFF", //家长
                registrar: "#FFEFEB", //教务主任
                headmaster: "#ECFFF3", //班主任
                teacher: "#F2F9FF", //任课老师
                dorm_admin: "#F1F5FF", //宿舍管理员
                student: "#FFF6EB" // 学生
            }
            return roleBg[role]
        }
    })
    return {
        roleImg,
        roleBgColor
    }
}


/**
 * useCurrentPage 获取页面参数
 * 
 * @param {Function} callback  ,  当获取页面 params 时回调函数
 * @param {String} [key]       ,  页面 params 数据需要缓存的key
 * @return {Object}            ,  返回当前调用页面参数query及实列
 */
export function useCurrentPage(callback, key) {
    if (typeof callback != 'function') console.error('useCurrentPage The first parameter must be a function')
    if (typeof key != 'string') console.error('useCurrentPage The second parameter must be a string')
    const page = getCurrentPages()[0];
    const instance = getCurrentInstance().proxy
    const eventChannel = instance.getOpenerEventChannel();
    const query = page.options
    const useCache = key && typeof key == 'string'
    const cacheKey = `__temp-${key}`

    if (useCache) {
        const data = uni.getStorageSync(cacheKey);
        if (callback && data != '' && data !== null && data !== undefined) {
            callback(data)
        }
    }
    // let params = ref({})
    eventChannel.on('_GETPAGEPARAMS', (data) => {
        // params.value = data
        // TODO：异步，先使用回调处理不使用async避免污染调用方
        // 最佳方案： 等一个有缘人改写成成同步返回
        callback && callback(data)
        // 需要缓存处理
        if (useCache) uni.setStorageSync(cacheKey, data);
    })
    onBeforeUnmount(() => {
        eventChannel.off('_GETPAGEPARAMS')
        if (useCache) uni.removeStorageSync(cacheKey);
    })
    return {
        page,
        query,
        // params
    }
}
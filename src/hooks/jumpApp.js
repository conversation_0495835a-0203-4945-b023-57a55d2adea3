import useStore from "@/store"
let store = null
export default (app) => {
	if (!store) {
		store = useStore()
	}
	/**用户角色 */
	const roleCode = computed(() => store.user?.identityInfo?.roleCode)
	const { code, routePath } = app
	const urlObj = {
		visitorSystem: `/apps/${routePath}/visitorRecord`, // 访客系统
		schoolAssignment: `/apps/${routePath}/${roleCode.value == "eltern" ? "parent" : "teacher"}/index`, // 作业
		attendance: `/apps/${routePath}/${roleCode.value == "teacher" ? "classTeacher" : "lecturer"}/index`,
		Intelligence: `/apps/intelligence/index`, // 智能体
	}
	navigateTo({
		url: urlObj[code] || `/apps/${routePath}/index`,
		query: {
			code,
			routePath
		}
	})
}

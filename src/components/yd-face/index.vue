<template>
    <view>
        <!-- 人脸采集弹框 -->
        <uni-popup ref="facePopup" background-color="#fff" borderRadius="20rpx 20rpx 20rpx 20rpx">
            <view class="face_popup">
                <view class="face_title">开启人脸采集</view>
                <view class="face_content">
                    <image class="face_img" src="@nginx/personalCenter/facePopup.png" alt=""></image>
                    <checkbox-group @change="checkboxChange">
                        <checkbox
                            value="0"
                            :checked="checked"
                            backgroundColor="#fff"
                            borderColor="#eee"
                            activeBackgroundColor="#04B578"
                            activeBorderColor="#04B578"
                            iconColor="#fff"
                            :style="{
                                transform: 'scale(0.6)',
                                borderRadius: '50%',
                                marginTop: '70rpx'
                            }"
                        />
                        <text class="text">
                            同意
                            <text class="text_link" @click="facePolicy">《一加壹人脸识别用户协议》</text>
                        </text>
                    </checkbox-group>
                </view>
                <view class="face_footer">
                    <view class="btn" @click="facePopup.close()">取消</view>
                    <view class="btn" @click="agreeToOpen">同意开启</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import useStore from "@/store"
const { local } = useStore()
const facePopup = ref(false) // 人脸采集弹框
const checked = ref(false) // 是否勾选相关协议

const open = () => {
    facePopup.value.open()
}

const close = () => {
    facePopup.value.close()
}

function facePolicy() {
    navigateTo({
        url: "/package/my/privacyPolicy/facePolicy"
    })
}

// 是否勾选相关协议
function checkboxChange(e) {
    if (e.detail.value == "0") {
        checked.value = true
    } else {
        checked.value = false
    }
}

function agreeToOpen() {
    if (!checked.value) {
        uni.showToast({
            title: "请先阅读并勾选内容！",
            icon: "none",
            duration: 2000
        })
        return
    }
    facePopup.value.close()
    // 只有登录后第一次点击进入时需要提示同意协议
    local.setAgreeFace()
    navigateTo({
        url: "/package/my/face/index"
    })
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.face_popup {
    width: 90vw;
    border-radius: 20rpx;

    .face_title {
        text-align: center;
        height: 108rpx;
        background: $uni-bg-color;
        border-radius: 20rpx 20rpx 0rpx 0rpx;
        line-height: 108rpx;
        font-weight: 500;
        font-size: 34rpx;
        color: $uni-text-color;
    }

    .face_content {
        height: 650rpx;
        background: $uni-bg-color-grey;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        :deep(.uni-checkbox-input) {
            border-radius: 50%;
        }

        .face_img {
            width: 380rpx;
            height: 380rpx;
        }

        .text {
            font-weight: 400;
            font-size: 24rpx;
            color: $uni-text-color-grey;
            line-height: 48rpx;

            .text_link {
                color: $uni-color-primary;
            }
        }
    }

    .face_footer {
        height: 100rpx;
        background: $uni-bg-color;
        border-radius: 0rpx 0rpx 20rpx 20rpx;
        display: flex;
        justify-content: center;

        .btn {
            width: 50%;
            text-align: center;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-color-primary;
            line-height: 100rpx;
        }
    }
}
</style>

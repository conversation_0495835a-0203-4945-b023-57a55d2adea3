<template>
    <editor v-model="state.content" tag-name="textarea" :init="init" style="height: 100%" />
</template>

<script>
import { watch, reactive } from "vue"
import tinymce from "tinymce/tinymce"
import Editor from "@tinymce/tinymce-vue"
import "tinymce/themes/silver/theme" // 引用主题文件
import "tinymce/icons/default" // 引用图标文件

export default {
    props: {
        modelValue: String,
        initInstance: Function,
        toolbar: {
            type: Array,
            default: () => ["bold underline strikethrough link unlink image forecolor fontsizeselect", "hr insertdatetime  aligncenter alignleft alignright alignjustify bullist backcolor"]
        }
    },
    components: {
        editor: Editor
    },
    emits: ["update:modelValue"],
    setup(props, context) {
        const init = {
            selector: "textarea",
            //  toolbar_location:"bottom",
            // mobile: {
            //     menubar: false,// 隐藏菜单栏
            // },
            skin_url: "/tinymce/skins/ui/oxide", // 编辑器皮肤样式
            content_css: "/tinymce/skins/content/default/content.min.css",
            theme: "silver",
            language_url: "/tinymce/langs/zh_CN.js", // 中文语言包路径
            language: "zh_CN",
            //一个汉字算一个字符，为了统计相对准确
            wordcount_countregex: /([\w\u2019\x27\-\u00C0-\u1FFF]+)|([^\x00-\xff])/g,
            menubar: true, // 隐藏菜单栏
            // autoresize_bottom_margin: 50,
            // max_height: 550,
            min_height: 450,
            toolbar_mode: "none",
            // plugins:
            //   "wordcount visualchars visualblocks toc textpattern template tabfocus spellchecker searchreplace save quickbars print preview paste pagebreak noneditable nonbreaking media insertdatetime importcss imagetools image hr help fullscreen fullpage directionality codesample code charmap link code table lists advlist anchor autolink autoresize autosave", // 插件需要import进来
            plugins: "paste image lists advlist autoresize imagetools importcss hr insertdatetime  media",

            toolbar: props.toolbar,
            content_style: "p {margin: 5px 0; font-size: 16px}",
            fontsize_formats: "12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px", //字体大小
            font_formats: "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
            content_style: "img {max-width:100%;}",
            branding: false,
            elementpath: false,
            resize: false, // 禁止改变大小
            image_dimensions: false,
            placeholder: "在这里输入文字",
            statusbar: false, //最下方的元素路径和字数统计那一栏是否显示
            paste_data_images: true, //图片是否可粘贴
            media_url_resolver(data, resolve) {
                try {
                    const videoUri = encodeURI(data.url)
                    const embedHtml = `<p>
						<span
							data-mce-selected="1"
							data-mce-object="video"
							data-mce-p-controls="controls"
							data-mce-p-controlslist="nodownload"
							data-mce-p-allowfullscreen="true"
							style="width: 200px;height:120px;display: block;"
							data-mce-p-src=${videoUri} >
							<video src=${videoUri} width="100%" height="100%" controls="controls" controlslist="nodownload">
							</video>
						</span>
						</p>
						<p style="text-align: left;"></p>`
                    resolve({ html: embedHtml })
                } catch (e) {
                    resolve({ html: "" })
                }
            },
            init_instance_callback: function (editor) {
                const text = props.initInstance()
                text && tinymce.activeEditor.setContent(text)
            },
            images_dataimg_filter: function (img) {
                return img.hasAttribute("internal-blob")
            },
            images_upload_handler: async function (blobInfo, success, failure, percent) {
                if (blobInfo.blob().size / 1024 / 1024 > 5) {
                    failure("上传失败，图片大小请控制在 5M 以内")
                } else {
                    const formData = new FormData()
                    formData.append("file", blobInfo.blob(), blobInfo.filename())
                    try {
                        // const { data } = await newUploadImg(formData, num =>
                        //     percent(num)
                        // );
                        http.post("/cloud/file/upload", formData).then(({ data }) => {
                            // percent(num)
                        })
                        success(data[0] ? data[0].url : "")
                    } catch (err) {
                        failure(err.message)
                    }
                }
            }
        }
        tinymce.init // 初始化
        const revert_data = (content) => {
            context.emit("update:modelValue", content)
        }

        const state = reactive({
            content: props.modelValue
        })

        watch(
            () => state.content,
            (v) => {
                revert_data(v)
            }
        )

        return {
            init,
            revert_data,
            state
        }
    }
}
</script>
<style scoped lang="scss"></style>

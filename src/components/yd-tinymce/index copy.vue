<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: <EMAIL>
 * @Date: 2022-04-20 13:48:12
 * @LastEditors: jingrou
 * @LastEditTime: 2023-04-01 10:22:10
-->
<template>
    <editor v-model="state.content" tag-name="textarea" :init="init" style="height: 100%" />
</template>

<script>
import { watch, reactive } from "vue";
import tinymce from "tinymce/tinymce";
import Editor from "@tinymce/tinymce-vue";
import "tinymce/themes/silver/theme"; // 引用主题文件
import "tinymce/icons/default"; // 引用图标文件
// import "tinymce/plugins/link";
// import "tinymce/plugins/code";
// import "tinymce/plugins/table";
// import "tinymce/plugins/anchor";
// import "tinymce/plugins/autolink"; //锚点
import "tinymce/plugins/autoresize";
import "tinymce/plugins/textcolor"; // 文字颜色
// import "tinymce/plugins/autosave";
// import "tinymce/plugins/charmap"; //特殊字符
// import "tinymce/plugins/code"; //查看源码
// import "tinymce/plugins/codesample"; //插入代码
// import "tinymce/plugins/directionality"; //
// import "tinymce/plugins/fullpage"; //页面属性编辑
// import "tinymce/plugins/fullscreen"; //全屏
// import "tinymce/plugins/help"; //帮助
import "tinymce/plugins/hr"; //横线
import "tinymce/plugins/insertdatetime"; //时间插入
import "tinymce/plugins/media"; //媒体插入
// import "tinymce/plugins/nonbreaking"; //
// import "tinymce/plugins/noneditable"; //不间断空格
// import "tinymce/plugins/pagebreak"; //分页
// import "tinymce/plugins/preview"; //预览
// import "tinymce/plugins/print"; //打印
// import "tinymce/plugins/quickbars"; //快捷菜单
// import "tinymce/plugins/save"; //保存
// import "tinymce/plugins/searchreplace"; //查询替换
// import "tinymce/plugins/spellchecker"; //拼写检查
// import "tinymce/plugins/tabfocus"; //
// import "tinymce/plugins/template"; //插入模板
// import "tinymce/plugins/textpattern"; //
// import "tinymce/plugins/toc"; //
// import "tinymce/plugins/visualblocks"; //
// import "tinymce/plugins/visualchars"; //
// import "tinymce/plugins/wordcount"; //数字统计
import "tinymce/plugins/lists";
import "tinymce/plugins/advlist";
import "tinymce/plugins/image"; //图片
import "tinymce/plugins/imagetools"; //图片工具
import "tinymce/plugins/importcss"; //引入css
import "tinymce/plugins/paste"; //粘贴
import "tinymce/skins/ui/oxide/skin.css";

import { newUploadImg } from "@/api/notice.js";

export default {
    props: {
        modelValue: String,
        initInstance: Function,
        toolbar: {
            type: Array,
            default: () => [
                "bold underline strikethrough link unlink image forecolor fontsizeselect",
                "hr insertdatetime  aligncenter alignleft alignright alignjustify bullist backcolor",
            ],
        },
    },
    components: {
        editor: Editor,
    },
    emits: ["update:modelValue"],
    setup(props, context) {
        const init = {
            selector: "textarea",
            //  toolbar_location:"bottom",
            // mobile: {
            //     menubar: false,// 隐藏菜单栏
            // },
            skin_url: '/tinymce/skins/ui/oxide', // 编辑器皮肤样式
            content_css: '/tinymce/skins/content/default/content.min.css',
            theme: "silver",
            language_url: "/tinymce/langs/zh_CN.js", // 中文语言包路径
            language: "zh_CN",
            //一个汉字算一个字符，为了统计相对准确
            wordcount_countregex:
                /([\w\u2019\x27\-\u00C0-\u1FFF]+)|([^\x00-\xff])/g,
            menubar: true, // 隐藏菜单栏
            // autoresize_bottom_margin: 50,
            // max_height: 550,
            min_height: 450,
            toolbar_mode: "none",
            // plugins:
            //   "wordcount visualchars visualblocks toc textpattern template tabfocus spellchecker searchreplace save quickbars print preview paste pagebreak noneditable nonbreaking media insertdatetime importcss imagetools image hr help fullscreen fullpage directionality codesample code charmap link code table lists advlist anchor autolink autoresize autosave", // 插件需要import进来
            plugins:
                "paste image lists advlist autoresize imagetools importcss hr insertdatetime textcolor media",

            toolbar: props.toolbar,
            content_style: "p {margin: 5px 0; font-size: 16px}",
            fontsize_formats:
                "12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px", //字体大小
            font_formats:
                "微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;",
            content_style: "img {max-width:100%;}",
            branding: false,
            elementpath: false,
            resize: false, // 禁止改变大小
            image_dimensions: false,
            placeholder: "在这里输入文字",
            statusbar: false, //最下方的元素路径和字数统计那一栏是否显示
            paste_data_images: true, //图片是否可粘贴
            media_url_resolver(data, resolve) {
                try {
                    const videoUri = encodeURI(data.url);
                    const embedHtml = `<p>
						<span
							data-mce-selected="1"
							data-mce-object="video"
							data-mce-p-controls="controls"
							data-mce-p-controlslist="nodownload"
							data-mce-p-allowfullscreen="true"
							style="width: 200px;height:120px;display: block;"
							data-mce-p-src=${videoUri} >
							<video src=${videoUri} width="100%" height="100%" controls="controls" controlslist="nodownload">
							</video>
						</span>
						</p>
						<p style="text-align: left;"></p>`;
                    resolve({ html: embedHtml });
                } catch (e) {
                    resolve({ html: "" });
                }
            },
            init_instance_callback: function (editor) {
                const text = props.initInstance();
                text && tinymce.activeEditor.setContent(text);
            },
            //   图片上传
            // file_picker_types: 'file,image,media',
            // file_picker_callback:(callback, value, meta)=>{
            // console.log(callback, value, meta)
            //文件分类
            // var filetype='.pdf, .txt, .zip, .rar, .7z, .doc, .docx, .xls, .xlsx, .ppt, .pptx, .mp3, .mp4';
            // //后端接收上传文件的地址
            // var upurl='/demo/upfile.php';
            // //为不同插件指定文件类型及后端地址
            // switch(meta.filetype){
            //     case 'image':
            //         filetype='.jpg, .jpeg, .png, .gif';
            //         upurl='upimg.php';
            //         break;
            //     case 'media':
            //         filetype='.mp3, .mp4';
            //         upurl='upfile.php';
            //         break;
            //     case 'file':
            //     default:
            // }
            // //模拟出一个input用于添加本地文件
            // var input = document.createElement('input');
            //     input.setAttribute('type', 'file');
            //     input.setAttribute('accept', filetype);
            // input.click();
            // input.onchange = function() {
            //     var file = this.files[0];

            //     var xhr, formData;
            //     console.log(file.name);
            //     xhr = new XMLHttpRequest();
            //     xhr.withCredentials = false;
            //     xhr.open('POST', upurl);
            //     xhr.onload = function() {
            //         var json;
            //         if (xhr.status != 200) {
            //             failure('HTTP Error: ' + xhr.status);
            //             return;
            //         }
            //         json = JSON.parse(xhr.responseText);
            //         if (!json || typeof json.location != 'string') {
            //             failure('Invalid JSON: ' + xhr.responseText);
            //             return;
            //         }
            //         callback(json.location);
            //     };
            //     formData = new FormData();
            //     formData.append('file', file, file.name );
            //     xhr.send(formData);
            // }
            // },
            images_dataimg_filter: function (img) {
                return img.hasAttribute("internal-blob");
            },
            images_upload_handler: async function (
                blobInfo,
                success,
                failure,
                percent
            ) {
                if (blobInfo.blob().size / 1024 / 1024 > 5) {
                    failure("上传失败，图片大小请控制在 5M 以内");
                } else {
                    const formData = new FormData();
                    formData.append(
                        "file",
                        blobInfo.blob(),
                        blobInfo.filename()
                    );
                    try {
                        const { data } = await newUploadImg(formData, num =>
                            percent(num)
                        );
                        success(data[0] ? data[0].url : '');
                    } catch (err) {
                        failure(err.message);
                    }
                }
                // const { code, message, data } = await newUploadImg(formData,(num)=>percent(num));
                // console.log(code)
                // if (code === 0) {
                // 		success(data[0].url);
                // } else {
                // 		failure(message);
                // 		console.error(message, 12121);
                // }
            },
        };
        tinymce.init; // 初始化
        const revert_data = content => {
            context.emit("update:modelValue", content);
        };

        const state = reactive({
            content: props.modelValue,
        });

        watch(
            () => state.content,
            v => {
                revert_data(v);
            }
        );

        return {
            init,
            revert_data,
            state,
        };
    },
};
</script>
<style scoped lang="scss"></style>

<template>
    <view>
        <uni-popup ref="selectPopup" type="bottom" :is-mask-click="false" :safe-area="false">
            <div class="select_popup">
                <div class="title">
                    <span class="text">{{ title }}</span>
                    <uni-icons class="closeempty_icons" type="closeempty" size="22" color="#333" @click="closeFn"></uni-icons>
                </div>
                <div class="select_list">
                    <uni-list :border="false" v-if="list && list.length > 0">
                        <uni-list-item @click="changeItem(item)" clickable v-for="(item, index) in list" :key="index">
                            <template v-slot:header>
                                <slot :item="item">
                                    <text class="list_item"> {{ getItemName(item) }}</text>
                                </slot>
                            </template>
                            <template v-slot:footer>
                                <slot name="right" :data="{ ...item }">
                                    <image v-if="item.isCheck" class="select" src="@nginx/components/selectYes.png"></image>
                                </slot>
                            </template>
                        </uni-list-item>
                    </uni-list>
                    <yd-empty :isMargin="true" text="暂无数据" v-else />
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script setup>
const selectPopup = ref(null)
const emit = defineEmits(["closePopup"])
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: ""
    },
    multiple: {
        type: Boolean,
        default: false
    },
    showRight: {
        type: Boolean,
        default: true
    },
    selectId: {
        type: Array,
        default: () => []
    },
    fieldNames: {
        type: Object,
        default: {
            value: "value",
            label: "label"
        }
    }
})

// 获取用于显示的字段值
const getItemName = (item) => item[props.fieldNames.label]

// 获取用于作为键的字段值
const getItemKey = (item) => item[props.fieldNames.value]

function changeItem(data) {
    if (!props.showRight) return
    if (!props.multiple) {
        props.list.forEach((i) => {
            if (getItemKey(i) == getItemKey(data)) {
                return
            } else {
                i.isCheck = false
            }
        })
        data.isCheck = !data.isCheck
    } else {
        data.isCheck = !data.isCheck
    }
    // 如果是单选的时候 选中后则关闭
    !props.multiple && closeFn()
}

const open = () => {
    selectPopup.value.open()
}

const close = () => {
    selectPopup.value.close()
}

let value = null
function closeFn() {
    if (!props.multiple) {
        value = props.list.find((i) => i.isCheck)
    } else {
        value = props.list.filter((i) => i.isCheck)
    }
    selectPopup.value.close()
    emit("closePopup", value, props.multiple)
}

watch(
    () => props.selectId,
    (value) => {
        props.list.forEach((i) => {
            i.isCheck = !!value.includes(getItemKey(i))
        })
    },
    {
        immediate: true
    }
)

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.select_popup {
    min-height: 300rpx;
    background: $uni-bg-color;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 120rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-weight: 600;
            color: $uni-text-color;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
            margin-left: 62rpx;
        }

        .closeempty_icons {
            margin-right: 18rpx;
        }
    }

    .select {
        height: 40rpx;
        width: 40rpx;
    }

    .text {
        font-size: 28rpx;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: $uni-text-color;

        .time {
            color: $uni-text-color-grey;
        }
    }

    .select_list {
        min-height: 300rpx;
        max-height: 550rpx;
        overflow-y: auto;
    }

    .list_item {
        font-size: 28rpx;
        font-weight: 400;
        color: $uni-text-color;
        line-height: 40rpx;
    }
}
</style>

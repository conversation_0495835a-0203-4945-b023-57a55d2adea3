<template>
    <view class="tab_bar">
        <view class="bar_item__warp" v-for="(item, idx) in data" :key="idx" @click="onClick(item, idx)">
            <uni-badge size="small" :text="item.badge == 0 ? null : item.badge" absolute="rightTop">
                <slot v-if="item.key == 'qrCode'" name="qrCode" :item="{ item: renderItem(item, idx), text: item.text }"> </slot>
                <view v-else class="bar_item" :active="renderItem(item, idx).active">
                    <image class="bar_logo" :src="renderItem(item, idx).src" mode="aspectFit"></image>
                    <text :class="renderItem(item, idx).active ? 'bar_text_active' : ''" class="bar_text">{{ item.text }}</text>
                </view>
            </uni-badge>
        </view>
    </view>
</template>

<script setup>
const { user } = store()
let color = ref("#00b781")
onShow(() => {
    // 动态设置选中颜色
    color.value = user.identityInfo.roleCode === "dorm_admin" ? "#4566d5" : "#00b781"
})

onMounted(() => {
    nextTick(() => {
        // 隐藏tabBar
        uni.hideTabBar({
            success: () => {
                console.log("成功")
            },
            fail: () => {
                console.log("失败")
            }
        })
    })
})

const emit = defineEmits(["yClick"])
const props = defineProps({
    // tabBar data
    data: {
        type: Array,
        default: []
    },
    // check current
    current: {
        type: [Number, String],
        default: 0
    }
})
const page = getCurrentPages()[getCurrentPages().length - 1]
const current = ref(null)

onShow(() => {
    props.data.forEach((item) => {
        // #ifdef MP-WEIXIN
        if (item.pagePath == `/${page.$vm.__route__}`) {
            current.value = item.id
        }
        // #endif
        // #ifdef H5-WEIXIN || H5
        if (item.pagePath == page.$page.path) {
            current.value = item.id
        }
        // #endif
    })
})

const renderItem = computed(() => {
    return (item, idx) => {
        return {
            src: current.value == (item.id || idx) ? item.selectedIconPath : item.iconPath,
            active: current.value == (item.id || idx)
        }
    }
})

const onClick = (item, idx) => {
    current.value = item.id || idx
    emit("yClick", toRaw(item, idx))
}

watch(
    () => props.current,
    (val) => {
        current.value = val
    }
)
</script>

<style lang="scss" scoped>
.tab_bar {
    width: 100%;
    height: 120rpx;
    background: #f8f9fa;
    border-top: 1rpx solid $uni-border-color;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .bar_item__warp {
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 100%;

        .bar_item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding-bottom: 10rpx;
        }

        .bar_logo {
            width: 42rpx;
            height: 42rpx;
        }

        .bar_text {
            font-weight: 400;
            font-size: 20rpx;
            color: $uni-text-color;
            line-height: 28rpx;
            padding-top: 6rpx;
        }

        .bar_text_active {
            color: v-bind(color);
            transition: all 0.3s;
        }
    }
}
</style>

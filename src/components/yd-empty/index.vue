<template>
    <view
        class="no_data"
        :style="{
            margin: isMargin ? '200rpx auto' : '0'
        }"
    >
        <view class="no_data_box">
            <div class="no_data_img" :style="{ width: widthSize + 'px', height: heightSize + 'px' }"></div>
            <view class="no_data_desc">{{ text }}</view>
        </view>
        <view class="no_data_handle" v-if="showBtn">
            <slot>
                <button class="btn" :style="btnStyle" @click="emit('ok')">{{ btnText }}</button>
            </slot>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["ok"])

const props = defineProps({
    // 使用了z-pinging的不要设置为true
    isMargin: {
        type: Boolean,
        default: false
    },
    text: {
        type: String,
        default: "暂无数据！"
    },
    btnText: {
        type: String,
        default: "立即刷新"
    },
    widthSize: {
        type: [String, Number],
        default: "180"
    },
    heightSize: {
        type: [String, Number],
        default: "105"
    },
    showBtn: {
        type: Boolean,
        default: false
    },
    btnStyle: {
        type: String,
        default: ""
    }
})
</script>

<style lang="scss" scoped>
.no_data {
    padding: 0 40rpx;

    .no_data_img {
        background: url("@nginx/components/empty.png") no-repeat;
        background-size: contain;
        width: 360rpx;
        height: 210rpx;
        display: block;
        margin: 0 auto;
    }

    .no_data_desc {
        padding-top: 24rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #8c8c8c;
        line-height: 40rpx;
        text-align: center;
        margin-bottom: 50rpx;
    }

    .no_data_handle {
        text-align: center;

        .btn {
            display: inline-block;
            margin: 40rpx auto 0 auto;
            background: none !important;
            font-weight: 600;
            font-size: 36rpx;
            color: $uni-color-primary;

            &::after {
                content: "";
                border: none;
            }
        }
    }
}
</style>

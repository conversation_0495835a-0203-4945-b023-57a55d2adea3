<!doctype html>
<html>
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
        <title>验证码</title>
        <style type="text/css"></style>
    </head>
    <body style="background: #fff">
        <!-- uni 的 SDK -->
        <!-- 需要把 uni.webview.1.5.4.js 下载到自己的服务器 -->
        <script type="text/javascript" src="https://file.1d1j.cn/cloud-mobile/js/webview.js"></script>
        <script type="text/javascript" src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>

        <script>
            document.addEventListener("UniAppJSBridgeReady", function () {
                //确认是否加载好了TencentCaptcha
                let appid = "193884511" // 腾讯云控制台中对应这个项目的 appid
                let callback = function (res) {
                    //操作验证码后吧回调信息传递给web-view
                    if (res.ret == 0) {
                        uni.postMessage(res)
                    }
                }
                let captcha = new TencentCaptcha(appid, callback)
                // 滑块显示
                captcha.show()
            })
        </script>
    </body>
</html>

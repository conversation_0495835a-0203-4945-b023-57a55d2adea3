<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=3184698" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">智慧校园合同审核</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">项目报名申请</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7aa;</span>
                <div class="name">图书合同审核</div>
                <div class="code-name">&amp;#xe7aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">定制开发合同审核</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79a;</span>
                <div class="name">标书制作申请</div>
                <div class="code-name">&amp;#xe79a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">培训</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">平台账号开通</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">申请定制开发</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79e;</span>
                <div class="name">申请发货</div>
                <div class="code-name">&amp;#xe79e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">申请配货</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a0;</span>
                <div class="name">申请项目报价</div>
                <div class="code-name">&amp;#xe7a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">申请项目方案</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a2;</span>
                <div class="name">申请样机</div>
                <div class="code-name">&amp;#xe7a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">申请制作图纸</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a4;</span>
                <div class="name">售后</div>
                <div class="code-name">&amp;#xe7a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">退货</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">退款申请表单</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a7;</span>
                <div class="name">项目报备</div>
                <div class="code-name">&amp;#xe7a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe726;</span>
                <div class="name">加班</div>
                <div class="code-name">&amp;#xe726;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe727;</span>
                <div class="name">补卡套件</div>
                <div class="code-name">&amp;#xe727;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe728;</span>
                <div class="name">外出</div>
                <div class="code-name">&amp;#xe728;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe724;</span>
                <div class="name">出差</div>
                <div class="code-name">&amp;#xe724;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe725;</span>
                <div class="name">请假套件备份</div>
                <div class="code-name">&amp;#xe725;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68d;</span>
                <div class="name">切换 </div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ee;</span>
                <div class="name">拖拽</div>
                <div class="code-name">&amp;#xe6ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ef;</span>
                <div class="name">编组备份</div>
                <div class="code-name">&amp;#xe6ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f0;</span>
                <div class="name">Batch folding</div>
                <div class="code-name">&amp;#xe6f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ea;</span>
                <div class="name">多行文本备份 2</div>
                <div class="code-name">&amp;#xe6ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6eb;</span>
                <div class="name">单行文本备份 2</div>
                <div class="code-name">&amp;#xe6eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ec;</span>
                <div class="name">单选备份</div>
                <div class="code-name">&amp;#xe6ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ed;</span>
                <div class="name">多选备份</div>
                <div class="code-name">&amp;#xe6ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">培训与交流-copy</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70f;</span>
                <div class="name">财务</div>
                <div class="code-name">&amp;#xe70f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">笔记本</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">财务 2</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe712;</span>
                <div class="name">飞机</div>
                <div class="code-name">&amp;#xe712;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">培训与交流</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe714;</span>
                <div class="name">汽车</div>
                <div class="code-name">&amp;#xe714;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe715;</span>
                <div class="name">补</div>
                <div class="code-name">&amp;#xe715;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe716;</span>
                <div class="name">加班</div>
                <div class="code-name">&amp;#xe716;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe717;</span>
                <div class="name">审批</div>
                <div class="code-name">&amp;#xe717;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe718;</span>
                <div class="name">公文包</div>
                <div class="code-name">&amp;#xe718;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">审批管理</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71a;</span>
                <div class="name">请假</div>
                <div class="code-name">&amp;#xe71a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71b;</span>
                <div class="name">消息</div>
                <div class="code-name">&amp;#xe71b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71c;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe71c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70a;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe70a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe70b;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe70b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71d;</span>
                <div class="name">删除-copy</div>
                <div class="code-name">&amp;#xe71d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6f4;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe6f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe670;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe670;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">工作信息</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e5;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe6e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe668;</span>
                <div class="name">未选中备份 13</div>
                <div class="code-name">&amp;#xe668;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">未选中备份 19</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66a;</span>
                <div class="name">未选中备份 15</div>
                <div class="code-name">&amp;#xe66a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e1;</span>
                <div class="name">增</div>
                <div class="code-name">&amp;#xe6e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e2;</span>
                <div class="name">减</div>
                <div class="code-name">&amp;#xe6e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e0;</span>
                <div class="name">首页-删除</div>
                <div class="code-name">&amp;#xe6e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6df;</span>
                <div class="name">移动</div>
                <div class="code-name">&amp;#xe6df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6de;</span>
                <div class="name">日程完成</div>
                <div class="code-name">&amp;#xe6de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe666;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe666;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe665;</span>
                <div class="name">编组 9</div>
                <div class="code-name">&amp;#xe665;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dd;</span>
                <div class="name">切换</div>
                <div class="code-name">&amp;#xe6dd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6dc;</span>
                <div class="name">排序</div>
                <div class="code-name">&amp;#xe6dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d2;</span>
                <div class="name">复制并创建</div>
                <div class="code-name">&amp;#xe6d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d8;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe6d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6db;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6bf;</span>
                <div class="name">标签</div>
                <div class="code-name">&amp;#xe6bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6da;</span>
                <div class="name">描述</div>
                <div class="code-name">&amp;#xe6da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cb;</span>
                <div class="name">Group 104备份</div>
                <div class="code-name">&amp;#xe6cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cc;</span>
                <div class="name">时间</div>
                <div class="code-name">&amp;#xe6cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cd;</span>
                <div class="name">参与人</div>
                <div class="code-name">&amp;#xe6cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ce;</span>
                <div class="name">地址</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6cf;</span>
                <div class="name">提醒</div>
                <div class="code-name">&amp;#xe6cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d0;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe6d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d1;</span>
                <div class="name">展开</div>
                <div class="code-name">&amp;#xe6d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d3;</span>
                <div class="name">未完成</div>
                <div class="code-name">&amp;#xe6d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d4;</span>
                <div class="name">我的</div>
                <div class="code-name">&amp;#xe6d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d5;</span>
                <div class="name">置顶</div>
                <div class="code-name">&amp;#xe6d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d6;</span>
                <div class="name">新建日程</div>
                <div class="code-name">&amp;#xe6d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d7;</span>
                <div class="name">展开2</div>
                <div class="code-name">&amp;#xe6d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6d9;</span>
                <div class="name">设置 2</div>
                <div class="code-name">&amp;#xe6d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">下载</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c7;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe6c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c5;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe6c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">形状</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe662;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe662;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe663;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe663;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe661;</span>
                <div class="name">outline备份</div>
                <div class="code-name">&amp;#xe661;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">路径 18</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe660;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe660;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65d;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe65d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe658;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe658;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe659;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe659;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">Shape1</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65b;</span>
                <div class="name">形状结合2</div>
                <div class="code-name">&amp;#xe65b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65c;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe65c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe656;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe656;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe657;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe657;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">Shape Copy 2</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe654;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe654;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe655;</span>
                <div class="name">Shape1</div>
                <div class="code-name">&amp;#xe655;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe650;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe650;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe651;</span>
                <div class="name">形状结合1</div>
                <div class="code-name">&amp;#xe651;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe652;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe652;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64f;</span>
                <div class="name">椭圆形</div>
                <div class="code-name">&amp;#xe64f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe64e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64d;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe64d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">Shape备份 3</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe644;</span>
                <div class="name">Combined Shape</div>
                <div class="code-name">&amp;#xe644;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">Combined Shape</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63d;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe63d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">Shape备份</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe638;</span>
                <div class="name">三角形备份 4</div>
                <div class="code-name">&amp;#xe638;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe637;</span>
                <div class="name">Rectangle 271备份 2</div>
                <div class="code-name">&amp;#xe637;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe633;</span>
                <div class="name">蒙版</div>
                <div class="code-name">&amp;#xe633;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe631;</span>
                <div class="name">蒙版</div>
                <div class="code-name">&amp;#xe631;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62f;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">Shape</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe629;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe629;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe628;</span>
                <div class="name">Fill 2</div>
                <div class="code-name">&amp;#xe628;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe624;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe624;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe622;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe622;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">形状结合</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot?t=1735545592134'); /* IE9 */
  src: url('iconfont.eot?t=1735545592134#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('data:application/x-font-woff2;charset=utf-8;base64,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**************************************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') format('woff2'),
       url('iconfont.woff?t=1735545592134') format('woff'),
       url('iconfont.ttf?t=1735545592134') format('truetype'),
       url('iconfont.svg?t=1735545592134#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-zhihuixiaoyuanhetongshenhe"></span>
            <div class="name">
              智慧校园合同审核
            </div>
            <div class="code-name">.icon-zhihuixiaoyuanhetongshenhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangmubaomingshenqing"></span>
            <div class="name">
              项目报名申请
            </div>
            <div class="code-name">.icon-xiangmubaomingshenqing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tushuhetongshenhe"></span>
            <div class="name">
              图书合同审核
            </div>
            <div class="code-name">.icon-tushuhetongshenhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingzhikaifahetongshenhe"></span>
            <div class="name">
              定制开发合同审核
            </div>
            <div class="code-name">.icon-dingzhikaifahetongshenhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoshuzhizuoshenqing1"></span>
            <div class="name">
              标书制作申请
            </div>
            <div class="code-name">.icon-biaoshuzhizuoshenqing1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peixun1"></span>
            <div class="name">
              培训
            </div>
            <div class="code-name">.icon-peixun1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pingtaizhanghaokaitong1"></span>
            <div class="name">
              平台账号开通
            </div>
            <div class="code-name">.icon-pingtaizhanghaokaitong1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingdingzhikaifa1"></span>
            <div class="name">
              申请定制开发
            </div>
            <div class="code-name">.icon-shenqingdingzhikaifa1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingfahuo1"></span>
            <div class="name">
              申请发货
            </div>
            <div class="code-name">.icon-shenqingfahuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingpeihuo1"></span>
            <div class="name">
              申请配货
            </div>
            <div class="code-name">.icon-shenqingpeihuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingxiangmubaojia1"></span>
            <div class="name">
              申请项目报价
            </div>
            <div class="code-name">.icon-shenqingxiangmubaojia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingxiangmufangan1"></span>
            <div class="name">
              申请项目方案
            </div>
            <div class="code-name">.icon-shenqingxiangmufangan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingyangji1"></span>
            <div class="name">
              申请样机
            </div>
            <div class="code-name">.icon-shenqingyangji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenqingzhizuotuzhi1"></span>
            <div class="name">
              申请制作图纸
            </div>
            <div class="code-name">.icon-shenqingzhizuotuzhi1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouhou1"></span>
            <div class="name">
              售后
            </div>
            <div class="code-name">.icon-shouhou1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuihuo1"></span>
            <div class="name">
              退货
            </div>
            <div class="code-name">.icon-tuihuo1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuikuanshenqingbiaodan1"></span>
            <div class="name">
              退款申请表单
            </div>
            <div class="code-name">.icon-tuikuanshenqingbiaodan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiangmubaobei1"></span>
            <div class="name">
              项目报备
            </div>
            <div class="code-name">.icon-xiangmubaobei1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiabaning"></span>
            <div class="name">
              加班
            </div>
            <div class="code-name">.icon-jiabaning
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bukataojian"></span>
            <div class="name">
              补卡套件
            </div>
            <div class="code-name">.icon-bukataojian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-waichu"></span>
            <div class="name">
              外出
            </div>
            <div class="code-name">.icon-waichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chucha1"></span>
            <div class="name">
              出差
            </div>
            <div class="code-name">.icon-chucha1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingjiataojianbeifen"></span>
            <div class="name">
              请假套件备份
            </div>
            <div class="code-name">.icon-qingjiataojianbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiehuan1"></span>
            <div class="name">
              切换 
            </div>
            <div class="code-name">.icon-qiehuan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuozhuai"></span>
            <div class="name">
              拖拽
            </div>
            <div class="code-name">.icon-tuozhuai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianzubeifen"></span>
            <div class="name">
              编组备份
            </div>
            <div class="code-name">.icon-bianzubeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Batchfolding"></span>
            <div class="name">
              Batch folding
            </div>
            <div class="code-name">.icon-a-Batchfolding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-duohangwenbenbeifen2"></span>
            <div class="name">
              多行文本备份 2
            </div>
            <div class="code-name">.icon-a-duohangwenbenbeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-danhangwenbenbeifen2"></span>
            <div class="name">
              单行文本备份 2
            </div>
            <div class="code-name">.icon-a-danhangwenbenbeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-danxuanbeifen"></span>
            <div class="name">
              单选备份
            </div>
            <div class="code-name">.icon-danxuanbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duoxuanbeifen"></span>
            <div class="name">
              多选备份
            </div>
            <div class="code-name">.icon-duoxuanbeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peixunyujiaoliu-copy"></span>
            <div class="name">
              培训与交流-copy
            </div>
            <div class="code-name">.icon-peixunyujiaoliu-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caiwu"></span>
            <div class="name">
              财务
            </div>
            <div class="code-name">.icon-caiwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bijiben"></span>
            <div class="name">
              笔记本
            </div>
            <div class="code-name">.icon-bijiben
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-caiwu2"></span>
            <div class="name">
              财务 2
            </div>
            <div class="code-name">.icon-a-caiwu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-feiji"></span>
            <div class="name">
              飞机
            </div>
            <div class="code-name">.icon-feiji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-peixunyujiaoliu"></span>
            <div class="name">
              培训与交流
            </div>
            <div class="code-name">.icon-peixunyujiaoliu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiche"></span>
            <div class="name">
              汽车
            </div>
            <div class="code-name">.icon-qiche
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bu"></span>
            <div class="name">
              补
            </div>
            <div class="code-name">.icon-bu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiaban1"></span>
            <div class="name">
              加班
            </div>
            <div class="code-name">.icon-jiaban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenpi"></span>
            <div class="name">
              审批
            </div>
            <div class="code-name">.icon-shenpi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongwenbao"></span>
            <div class="name">
              公文包
            </div>
            <div class="code-name">.icon-gongwenbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shenpiguanli"></span>
            <div class="name">
              审批管理
            </div>
            <div class="code-name">.icon-shenpiguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qingjia"></span>
            <div class="name">
              请假
            </div>
            <div class="code-name">.icon-qingjia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiaoxi"></span>
            <div class="name">
              消息
            </div>
            <div class="code-name">.icon-xiaoxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili1"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu1"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu2"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu2-copy"></span>
            <div class="name">
              删除-copy
            </div>
            <div class="code-name">.icon-shanchu2-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.icon-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji2"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongzuoxinxi"></span>
            <div class="name">
              工作信息
            </div>
            <div class="code-name">.icon-gongzuoxinxi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.icon-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen13"></span>
            <div class="name">
              未选中备份 13
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen13
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen19-copy"></span>
            <div class="name">
              未选中备份 19
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen19-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-weixuanzhongbeifen151"></span>
            <div class="name">
              未选中备份 15
            </div>
            <div class="code-name">.icon-a-weixuanzhongbeifen151
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zeng"></span>
            <div class="name">
              增
            </div>
            <div class="code-name">.icon-zeng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jian"></span>
            <div class="name">
              减
            </div>
            <div class="code-name">.icon-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-shanchu"></span>
            <div class="name">
              首页-删除
            </div>
            <div class="code-name">.icon-shouye-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye-yidong"></span>
            <div class="name">
              移动
            </div>
            <div class="code-name">.icon-shouye-yidong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-richengwancheng"></span>
            <div class="name">
              日程完成
            </div>
            <div class="code-name">.icon-richengwancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape6"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-bianzu9"></span>
            <div class="name">
              编组 9
            </div>
            <div class="code-name">.icon-a-bianzu9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-qiehuan"></span>
            <div class="name">
              切换
            </div>
            <div class="code-name">.icon-qiehuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paixu"></span>
            <div class="name">
              排序
            </div>
            <div class="code-name">.icon-paixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fuzhibingchuangjian"></span>
            <div class="name">
              复制并创建
            </div>
            <div class="code-name">.icon-fuzhibingchuangjian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu1"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji1"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-biaoqian1"></span>
            <div class="name">
              标签
            </div>
            <div class="code-name">.icon-biaoqian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-miaoshu"></span>
            <div class="name">
              描述
            </div>
            <div class="code-name">.icon-miaoshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Group104beifen"></span>
            <div class="name">
              Group 104备份
            </div>
            <div class="code-name">.icon-a-Group104beifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shijian"></span>
            <div class="name">
              时间
            </div>
            <div class="code-name">.icon-shijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-canyuren"></span>
            <div class="name">
              参与人
            </div>
            <div class="code-name">.icon-canyuren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dizhi"></span>
            <div class="name">
              地址
            </div>
            <div class="code-name">.icon-dizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tixing"></span>
            <div class="name">
              提醒
            </div>
            <div class="code-name">.icon-tixing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai"></span>
            <div class="name">
              展开
            </div>
            <div class="code-name">.icon-zhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weiwancheng"></span>
            <div class="name">
              未完成
            </div>
            <div class="code-name">.icon-weiwancheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wode"></span>
            <div class="name">
              我的
            </div>
            <div class="code-name">.icon-wode
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhiding"></span>
            <div class="name">
              置顶
            </div>
            <div class="code-name">.icon-zhiding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xinjianricheng"></span>
            <div class="name">
              新建日程
            </div>
            <div class="code-name">.icon-xinjianricheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhankai2"></span>
            <div class="name">
              展开2
            </div>
            <div class="code-name">.icon-zhankai2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-shezhi2"></span>
            <div class="name">
              设置 2
            </div>
            <div class="code-name">.icon-a-shezhi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xiazai"></span>
            <div class="name">
              下载
            </div>
            <div class="code-name">.icon-xiazai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.icon-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuang"></span>
            <div class="name">
              形状
            </div>
            <div class="code-name">.icon-xingzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe27"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe27
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe113"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe113
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-outlinebeifen"></span>
            <div class="name">
              outline备份
            </div>
            <div class="code-name">.icon-outlinebeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-lujing18"></span>
            <div class="name">
              路径 18
            </div>
            <div class="code-name">.icon-a-lujing18
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe26"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe26
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen3"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape5"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe24"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe24
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe112"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe112
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape12"></span>
            <div class="name">
              Shape1
            </div>
            <div class="code-name">.icon-Shape12
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe25"></span>
            <div class="name">
              形状结合2
            </div>
            <div class="code-name">.icon-xingzhuangjiehe25
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape4"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe111"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe111
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe23"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe23
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-ShapeCopy2"></span>
            <div class="name">
              Shape Copy 2
            </div>
            <div class="code-name">.icon-a-ShapeCopy2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape3"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape11"></span>
            <div class="name">
              Shape1
            </div>
            <div class="code-name">.icon-Shape11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe22"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe22
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe110"></span>
            <div class="name">
              形状结合1
            </div>
            <div class="code-name">.icon-xingzhuangjiehe110
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill22"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill22
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tuoyuanxing"></span>
            <div class="name">
              椭圆形
            </div>
            <div class="code-name">.icon-tuoyuanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe21"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe20"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe20
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Shapebeifen3"></span>
            <div class="name">
              Shape备份 3
            </div>
            <div class="code-name">.icon-a-Shapebeifen3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape2"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe19"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe19
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-CombinedShape1"></span>
            <div class="name">
              Combined Shape
            </div>
            <div class="code-name">.icon-a-CombinedShape1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-CombinedShape"></span>
            <div class="name">
              Combined Shape
            </div>
            <div class="code-name">.icon-a-CombinedShape
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe18"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe18
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe17"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe17
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape1"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe16"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe16
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen2"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen1"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shapebeifen"></span>
            <div class="name">
              Shape备份
            </div>
            <div class="code-name">.icon-Shapebeifen
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-sanjiaoxingbeifen4"></span>
            <div class="name">
              三角形备份 4
            </div>
            <div class="code-name">.icon-a-sanjiaoxingbeifen4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Rectangle271beifen2"></span>
            <div class="name">
              Rectangle 271备份 2
            </div>
            <div class="code-name">.icon-a-Rectangle271beifen2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe15"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe15
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe14"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe14
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mengban1"></span>
            <div class="name">
              蒙版
            </div>
            <div class="code-name">.icon-mengban1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mengban"></span>
            <div class="name">
              蒙版
            </div>
            <div class="code-name">.icon-mengban
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe13"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe13
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe12"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe12
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe11"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe11
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe10"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe10
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Shape"></span>
            <div class="name">
              Shape
            </div>
            <div class="code-name">.icon-Shape
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill21"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill21
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-Fill2"></span>
            <div class="name">
              Fill 2
            </div>
            <div class="code-name">.icon-a-Fill2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe9"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe9
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe8"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe8
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe7"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe6"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe6
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe5"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe4"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe3"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe2"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe1"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xingzhuangjiehe"></span>
            <div class="name">
              形状结合
            </div>
            <div class="code-name">.icon-xingzhuangjiehe
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhihuixiaoyuanhetongshenhe"></use>
                </svg>
                <div class="name">智慧校园合同审核</div>
                <div class="code-name">#icon-zhihuixiaoyuanhetongshenhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangmubaomingshenqing"></use>
                </svg>
                <div class="name">项目报名申请</div>
                <div class="code-name">#icon-xiangmubaomingshenqing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tushuhetongshenhe"></use>
                </svg>
                <div class="name">图书合同审核</div>
                <div class="code-name">#icon-tushuhetongshenhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingzhikaifahetongshenhe"></use>
                </svg>
                <div class="name">定制开发合同审核</div>
                <div class="code-name">#icon-dingzhikaifahetongshenhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoshuzhizuoshenqing1"></use>
                </svg>
                <div class="name">标书制作申请</div>
                <div class="code-name">#icon-biaoshuzhizuoshenqing1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peixun1"></use>
                </svg>
                <div class="name">培训</div>
                <div class="code-name">#icon-peixun1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pingtaizhanghaokaitong1"></use>
                </svg>
                <div class="name">平台账号开通</div>
                <div class="code-name">#icon-pingtaizhanghaokaitong1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingdingzhikaifa1"></use>
                </svg>
                <div class="name">申请定制开发</div>
                <div class="code-name">#icon-shenqingdingzhikaifa1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingfahuo1"></use>
                </svg>
                <div class="name">申请发货</div>
                <div class="code-name">#icon-shenqingfahuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingpeihuo1"></use>
                </svg>
                <div class="name">申请配货</div>
                <div class="code-name">#icon-shenqingpeihuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingxiangmubaojia1"></use>
                </svg>
                <div class="name">申请项目报价</div>
                <div class="code-name">#icon-shenqingxiangmubaojia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingxiangmufangan1"></use>
                </svg>
                <div class="name">申请项目方案</div>
                <div class="code-name">#icon-shenqingxiangmufangan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingyangji1"></use>
                </svg>
                <div class="name">申请样机</div>
                <div class="code-name">#icon-shenqingyangji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenqingzhizuotuzhi1"></use>
                </svg>
                <div class="name">申请制作图纸</div>
                <div class="code-name">#icon-shenqingzhizuotuzhi1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouhou1"></use>
                </svg>
                <div class="name">售后</div>
                <div class="code-name">#icon-shouhou1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuihuo1"></use>
                </svg>
                <div class="name">退货</div>
                <div class="code-name">#icon-tuihuo1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuikuanshenqingbiaodan1"></use>
                </svg>
                <div class="name">退款申请表单</div>
                <div class="code-name">#icon-tuikuanshenqingbiaodan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiangmubaobei1"></use>
                </svg>
                <div class="name">项目报备</div>
                <div class="code-name">#icon-xiangmubaobei1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiabaning"></use>
                </svg>
                <div class="name">加班</div>
                <div class="code-name">#icon-jiabaning</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bukataojian"></use>
                </svg>
                <div class="name">补卡套件</div>
                <div class="code-name">#icon-bukataojian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-waichu"></use>
                </svg>
                <div class="name">外出</div>
                <div class="code-name">#icon-waichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chucha1"></use>
                </svg>
                <div class="name">出差</div>
                <div class="code-name">#icon-chucha1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingjiataojianbeifen"></use>
                </svg>
                <div class="name">请假套件备份</div>
                <div class="code-name">#icon-qingjiataojianbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiehuan1"></use>
                </svg>
                <div class="name">切换 </div>
                <div class="code-name">#icon-qiehuan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuozhuai"></use>
                </svg>
                <div class="name">拖拽</div>
                <div class="code-name">#icon-tuozhuai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianzubeifen"></use>
                </svg>
                <div class="name">编组备份</div>
                <div class="code-name">#icon-bianzubeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Batchfolding"></use>
                </svg>
                <div class="name">Batch folding</div>
                <div class="code-name">#icon-a-Batchfolding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-duohangwenbenbeifen2"></use>
                </svg>
                <div class="name">多行文本备份 2</div>
                <div class="code-name">#icon-a-duohangwenbenbeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-danhangwenbenbeifen2"></use>
                </svg>
                <div class="name">单行文本备份 2</div>
                <div class="code-name">#icon-a-danhangwenbenbeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-danxuanbeifen"></use>
                </svg>
                <div class="name">单选备份</div>
                <div class="code-name">#icon-danxuanbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duoxuanbeifen"></use>
                </svg>
                <div class="name">多选备份</div>
                <div class="code-name">#icon-duoxuanbeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peixunyujiaoliu-copy"></use>
                </svg>
                <div class="name">培训与交流-copy</div>
                <div class="code-name">#icon-peixunyujiaoliu-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caiwu"></use>
                </svg>
                <div class="name">财务</div>
                <div class="code-name">#icon-caiwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bijiben"></use>
                </svg>
                <div class="name">笔记本</div>
                <div class="code-name">#icon-bijiben</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-caiwu2"></use>
                </svg>
                <div class="name">财务 2</div>
                <div class="code-name">#icon-a-caiwu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-feiji"></use>
                </svg>
                <div class="name">飞机</div>
                <div class="code-name">#icon-feiji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-peixunyujiaoliu"></use>
                </svg>
                <div class="name">培训与交流</div>
                <div class="code-name">#icon-peixunyujiaoliu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiche"></use>
                </svg>
                <div class="name">汽车</div>
                <div class="code-name">#icon-qiche</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bu"></use>
                </svg>
                <div class="name">补</div>
                <div class="code-name">#icon-bu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiaban1"></use>
                </svg>
                <div class="name">加班</div>
                <div class="code-name">#icon-jiaban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenpi"></use>
                </svg>
                <div class="name">审批</div>
                <div class="code-name">#icon-shenpi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongwenbao"></use>
                </svg>
                <div class="name">公文包</div>
                <div class="code-name">#icon-gongwenbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shenpiguanli"></use>
                </svg>
                <div class="name">审批管理</div>
                <div class="code-name">#icon-shenpiguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qingjia"></use>
                </svg>
                <div class="name">请假</div>
                <div class="code-name">#icon-qingjia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiaoxi"></use>
                </svg>
                <div class="name">消息</div>
                <div class="code-name">#icon-xiaoxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili1"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu1"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu2"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu2-copy"></use>
                </svg>
                <div class="name">删除-copy</div>
                <div class="code-name">#icon-shanchu2-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#icon-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji2"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongzuoxinxi"></use>
                </svg>
                <div class="name">工作信息</div>
                <div class="code-name">#icon-gongzuoxinxi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#icon-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen13"></use>
                </svg>
                <div class="name">未选中备份 13</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen13</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen19-copy"></use>
                </svg>
                <div class="name">未选中备份 19</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen19-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-weixuanzhongbeifen151"></use>
                </svg>
                <div class="name">未选中备份 15</div>
                <div class="code-name">#icon-a-weixuanzhongbeifen151</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zeng"></use>
                </svg>
                <div class="name">增</div>
                <div class="code-name">#icon-zeng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jian"></use>
                </svg>
                <div class="name">减</div>
                <div class="code-name">#icon-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-shanchu"></use>
                </svg>
                <div class="name">首页-删除</div>
                <div class="code-name">#icon-shouye-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye-yidong"></use>
                </svg>
                <div class="name">移动</div>
                <div class="code-name">#icon-shouye-yidong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-richengwancheng"></use>
                </svg>
                <div class="name">日程完成</div>
                <div class="code-name">#icon-richengwancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape6"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-bianzu9"></use>
                </svg>
                <div class="name">编组 9</div>
                <div class="code-name">#icon-a-bianzu9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-qiehuan"></use>
                </svg>
                <div class="name">切换</div>
                <div class="code-name">#icon-qiehuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paixu"></use>
                </svg>
                <div class="name">排序</div>
                <div class="code-name">#icon-paixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fuzhibingchuangjian"></use>
                </svg>
                <div class="name">复制并创建</div>
                <div class="code-name">#icon-fuzhibingchuangjian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu1"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji1"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-biaoqian1"></use>
                </svg>
                <div class="name">标签</div>
                <div class="code-name">#icon-biaoqian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-miaoshu"></use>
                </svg>
                <div class="name">描述</div>
                <div class="code-name">#icon-miaoshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Group104beifen"></use>
                </svg>
                <div class="name">Group 104备份</div>
                <div class="code-name">#icon-a-Group104beifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shijian"></use>
                </svg>
                <div class="name">时间</div>
                <div class="code-name">#icon-shijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-canyuren"></use>
                </svg>
                <div class="name">参与人</div>
                <div class="code-name">#icon-canyuren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dizhi"></use>
                </svg>
                <div class="name">地址</div>
                <div class="code-name">#icon-dizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tixing"></use>
                </svg>
                <div class="name">提醒</div>
                <div class="code-name">#icon-tixing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai"></use>
                </svg>
                <div class="name">展开</div>
                <div class="code-name">#icon-zhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weiwancheng"></use>
                </svg>
                <div class="name">未完成</div>
                <div class="code-name">#icon-weiwancheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wode"></use>
                </svg>
                <div class="name">我的</div>
                <div class="code-name">#icon-wode</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhiding"></use>
                </svg>
                <div class="name">置顶</div>
                <div class="code-name">#icon-zhiding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xinjianricheng"></use>
                </svg>
                <div class="name">新建日程</div>
                <div class="code-name">#icon-xinjianricheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhankai2"></use>
                </svg>
                <div class="name">展开2</div>
                <div class="code-name">#icon-zhankai2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-shezhi2"></use>
                </svg>
                <div class="name">设置 2</div>
                <div class="code-name">#icon-a-shezhi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xiazai"></use>
                </svg>
                <div class="name">下载</div>
                <div class="code-name">#icon-xiazai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#icon-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuang"></use>
                </svg>
                <div class="name">形状</div>
                <div class="code-name">#icon-xingzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe27"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe27</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe113"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe113</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-outlinebeifen"></use>
                </svg>
                <div class="name">outline备份</div>
                <div class="code-name">#icon-outlinebeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-lujing18"></use>
                </svg>
                <div class="name">路径 18</div>
                <div class="code-name">#icon-a-lujing18</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe26"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe26</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen3"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape5"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe24"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe24</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe112"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe112</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape12"></use>
                </svg>
                <div class="name">Shape1</div>
                <div class="code-name">#icon-Shape12</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe25"></use>
                </svg>
                <div class="name">形状结合2</div>
                <div class="code-name">#icon-xingzhuangjiehe25</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape4"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe111"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe111</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe23"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe23</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-ShapeCopy2"></use>
                </svg>
                <div class="name">Shape Copy 2</div>
                <div class="code-name">#icon-a-ShapeCopy2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape3"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape11"></use>
                </svg>
                <div class="name">Shape1</div>
                <div class="code-name">#icon-Shape11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe22"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe22</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe110"></use>
                </svg>
                <div class="name">形状结合1</div>
                <div class="code-name">#icon-xingzhuangjiehe110</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill22"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill22</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuoyuanxing"></use>
                </svg>
                <div class="name">椭圆形</div>
                <div class="code-name">#icon-tuoyuanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe21"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe20"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe20</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Shapebeifen3"></use>
                </svg>
                <div class="name">Shape备份 3</div>
                <div class="code-name">#icon-a-Shapebeifen3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape2"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe19"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe19</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-CombinedShape1"></use>
                </svg>
                <div class="name">Combined Shape</div>
                <div class="code-name">#icon-a-CombinedShape1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-CombinedShape"></use>
                </svg>
                <div class="name">Combined Shape</div>
                <div class="code-name">#icon-a-CombinedShape</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe18"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe18</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe17"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe17</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape1"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe16"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe16</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen2"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen1"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shapebeifen"></use>
                </svg>
                <div class="name">Shape备份</div>
                <div class="code-name">#icon-Shapebeifen</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-sanjiaoxingbeifen4"></use>
                </svg>
                <div class="name">三角形备份 4</div>
                <div class="code-name">#icon-a-sanjiaoxingbeifen4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Rectangle271beifen2"></use>
                </svg>
                <div class="name">Rectangle 271备份 2</div>
                <div class="code-name">#icon-a-Rectangle271beifen2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe15"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe15</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe14"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe14</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mengban1"></use>
                </svg>
                <div class="name">蒙版</div>
                <div class="code-name">#icon-mengban1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mengban"></use>
                </svg>
                <div class="name">蒙版</div>
                <div class="code-name">#icon-mengban</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe13"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe13</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe12"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe12</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe11"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe11</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe10"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe10</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Shape"></use>
                </svg>
                <div class="name">Shape</div>
                <div class="code-name">#icon-Shape</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill21"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill21</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-Fill2"></use>
                </svg>
                <div class="name">Fill 2</div>
                <div class="code-name">#icon-a-Fill2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe9"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe9</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe8"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe8</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe7"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe6"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe6</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe5"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe4"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe3"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe2"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe1"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xingzhuangjiehe"></use>
                </svg>
                <div class="name">形状结合</div>
                <div class="code-name">#icon-xingzhuangjiehe</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>

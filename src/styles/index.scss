/* 一行显示省略号 */
.ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

/* 两行显示省略号 */
.two_ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

button:after {
    /* 按钮有个边框 */
    border: none !important;
}

.uni-page-head {
    align-items: center;
}

.uni-page-head__title {
    font-weight: 500 !important;
    font-size: 34rpx !important;
    color: $uni-text-color !important;
    line-height: 48rpx;
    text-align: center;
}

.uni-nav-bar-text {
    font-weight: 500;
    font-size: 32rpx !important;
    color: $uni-text-color !important;
    line-height: 48rpx;
    text-align: center;
}

.uni-picker-action-confirm {
    color: $uni-color-primary !important;
}

/* #ifdef MP-WEIXIN */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}

/* #endif */

uni-button[disabled*="true"] {
    background: #ccc !important;
}

button[aria-disabled*="true"] {
    background: #ccc !important;
}


// @media (min-width: 520px) {
//     body{
//         background: #f8f7fb;
//     }
//     uni-page {
// 		width: 414px !important; 
// 		overflow-y: auto;
// 		min-width: 300px !important;
//         margin: 0 auto;
//         box-shadow: #999 0 0 12px;
//         background-color: #fff;    
// 	}
// 	uni-page-body {
// 		width: 414px !important; 
// 	}    
// }

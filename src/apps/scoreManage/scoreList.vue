<template>
    <view class="score-list">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="成绩表"> </uni-nav-bar>
        <view class="reset-select">
            <view class="reset-select-item">
                <uni-data-picker placeholder="请选择班级" popup-title="选择班级" :map="{ text: 'classesName', value: 'classesId' }" :localdata="state.classList" :clear-icon="false" v-model="state.classesId" @change="changeSelect"> </uni-data-picker>
            </view>
            <view class="reset-select-item type"> </view>
        </view>

        <scroll-view class="scroll-Y" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom" v-if="state.scoreList && state.scoreList.length">
            <view class="score-list-item" v-for="item in state.scoreList" :key="item" @click="handlerToDetails(item)">
                <view class="userName"
                    ><span class="adaptive_hiding user">{{ item.studentName }}</span
                    ><span class="adaptive_hiding clazz">（{{ item.classesName }}）</span>
                </view>
                <view class="item" v-if="item.totalScore">{{ item.totalScore }}分</view>
                <view class="item" v-if="item.totalRank">第{{ item.totalRank }}名<uni-icons type="right" size="14" color="#333"></uni-icons></view>
            </view>
            <uni-load-more :showText="state.loadMore.showText" :contentText="state.loadMore.contentText" :status="state.loadMore.status" />
        </scroll-view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>

<script setup name="scoreList">
import { onLoad } from "@dcloudio/uni-app"

const state = reactive({
    classesId: 0,
    loadMore: {
        status: "more", // more	加载前, loading	加载中, no-more	没有更多数据
        showText: true, // 显示文本
        contentText: {
            contentdown: "点击加载更多"
        }
    },
    classList: [],
    scoreList: [],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    socleParams: {}
})

const getClassListInfo = () => {
    http.get("/app/score/exam/getClassesList", { id: state.socleParams?.examId }).then(({ data }) => {
        state.classList = [{ classesId: 0, classesName: "全部班级" }, ...data]
    })
}
const init = async () => {
    const params = {
        ...state.pagination,
        examId: state.socleParams?.examId || "",
        classesId: state.classesId || ""
    }
    await http.post("/app/score/exam/employeeDetailPage", params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.pagination.pageNo = pageNo
        state.pagination.pageSize = pageSize
        state.pagination.total = total
        if (list.length && state.scoreList.length) {
            state.scoreList = state.scoreList.concat(list)
        } else {
            state.scoreList = list
        }
        if (state.pagination.pageNo * pageSize >= total) {
            state.loadMore.status = "no-more"
        } else {
            state.loadMore.status = "more"
        }
    })
}
// 跳详情
const handlerToDetails = (item) => {
    const { scoreDetailList = [], studentName = "", examName = "" } = item
    const params = {
        studentName,
        examName,
        scoreDetailList: JSON.stringify(scoreDetailList)
    }
    navigateTo({
        url: "/apps/scoreManage/scoreDetails",
        query: params
    })
}
// 下拉查询
const changeSelect = () => {
    state.scoreList = []
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    init()
}
// 滚动加载
const handlerScrollBottom = (e) => {
    const { pageNo, pageSize, total } = state.pagination
    if (pageNo * pageSize <= total) {
        state.loadMore.status = "loading"
        state.pagination.pageNo++
        init()
    }
}

onPullDownRefresh(async () => {
    state.scoreList = []
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.loadMore.status = "loading"
    state.classesId = 0
    await init()
    uni.stopPullDownRefresh()
})

onLoad(async (item) => {
    state.socleParams = item
    init()
    getClassListInfo()
})
const clickLeft = () => {
    uni.navigateBack()
}
</script>

<style scoped lang="scss">
.score-list {
    background-color: $uni-bg-color-grey;
    min-height: 100vh;
    .scroll-Y {
        height: calc(100vh - 152rpx);
        overflow: hidden auto;

        .score-list-item {
            margin: 20rpx 30rpx;
            padding: 30rpx;
            background-color: $uni-bg-color;
            border-radius: 50rpx;
            display: flex;
            justify-content: space-between;

            .userName {
                display: flex;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                .adaptive_hiding {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;

                    &.user {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }

                    &.clazz {
                        flex: 1;
                        color: $uni-text-color-grey;
                        font-weight: 400;
                        font-size: 28rpx;
                        line-height: 40rpx;
                    }
                }
            }

            .item {
                min-width: 130rpx;
                text-align: center;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }
        }
    }

    .reset-select {
        display: flex;
        justify-content: space-between;
        padding: 0 20rpx 0;
        background-color: $uni-bg-color;

        .reset-select-item {
            :deep(.uni-data-tree) {
                .input-value-border {
                    border: none;
                }

                .arrow-area {
                    transform: rotate(0deg);

                    &:before {
                        content: "";
                        display: block;
                        border: 10rpx solid transparent;
                        margin-left: 6rpx;
                        border-top: 10rpx solid $uni-color-primary;
                        border-bottom-width: 1px;
                        margin-top: 6rpx;
                    }

                    .input-arrow {
                        display: none;
                    }
                }
            }

            &.type {
                :deep(.uni-select__input-box) {
                    justify-content: end;
                }
            }
        }
    }

    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;
    }
}
</style>

<template>
    <view class="achievement">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="成绩管理"> </uni-nav-bar>
        <view class="reset-select">
            <view class="reset-select-left">
                <view class="reset-select-item">
                    <uni-data-select v-model="state.examTimeType" :localdata="examTimeType" @change="changeSelectTimeType" :clear="false"></uni-data-select>
                </view>
                <view class="reset-select-item type">
                    <uni-data-select v-model="state.type" :localdata="examType" @change="changeSelectTyp" :clear="false" placeholder="考试类型"></uni-data-select>
                </view>
            </view>

            <view class="reset-select-item" @click="handlerSwitchStudents">
                <view class="user">
                    <span class="adaptive_hiding">{{ state.studentName }}</span>
                    <image class="switchSons-icon" src="@nginx/components/select_child.png" />
                </view>
            </view>
        </view>
        <scroll-view class="scroll-Y" :scrollTop="state.scrollTop" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom" v-if="state.examBasicList && state.examBasicList.length">
            <view class="achievement-item" v-for="item in state.examBasicList" :key="item">
                <view class="title">
                    <text>{{ item.name }}</text>
                </view>
                <view class="content">
                    <ul>
                        <li class="list-item" v-for="it in state.examBasicInfo" :key="it.key" @click="handlerToDetails(item)">
                            <span class="label">{{ it.label }}</span>
                            <span v-if="it.key == 'isImport'">{{ item[it.key] ? "已导入" : "未导入" }}</span>
                            <span class="value" v-else>{{ item[it.key] }}</span>
                        </li>
                    </ul>
                </view>
            </view>
            <uni-load-more :showText="state.loadMore.showText" :contentText="state.loadMore.contentText" :status="state.loadMore.status" @clickLoadMore="state.scrollTop = 0" />
        </scroll-view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
        <yd-select-popup :fieldNames="{ value: 'studentId', label: 'studentName' }" ref="selectPopupRef" title="请选择学生" :list="state.selectList" @closePopup="closePopup" :selectId="[state.studentId]"></yd-select-popup>
    </view>
</template>

<script setup name="scoreManage">
import useStore from "@/store"
const { user } = useStore()

const selectPopupRef = ref()
const handlerSwitchStudents = () => {
    selectPopupRef.value.open()
}

const state = reactive({
    examBasicInfo: [
        { key: "examTime", label: "考试日期" },
        { key: "typeName", label: "考试类型" },
        { key: "subjectName", label: "考试科目" },
        { key: "isImport", label: "成绩导入" },
        { key: "createTime", label: "创建时间" }
    ],
    examBasicList: [],
    examTimeType: 0,
    type: 0,
    subjectName: "",
    loadMore: {
        status: "more", // more	加载前, loading	加载中, no-more	没有更多数据
        showText: true, // 显示文本
        contentText: {
            contentdown: "点击加载更多"
        }
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    scrollTop: 0,
    studentId: "",
    studentName: "",
    selectList: []
})
const examTimeType = [
    { value: 0, text: "全部时间" },
    { value: 1, text: "本学期" }
]
const examType = [
    { value: 0, text: "全部类型" },
    { value: 1, text: "期末统考 " },
    { value: 2, text: "期中统考" },
    { value: 3, text: "学科小考" }
]

const init = async () => {
    const { pagination, examTimeType, type, studentId } = state
    const params = {
        ...pagination,
        studentId,
        examTimeType,
        type: type || null
    }
    await http.post("/app/score/exam/elternPage", params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        state.pagination.pageNo = pageNo
        state.pagination.pageSize = pageSize
        state.pagination.total = total
        if (state.examBasicList.length) {
            state.examBasicList = state.examBasicList.concat(list)
        } else {
            state.examBasicList = list
        }
        if (pageNo * pageSize >= total) {
            state.loadMore.status = "no-more"
        } else {
            state.loadMore.status = "more"
        }
    })
}
// 跳详情
const handlerToDetails = (item) => {
    // * identity 0 是学生  1是教职工  2是家长
    // 成绩详情
    const { scoreDetailList = [], examName = "" } = item
    const params = {
        studentName: state.studentName,
        examName,
        scoreDetailList: JSON.stringify(scoreDetailList)
    }
    navigateTo({
        url: "/apps/scoreManage/scoreDetails",
        query: params
    })
}
// 下拉查询
const changeSelectTimeType = () => {
    state.pagination.pageNo = 1
    state.examBasicList = []
    state.scrollTop = 0
    init()
}

const changeSelectTyp = () => {
    state.pagination.pageNo = 1
    state.examBasicList = []
    state.scrollTop = 0
    init()
}
// 滚动加载
const handlerScrollBottom = (e) => {
    state.scrollTop = e.target.offsetTop
    const { pageNo, pageSize, total } = state.pagination
    if (pageNo * pageSize <= total) {
        state.loadMore.status = "loading"
        state.pagination.pageNo++
        init()
    }
}

// 切换孩子
const closePopup = (val) => {
    if (!val) return
    state.scrollTop = 0
    state.examBasicList = []
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.loadMore.status = "loading"
    state.examTimeType = 0
    state.type = 0
    state.studentName = val.studentName
    state.studentId = val.studentId
    init()
}

onPullDownRefresh(async () => {
    state.scrollTop = 0
    state.examBasicList = []
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.loadMore.status = "loading"
    state.examTimeType = 0
    state.type = 0
    await init()
    uni.stopPullDownRefresh()
})

onMounted(async () => {
    state.selectList = user.studentInfo
    state.studentName = user.studentInfo[0]?.studentName
    state.studentId = user.studentInfo[0]?.studentId
    init()
})
</script>

<style scoped lang="scss">
.achievement {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;

    .reset-select {
        display: flex;
        justify-content: space-between;
        padding: 0 20rpx 20rpx;
        background-color: $uni-bg-color;

        .reset-select-left {
            display: flex;
            flex: 1;
        }

        .reset-select-item {
            width: 204rpx;

            :deep(.uni-select__input-text) {
                font-size: 28rpx;
                color: #666666;
            }

            :deep(.uni-select) {
                border: none;
                height: 20rpx;
                margin-top: 20rpx;

                .uni-select__input-text {
                    width: auto;
                }

                .uni-icons:before {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    margin-left: 6rpx;
                }

                .uniui-bottom:before {
                    border-top: 10rpx solid $uni-color-primary;
                    border-bottom-width: 1px;
                    margin-top: 6rpx;
                }

                .uniui-top:before {
                    border-bottom-color: $uni-color-primary;
                    border-top-width: 1px;
                    margin-bottom: 6rpx;
                }
            }

            &.type {
                :deep(.uni-select__input-box) {
                    justify-content: end;
                }
            }

            .user {
                font-size: 28rpx;
                padding-top: 12rpx;
                text-align: right;
                color: #666666;

                .switchSons-icon {
                    width: 40rpx;
                    height: 40rpx;
                }

                // 一行自适应隐藏
                .adaptive_hiding {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 134rpx;
                    display: inline-block;
                }
            }
        }
    }

    .scroll-Y {
        height: calc(100vh - 152rpx);
        overflow: hidden auto;

        .achievement-item {
            margin: 20rpx 30rpx;
            padding: 0 30rpx;
            background-color: $uni-bg-color;
            border-radius: 20rpx;

            .title {
                border-bottom: 1rpx solid $uni-border-color;
                padding: 30rpx 0;
                font-weight: 600;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }

            .content {
                padding: 12rpx 0;

                ul {
                    padding: 0;

                    .list-item {
                        display: flex;
                        justify-content: space-between;
                        padding: 12rpx 0;

                        .label {
                            color: $uni-text-color-grey;
                            font-weight: 400;
                            font-size: 28rpx;
                            line-height: 40rpx;
                            min-width: 140rpx;
                        }

                        .value {
                            text-align: right;
                            padding-left: 12rpx;
                            font-weight: 400;
                            font-size: 28rpx;
                            color: #262626;
                            line-height: 40rpx;
                        }
                    }
                }
            }
        }
    }

    .popup-children {
        background-color: $uni-bg-color;
        height: 400rpx;
        position: relative;
        border-radius: 20rpx 20rpx 0 0;

        .title {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
        }

        .popup-close {
            position: absolute;
            top: 10rpx;
            right: 30rpx;
        }

        .uni-list-item {
            display: flex;
            justify-content: space-between;
            padding: 20rpx;
            .active {
                :deep(.uni-radio-input) {
                    background-color: $uni-color-primary !important;
                    border-color: $uni-color-primary !important;
                }
            }
        }
    }
}
</style>

<template>
    <view class="score-details">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="成绩详情"> </uni-nav-bar>
        <view class="empty-tag"></view>
        <view class="score-details-content">
            <view class="title">
                <text>{{ state.details.examName }}</text>
            </view>
            <uni-list class="reset-uni-list" v-if="state.details.scoreDetailList && state.details.scoreDetailList.length > 0">
                <uni-list-item title="姓名" :rightText="state.details.studentName"></uni-list-item>
                <uni-list-item v-for="item in state.details.scoreDetailList" :key="item.subjectId" :title="item.subjectName" :rightText="item.score"></uni-list-item>
            </uni-list>
            <yd-empty text="暂无数据" :isMargin="true" v-else />
        </view>
    </view>
</template>

<script setup name="scoreList">
import { onLoad } from "@dcloudio/uni-app"
const state = reactive({
    details: {
        examName: "",
        studentName: "",
        scoreDetailList: []
    },
    socleParams: {},
    back: ""
})

const handlerDetailsInfo = (item) => {
    const { examId, studentId, back } = JSON.parse(item.customParams)
    const params = {
        examId,
        studentId
    }
    http.post("/app/score/exam/elternDetail", params).then(({ data }) => {
        state.details = data
        state.details.examName = data.name
    })
}

onLoad(async (item) => {
    if (item.customParams) {
        state.back = `/${item.back}`
        handlerDetailsInfo(item)
    } else {
        state.details = item
        if (item.scoreDetailList) {
            state.details.scoreDetailList = JSON.parse(item.scoreDetailList)
        }
    }
})

const clickLeft = () => {
    uni.navigateBack()
}
</script>

<style scoped lang="scss">
.score-details {
    .empty-tag {
        height: 20rpx;
        background-color: $uni-bg-color-grey;
    }

    .score-details-content {
        background-color: $uni-bg-color;
        padding: 30rpx;
    }

    .title {
        font-size: 30rpx;
        font-weight: 600;
    }

    .reset-uni-list {
        :deep(.uni-list--border-top) {
            display: none;
        }

        :deep(.uni-list-item) {
            .uni-list-item__container {
                padding: 40rpx 0;

                .uni-list-item__content-title {
                    color: $uni-text-color-grey;
                    font-size: 28rpx;
                }

                .uni-list-item__extra {
                    color: $uni-text-color;
                    font-size: 28rpx;
                }
            }
        }
    }
}
</style>

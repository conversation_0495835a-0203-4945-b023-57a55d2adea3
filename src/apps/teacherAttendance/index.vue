<template>
    <div class="punch_page">
        <uni-nav-bar statusBar fixed left-icon="left" title="考勤打卡" :border="false" @clickLeft="routerBack"> </uni-nav-bar>
        <div class="punch_in">
            <!-- 头部自定义导航栏 -->
            <div class="teacher_info">
                <img class="avatar_teacher" src="@nginx/workbench/teacherAttendance/avatar_teacher.png" alt="" />
                <div class="info">
                    <span class="name">{{ data.personName }}</span>
                    <div class="text">
                        <span class="attendance_group">{{ data.groupName }}</span>
                        <span class="statistics" @click="statistics">统计</span>
                    </div>
                    <div class="tag" @click="viewRules">查看规则</div>
                </div>
            </div>
            <div class="punch_content">
                <div class="time">
                    <div class="punch_time" v-for="(i, index) in data.signTimeList" :key="index">
                        <span class="title">{{ i.signType == 0 ? "签到" : "签退" }}{{ i.shouldSignTime }}</span>
                        <span class="status"
                            >{{ i.actualSignTime ? i.actualSignTime + "已打卡" : "未打卡" }}
                            <span class="tip" v-if="!['未打卡'].includes(i.signResult)">
                                {{ i.signResult }}
                            </span>
                        </span>
                    </div>
                </div>
                <div class="punch_btn">
                    <div class="punch_btn_box" @click="punchFn">
                        <!-- 按钮颜色 -->
                        <!-- <img class="btn" :src="clockStatus.status == 0 ? '@nginx/workbench/teacherAttendance/punch_error_btn.png' : clockStatus.status == 1 ? '@nginx/workbench/teacherAttendance/punch_success_btn.png' : '@nginx/workbench/teacherAttendance/punch_warn_btn.png'" alt="" /> -->
                        <img class="btn" src="@nginx/workbench/teacherAttendance/punch_success_btn.png" alt="" />
                        <div class="punch_btn_text">
                            <span class="text title">{{ clockStatus.text }}</span>
                            <span class="text">{{ nowTime }}</span>
                        </div>
                    </div>
                    <div class="checkbox">
                        <image class="status_icon" :src="data.todayNeedSign ? '@nginx/workbench/teacherAttendance/punch_success.png' : '@nginx/workbench/teacherAttendance/punch_error.png'" alt="" />
                        <span class="status_text">{{ data.todayNeedSign ? "已进入考勤范围！" : "今日无排班！" }}</span>
                    </div>
                    <!-- 打卡提示 -->
                    <!-- <div class="checkbox">
                        <img class="status_icon" :src="data.canSign || data.canSignInOutside ? '@nginx/workbench/teacherAttendance/punch_success.png' : '@nginx/workbench/teacherAttendance/punch_error.png'" alt="" />
                        <span>{{ data.signMessage }}</span>
                    </div> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import dayjs from "dayjs"
import { onLoad } from "@dcloudio/uni-app"

const data = ref({})
const parame = ref({
    addressName: "德弘基创客居",
    effectiveRange: 500,
    geo: 113.877545,
    lat: 22.570005
})
const nowTime = ref()
function viewRules() {
    navigateTo({ url: "/apps/teacherAttendance/viewRules" })
}

function statistics() {
    navigateTo({ url: "/apps/teacherAttendance/statistics/index" })
}

const clockStatus = computed(() => {
    const { canSign, canSignByAddress, canSignInOutside, signInOrOut } = data.value
    // if (canSign) {
    // if (canSignByAddress) {
    //   return;
    // }
    // if (canSignInOutside) {
    //   return {
    //     text: "外勤打卡",
    //     status: 2,
    //   };
    // } else {
    return {
        text: signInOrOut == 0 ? "签到打卡" : "签退打卡",
        status: 1
    }
    // }
    // } else {
    //   return {
    //     text: "无法打卡",
    //     status: 0,
    //   };
    // }
})

function punchFn() {
    http.post("/attweb/attendanceWorkClock/clockByAddress", {
        ...parame.value
    }).then((res) => {
        uni.showToast({
            title: res.message,
            icon: "none",
            duration: 2000
        })
        getClockMessage()
    })
}

function getClockMessage() {
    http.post("/attweb/attendanceWorkClock/getClockMessage", {
        ...parame.value
    }).then((res) => {
        data.value = res.data
    })
}

function getLocationFn() {
    uni.getLocation({
        type: "gcj02",
        geocode: true,
        isHighAccuracy: true,
        success: async function (res) {
            console.log(res)
            console.log("当前位置的经度：" + res.longitude)
            console.log("当前位置的纬度：" + res.latitude)
            parame.value.geo = res.longitude
            parame.value.lat = res.latitude
        }
    })
}

// 更新时间的函数
function updateTime() {
    const now = dayjs().format("HH:mm:ss") // 格式化当前时间
    nowTime.value = now
}

// 每秒调用更新时间的函数

onLoad(() => {
    setInterval(updateTime, 1000)
    // getLocationFn()
    getClockMessage()
})
</script>

<style lang="scss" scoped>
.punch_page {
    max-height: 100vh;
}
.punch_in {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 148rpx);
    padding: 30rpx;

    // 头部

    .teacher_info {
        height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        padding: 30rpx;
        display: flex;
        align-items: center;
        position: relative;
        .avatar_teacher {
            width: 100rpx;
            height: 100rpx;
        }
        .info {
            display: flex;
            margin-left: 20rpx;
            height: 100rpx;
            flex-direction: column;
            justify-content: space-around;
            .name {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }
            .text {
                .attendance_group {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: $uni-text-color;
                    line-height: 34rpx;
                }
                .statistics {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: $uni-color-primary;
                    padding-left: 20rpx;
                    line-height: 34rpx;
                }
            }
        }
        .tag {
            position: absolute;
            top: 30rpx;
            right: 0rpx;
            width: 136rpx;
            height: 46rpx;
            border-radius: 30rpx 0rpx 0rpx 30rpx;
            font-weight: 400;
            font-size: 24rpx;
            color: $uni-color-primary;
            line-height: 46rpx;
            text-align: center;
            background: #cff4e7ff;
        }
    }
    .punch_content {
        margin-top: 20rpx;
        background: $uni-bg-color;
        padding: 30rpx;
        height: calc(100vh - 388rpx);
        border-radius: 20rpx;
        .time {
            display: flex;
            overflow-x: auto;
            max-width: 100%;
            .punch_time {
                margin-right: 30rpx;
                min-width: 260rpx;
                height: 84rpx;
                background: #f3fcf9;
                border-radius: 10rpx;
                display: flex;
                justify-content: space-between;
                flex-direction: column;
                padding: 20rpx;
                .title {
                    font-weight: 500;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }
                .status {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                    .tip {
                        margin-left: 10rpx;
                        width: 80rpx;
                        height: 30rpx;
                        border-radius: 4rpx;
                        border: 1rpx solid;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: $uni-color-warning;
                        line-height: 30rpx;
                        padding: 0rpx 10rpx;
                        text-align: center;
                    }
                }
            }
        }
        .punch_btn {
            margin-top: 200rpx;
            height: 394rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            flex-direction: column;
            .punch_btn_box {
                height: 280rpx;
                width: 280rpx;
                position: relative;
                .btn {
                    height: 280rpx;
                    width: 280rpx;
                }
                .punch_btn_text {
                    position: absolute;
                    width: 280rpx;
                    top: 86rpx;
                    left: 0rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    flex-direction: column;
                    .text {
                        font-weight: 500;
                        font-size: 30rpx;
                        color: $uni-text-color-inverse;
                        line-height: 42rpx;
                    }
                    .title {
                        font-weight: 500;
                        font-size: 40rpx;
                        color: $uni-text-color-inverse;
                        line-height: 56rpx;
                        padding-bottom: 10rpx;
                    }
                }
            }

            .checkbox {
                margin-top: 80rpx;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                .status_text {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: $uni-text-color;
                    line-height: 34rpx;
                    text-align: center;
                    padding-left: 4rpx;
                }
                .status_icon {
                    width: 28rpx;
                    height: 28rpx;
                    margin: 4rpx 0rpx 0rpx 0rpx;
                    display: inline-block;
                }
            }
        }
    }
}
</style>

<template>
    <div>
        <uni-nav-bar statusBar fixed left-icon="left" title="统计" :border="false" @clickLeft="clickLeft"> </uni-nav-bar>
        <div class="statistics">
            <view class="statistics_title">
                <view class="month_title">{{ data.month }}月汇总</view>
                <view class="month_statistics">
                    <div class="item">
                        <span class="num">{{ data.absenceFromWorkCount || 0 }}</span>
                        <span class="text">缺勤</span>
                    </div>
                    <div class="item">
                        <span class="num">{{ data.beLateCount || 0 }}</span>
                        <span class="text">迟到</span>
                    </div>
                    <div class="item">
                        <span class="num">{{ data.leaveEarlyCount || 0 }}</span>
                        <span class="text">早退</span>
                    </div>
                    <div class="item">
                        <span class="num">{{ data.askForLeaveCount || 0 }}</span>
                        <span class="text">请假</span>
                    </div>
                </view>
            </view>
            <view :class="{ minH: packUp }">
                <uni-calendar class="uni_calendar" :showMonth="false" :date="date" @change="change" @monthSwitch="monthSwitch" :selected="selectList" />
            </view>
            <view class="img_box" @click="packUpClick">
                <image class="image_icon" :src="packUp ? '@nginx/workbench/teacherAttendance/openTop.png' : '@nginx/workbench/teacherAttendance/openBottom.png'" />
            </view>
            <div class="rule_content">
                <span class="rule" v-if="dailyRuleDescription">规则：{{ dailyRuleDescription }}</span>
                <view class="content_list" v-if="dayList && dayList.length">
                    <div class="punch_item" v-for="(i, index) in dayList" :key="index">
                        <div class="circle"></div>
                        <div class="line" v-show="index != dayList.length - 1"></div>
                        <div class="box">
                            <div class="title_box">
                                <span class="title">{{ i.signTime ? i.signTime + "已打卡" : `${i.signType == 0 ? "签到" : "签退"}` + "未打卡" }}</span>
                                <div
                                    v-if="i.isAttendanceError || (i.signTime && i.signResult != '正常')"
                                    class="tip"
                                    :class="{
                                        color_error: ['缺勤'].includes(i.signResult),
                                        color_default: ['请假', '未打卡'].includes(i.signResult),
                                        color_warning: !['缺勤', '请假', '未打卡'].includes(i.signResult)
                                    }"
                                >
                                    {{ i.signResult }}
                                </div>
                                <div v-else class="tip">
                                    {{ i.signResult }}
                                </div>
                            </div>
                            <div class="handling_exception" v-if="i.isAttendanceError" @click="handleException(i)">处理异常</div>
                            <!-- 打卡地址 -->
                            <!-- <div class="text_content" v-if="i.address">
                            <img class="punch_position" src="@nginx/workbench/teacherAttendance/punch_position.png" alt="" />
                            <span>{{ i.address }}</span>
                        </div> -->
                        </div>
                    </div>
                </view>
                <yd-empty v-else text="今天休息日" :isMargin="true" />
            </div>
            <uni-popup type="bottom" ref="oaPopup" background-color="#fff" :borderRadius="'10rpx 10rpx 0rpx 0rpx'">
                <div class="oa_popup">
                    <div class="title">
                        <span>请选择您要提交的申请单</span>
                        <uni-icons @click="oaPopup.close()" class="icon" type="closeempty" size="20"></uni-icons>
                    </div>
                    <uni-list :border="false">
                        <uni-list-item v-for="(item, index) in oaList" :key="index" :title="item.formName" clickable @click="applyFor(item)" />
                    </uni-list>
                </div>
            </uni-popup>
        </div>
    </div>
</template>

<script setup>
import dayjs from "dayjs"
const date = ref(dayjs().format("YYYY-MM-DD"))
const data = ref({})
const oaPopup = ref()
const packUp = ref()
const dayList = ref([])
const month = ref(dayjs().format("YYYY-MM"))
const dailyRuleDescription = ref("")
const attendanceTime = ref(null)
const oaList = ref([])
const selectList = ref([])

const circleColor = computed(() => {
    return (i) => {
        if (i.hasRecord || i.hasLeaveEarly || i.hasBeLate) {
            return "#f0ad4e"
        } else if (i.hasAbsenceFromWork) {
            return "#fd4f45"
        } else if (i.hasAskForLeave || !i.hasRecord) {
            return "#333333"
        } else {
            return "#00b781"
        }
    }
})

function getPassedDates(dates) {
    const today = dayjs().startOf("day") // 获取今天的日期，去掉时间部分
    const passedDates = []

    dates.forEach((dateObj) => {
        const date = dayjs(dateObj.aboutDate)
        if (date.isBefore(today, "day") || date.isSame(today, "day")) {
            // 检查日期是否在今天之前
            passedDates.push(dateObj)
        }
    })

    return passedDates
}
function getClockRecordByMonth() {
    http.get("/attweb/attendanceWorkClock/getClockRecordByMonth", {
        month: month.value
    }).then((res) => {
        data.value = res.data
        const [year, month] = res.data.monthOfYear?.split("-")
        data.value.month = month
        const passedDates = getPassedDates(res.data.dailyRecord)
        const arr = passedDates.filter((i) => i.hasScheduling && !i.restDay)
        selectList.value = arr.map((i) => {
            return {
                date: i.aboutDate,
                color: circleColor.value(i),
                data: i
            }
        })
        selectList.value.forEach((i) => {
            if (i.date == date.value) {
                dayList.value = i.data.signTime
            }
        })
    })
}

const change = (e) => {
    if (e.extraInfo && e.extraInfo.data) {
        const data = e.extraInfo.data
        dayList.value = data && data.signTime ? e.extraInfo.data.signTime : []
    } else {
        dayList.value = []
    }
    dailyRuleDescription.value = e.extraInfo && e.extraInfo.data ? data.dailyRuleDescription : ""
}

function handleException(i) {
    attendanceTime.value = i.attendanceTime
    http.get("/app/formProcessTemplates/getErrorFormProcessList").then((res) => {
        oaList.value = res.data
        oaPopup.value.open()
    })
}

function packUpClick() {
    packUp.value = !packUp.value
    packUpFn()
}

function clickLeft() {
    uni.navigateBack()
}

function packUpFn() {
    // #ifdef H5-WEIXIN ||  H5
    const boxDom = document.getElementsByClassName("uni-calendar__box")[0]
    const dom = document.getElementsByClassName("uni-calendar-item--checked")[0] || document.getElementsByClassName("uni-calendar-item--isDay")[0]
    const weeks = boxDom.querySelectorAll(".uni-calendar__weeks")
    // 遍历并隐藏除了第一个之外的所有元素
    for (let i = 1; i < weeks.length; i++) {
        weeks[i].style.display = packUp.value ? "" : "none" // 隐藏元素
    }
    const checkedItems = dom.querySelectorAll(".uni-calendar-item--checked").length > 0 ? dom.querySelectorAll(".uni-calendar-item--checked") : dom.querySelectorAll(".uni-calendar-item--isDay")
    checkedItems.forEach((checkedItem) => {
        // 获取 checkedItem 的父级元素
        const parentWeek = checkedItem.closest(".uni-calendar__weeks")
        if (parentWeek) {
            // 确保父级元素是可见的
            parentWeek.style.display = "" // 或者使用 'block'，取决于你的布局
        }
    })
    // #endif
}

onMounted(() => {
    getClockRecordByMonth()
    nextTick(() => {
        packUpFn()
    })
})
function monthSwitch(e) {
    const [myyear, mymonth, day] = date.value?.split("-")
    date.value = e.year + "-" + e.month + "-" + day
    month.value = e.year + "-" + e.month
    getClockRecordByMonth()
    nextTick(() => {
        packUpFn()
    })
}

function applyFor(item) {
    navigateTo({
        url: "/apps/teacherAttendance/statistics/oaApplyFor",
        query: {
            fromKey: item.fromKey,
            fillTime: attendanceTime.value
        }
    })
}
</script>

<style lang="scss" scoped>
.statistics {
    background: $uni-bg-color;
    min-height: calc(100vh - 88rpx);
    .statistics_title {
        padding: 30rpx;
        .month_title {
            font-weight: 600;
            font-size: 34rpx;
            color: $uni-text-color;
            line-height: 42rpx;
            margin-bottom: 20rpx;
        }
        .month_statistics {
            height: 164rpx;
            background-color: $uni-bg-color-grey;
            border-radius: 20rpx;
            display: flex;
            justify-content: space-around;
            align-items: center;
            .item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .num {
                    font-weight: 500;
                    font-size: 44rpx;
                    color: $uni-text-color;
                    line-height: 60rpx;
                }
                .text {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                }
            }
        }
    }
    .rule_content {
        padding: 30rpx;
        .rule {
            font-weight: 400;
            font-size: 24rpx;
            color: $uni-text-color;
            line-height: 34rpx;
        }
    }
    .content_list {
        display: flex;
        flex-direction: column;
        .punch_item {
            display: flex;
            min-height: 98rpx;
            .circle {
                width: 16rpx;
                height: 16rpx;
                background: #b3b3b3;
                border-radius: 50%;
            }
            .line {
                width: 3rpx;
                min-height: 100%;
                background: #b3b3b3;
                margin-left: -10rpx;
            }
            .box {
                padding-bottom: 30rpx;
                margin-left: 50rpx;
                margin-top: -10rpx;
                .title_box {
                    display: flex;
                    align-items: center;
                    .title {
                        font-weight: 500;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }
                    .tip {
                        margin-left: 10rpx;
                        width: 80rpx;
                        height: 30rpx;
                        border-radius: 4rpx;
                        border: 1rpx solid;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: $uni-text-color;
                        line-height: 30rpx;
                        text-align: center;
                    }
                    .color_error {
                        color: $uni-color-error;
                        border-color: $uni-color-error;
                    }
                    .color_warning {
                        color: $uni-color-warning;
                        border-color: $uni-color-warning;
                    }

                    .color_default {
                        color: $uni-text-color;
                        border-color: $uni-text-color;
                    }
                }
                .handling_exception {
                    width: 100rpx;
                    height: 30rpx;
                    border-radius: 4rpx;
                    border: 1rpx solid $uni-color-error;
                    font-weight: 400;
                    font-size: 20rpx;
                    color: $uni-color-error;
                    line-height: 30rpx;
                    margin-top: 10rpx;
                    text-align: center;
                }
                .text_content {
                    margin-top: 10rpx;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #666666;
                    line-height: 34rpx;
                    display: flex;
                    align-items: center;
                    .punch_position {
                        width: 28rpx;
                        height: 28rpx;
                    }
                }
            }
        }
    }
    .minH {
        // height: 300rpx;
        overflow: hidden;
    }
    .img_box {
        text-align: center;
        background-color: #f3fcf9;
        padding: 28rpx;
        .image_icon {
            width: 56rpx;
            height: 16rpx;
        }
    }
    .uni_calendar {
        :deep(.uni-calendar__header-text) {
            font-size: 18px;
            font-weight: 600;
        }
        :deep(.uni-calendar__content) {
            background-color: #f3fcf9;
        }
        :deep(.uni-calendar-item--isDay) {
            border-radius: 50%;
        }
        :deep(.uni-calendar-item--checked) {
            border-radius: 50%;
        }
        :deep(.uni-calendar-item__weeks-lunar-text) {
            display: none;
        }
        :deep(.uni-calendar-item__weeks-box-circle) {
            top: 74rpx;
            right: 46rpx;
            width: 10rpx;
            height: 10rpx;
        }
    }
    .oa_popup {
        padding: 30rpx;
        border-radius: 10rpx 10rpx 0rpx 0rpx;
        .title {
            width: 100%;
            font-size: 34rpx;
            font-weight: 600;
            color: $uni-text-color;
            text-align: center;
            margin-bottom: 20rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
                position: absolute;
                right: 20rpx;
            }
        }
    }
    :deep(.uni-list-item__content) {
        text-align: center;
    }
}
</style>

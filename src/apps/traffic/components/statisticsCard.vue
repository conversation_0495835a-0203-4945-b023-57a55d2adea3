<template>
    <view class="statistics_card">
        <view class="card">
            <view class="card_box" @click="gotoDetail">
                <view class="title_box">
                    <text class="title">
                        通行统计
                        <text class="unit">（人）</text>
                    </text>
                    <view v-if="showTip" @click.stop="confirmRef.open()">
                        <uni-icons type="help" size="24" color="#fff"></uni-icons>
                    </view>
                </view>
                <view class="statistics_num">
                    <view class="num_item" :class="item.code == 'outPeopleNum' ? 'border_class' : ''" v-for="item in column" :key="item.code">
                        <text class="num">{{ data[item.code] || 0 }}</text>
                        <text class="label">{{ item.label }}</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 确认删除框 -->
        <yd-popup ref="confirmRef" title="解释说明" :canceFlag="false" confirmText="我知道了">
            <view class="dialog_content">
                <image src="@nginx/workbench/traffic/statistics_card_tip.png" mode="scaleToFill" class="tip_image" />
                <text class="text_content"> 通行数据的数据统计根据用户所拥有的数据范围权限去展示。比如用户只有一年级1班和一年级2班的权限，那么只统计一年级1班和一年级2班的通行数据。 </text>
            </view>
        </yd-popup>
    </view>
</template>

<script setup>
const emit = defineEmits(["goDetail"])
const props = defineProps({
    showTip: {
        type: Boolean,
        default: false
    },
    data: {
        type: Object,
        default: () => {
            return {
                businessName: "",
                totalPeopleNum: 0,
                outPeopleNum: 0,
                inPeopleNum: 0
            }
        }
    }
})
const confirmRef = ref(null)
const data = computed(() => props.data)
const showTip = computed(() => props.showTip)

const column = ref([
    {
        code: "totalPeopleNum",
        label: "通行人数"
    },
    {
        code: "outPeopleNum",
        label: "出校"
    },
    {
        code: "inPeopleNum",
        label: "入校"
    }
])

function gotoDetail() {
    emit("goDetail", data.value)
}
</script>

<style lang="scss" scoped>
.statistics_card {
    padding: 30rpx;
    background: $uni-bg-color;
    .card {
        height: 234rpx;
        width: calc(100vw - 60rpx);
        background: url("@nginx/workbench/traffic/statistics_card.png") no-repeat;
        background-size: 100% 100%;
        .card_box {
            padding: 20rpx 30rpx;
            display: flex;
            height: 194rpx;
            flex-direction: column;
            .title_box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #ffffff;
                    line-height: 42rpx;
                    .unit {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #ffffff;
                        line-height: 34rpx;
                    }
                }
            }
            .statistics_num {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex: 1;
                .num_item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    .num {
                        font-weight: 500;
                        font-size: 34rpx;
                        color: #ffffff;
                        line-height: 36rpx;
                        margin-bottom: 20rpx;
                    }
                    .label {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #ffffff;
                        line-height: 26rpx;
                    }
                }
                .border_class {
                    border-left: 1rpx solid #d8d8d85e;
                    border-right: 1rpx solid #d8d8d85e;
                }
            }
        }
    }
    .dialog_content {
        position: relative;
        .tip_image {
            width: 224rpx;
            height: 190rpx;
            position: absolute;
            right: -40rpx;
            top: -100rpx;
        }
        .text_content {
            position: relative;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
        }
    }
}
</style>

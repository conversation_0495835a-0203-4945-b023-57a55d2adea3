<template>
    <view class="release">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="添加放行" :rightWidth="120" :leftWidth="120">
            <!-- #ifdef H5-WEIXIN || H5 -->
            <template v-slot:right>
                <view class="bar_right" @click="goRecord">放行记录</view>
            </template>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <template v-slot:right>
                <view class="bar_right" @click="goRecord">放行记录</view>
            </template>
            <!-- #endif -->
        </uni-nav-bar>
        <!-- #ifdef MP-WEIXIN -->
        <view class="bar_right" @click="goRecord">放行记录</view>
        <!-- #endif -->
        <view class="release_form">
            <view class="form_item">
                <view class="form_item_title">
                    <text>选择人员</text>
                </view>
                <view
                    class="select_personnel"
                    :style="{
                        color: form.businessName ? '#333333' : '#999999'
                    }"
                    @click="selectPersonnel"
                >
                    {{ form.businessName || "添加人员" }}
                </view>
            </view>
            <view class="form_item">
                <view class="form_item_title">
                    <text>放行时间</text>
                </view>
                <view class="select_time">
                    <picker mode="time" :value="form.startTime" @change="changeStartTime">
                        <view class="time_input">{{ form.startTime }}</view>
                    </picker>
                    <view class="connect">至</view>
                    <picker mode="time" :value="form.endTime" @change="changeEndTime">
                        <view class="time_input">{{ form.endTime }}</view>
                    </picker>
                </view>
            </view>
            <view class="form_item">
                <view class="form_item_title">
                    <text>放行原因</text>
                </view>
                <view class="textarea_class">
                    <uni-easyinput :inputBorder="false" type="textarea" v-model="form.reason" :maxlength="30" placeholder="请输入内容" placeholderStyle="color: #999999;font-size: 28rpx"></uni-easyinput>
                    <view class="num_box">{{ form.reason?.length || 0 }}/30</view>
                </view>
            </view>
        </view>
        <div class="btn_class">
            <button type="default" @click="confirmRelease" class="button" :class="loading ? 'disabled_class' : ''" :loading="loading" :disabled="loading">添加</button>
        </div>
        <yd-selector ref="selectorRef" @confirm="confirmSelector" />
    </view>
</template>

<script setup>
import dayjs from "dayjs"

const selectorRef = ref(null)
const form = ref({
    startTime: dayjs().format("HH:mm"),
    endTime: dayjs().add(1, "hour").format("HH:mm"),
    reason: ""
})
const loading = ref(false)

function selectPersonnel() {
    const typeList = [
        {
            type: "student",
            name: "学生",
            selectLevel: "student"
        },
        {
            type: "classes",
            name: "班级",
            selectLevel: "classes"
        }
    ]
    selectorRef.value.open(typeList)
}

function changeStartTime(time) {
    form.value.startTime = time.detail.value
}

function changeEndTime(time) {
    form.value.endTime = time.detail.value
}

function confirmSelector(ids, selected) {
    if (ids && ids.length) {
        const type = {
            student: 1,
            classes: 2,
            grade: 3
        }
        form.value.type = type[selected[0]?.typeValue] // //1：学生 2：班级 3:年级
        form.value.businessName = selected.map((i) => i.name).join("、")
        form.value.businessId = ids
    }
}

function goRecord() {
    form.value = {}
    navigateTo({
        url: "/apps/traffic/release/record"
    })
}

function confirmRelease() {
    loading.value = true
    http.post("/cloud/quick/pass/create", form.value)
        .then((res) => {
            uni.showToast({ title: res.message, icon: "none" })
            goRecord()
        })
        .finally(() => {
            loading.value = false
        })
}
</script>

<style lang="scss" scoped>
.release {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    .bar_right {
        padding: 10rpx 0rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-color-primary;
        line-height: 40rpx;
        display: flex;
        justify-content: flex-end;
    }
    .release_form {
        background: $uni-bg-color;
        padding: 30rpx;
        .form_item {
            display: flex;
            padding-bottom: 30rpx;
            flex-direction: column;
            .form_item_title {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                line-height: 34rpx;
                margin-bottom: 10rpx;
            }
            .select_personnel {
                padding: 20rpx 10rpx;
                background: $uni-bg-color-grey;
                border-radius: 4rpx;
                font-weight: 400;
                font-size: 28rpx;
            }
            .select_time {
                display: flex;
                align-items: center;
                .connect {
                    padding: 0 10rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                }
                .time_input {
                    background: $uni-bg-color-grey;
                    height: 80rpx;
                    width: 320rpx;
                    flex: 1;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 80rpx;
                    text-align: center;
                }
            }
            .textarea_class {
                position: relative;
                background: $uni-bg-color-grey;
                padding: 10rpx 20rpx;
                :deep(.uni-easyinput__content) {
                    background: $uni-bg-color-grey !important;
                }
                .num_box {
                    position: absolute;
                    bottom: 20rpx;
                    right: 15rpx;
                    font-size: 20rpx;
                    color: #898989;
                }
            }
        }
    }
    .btn_class {
        position: fixed;
        bottom: 40rpx;
        left: 0;
        width: calc(100vw - 60rpx);
        padding: 30rpx;
        background: $uni-bg-color;

        .button {
            width: 100%;
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
        }

        .disabled_class {
            background: #ccc;
        }
    }
}
</style>

<template>
    <view class="details">
        <!-- 列表 -->
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="通行数据详情"> </uni-nav-bar>

                <!-- 统计卡片 -->
                <statistics-card :data="card" />

                <!-- tabs  -->
                <uv-tabs :current="0" :scrollable="false" lineColor="#00b781" :activeStyle="{ color: '#00b781' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>
            </template>

            <view class="list_box" v-if="dataList && dataList.length">
                <view class="list_item tr_item">
                    <text class="name">姓名</text>
                    <text class="name">{{ ["", "入校时间", "出校时间"][tabsCode] }}</text>
                </view>
                <view class="list_item" v-for="(item, index) in dataList" :key="index">
                    <text class="name">{{ item.userName }}</text>
                    <text class="name">{{ getTime(item) }}</text>
                    <text class="look" @click="userTraffic(item)">查看</text>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { computed } from "vue"
import StatisticsCard from "./components/statisticsCard.vue"
const params = ref({})
const card = ref({})
const tabsCode = ref(0)
const paging = ref(null)
const dataList = ref([])
const tabsList = ref([
    {
        name: "通行人数",
        code: 0
    },
    {
        name: "出校",
        code: 1
    },
    {
        name: "入校",
        code: 2
    }
])

// 调用List数据
async function queryList() {
    uni.showLoading({
        title: "加载中..."
    })

    const { businessId, queryDate, passId, userType, isAll } = params.value
    let url = ""
    let obj = { queryDate, passId }
    if (isAll == "true" && tabsCode.value == 0) {
        url = "/app/traffic/record/getAllUserTrafficRecordDetail"
        obj = {
            ...obj,
            businessId,
            userType
        }
    } else {
        if (userType == 1) {
            url = tabsCode.value == 0 ? "/app/traffic/statistics/deptPerson/detail" : "/app/traffic/statistics/direction/dept/detail"
            obj =
                isAll == "true"
                    ? { ...obj, direction: tabsCode.value }
                    : {
                          ...obj,
                          direction: tabsCode.value,
                          deptId: userType == 1 ? businessId : null,
                          classesId: userType == 0 ? businessId : null
                      }
        } else {
            url = tabsCode.value == 0 ? "/app/traffic/statistics/classesStudent/detail" : "/app/traffic/statistics/direction/classes/detail"
            obj =
                isAll == "true"
                    ? { ...obj, direction: tabsCode.value }
                    : {
                          ...obj,
                          direction: tabsCode.value,
                          deptId: userType == 1 ? businessId : null,
                          classesId: userType == 0 ? businessId : null
                      }
        }
    }
    await http
        .post(url, obj)
        .then((res) => {
            console.log(params.value.isAll == "true")
            if (params.value.isAll == "true" && tabsCode.value == 0) {
                paging.value.setLocalPaging(res.data.trafficUserList)
                card.value = res.data
            } else {
                paging.value.setLocalPaging(res.data)
            }
        })
        .finally(() => {
            uni.hideLoading()
        })
        .catch(() => {
            paging.value.setLocalPaging([])
        })
}

const getTime = computed(() => {
    return (item) => {
        if (item && item.lastTrafficDetail) {
            return item.lastTrafficDetail.time?.match(/(\d{2}:\d{2})/)[0] || "-"
        }
        return item.time || "-"
    }
})

function tabsClick(item) {
    tabsCode.value = item.code
    paging.value.reload()
}

function getCardData() {
    const { businessId, queryDate, passId, userType } = params.value
    const url = userType == 1 ? "/app/traffic/statistics/dept/count" : "/app/traffic/statistics/classes/count"
    http.post(url, {
        queryDate,
        passId,
        userType,
        deptId: userType == 1 ? businessId : null,
        classesId: userType == 0 ? businessId : null
    }).then((res) => {
        card.value = res.data
    })
}

function userTraffic(item) {
    const queryObj = {
        userName: item.userName,
        userId: item.userId,
        userType: params.value.userType,
        queryDate: params.value.queryDate,
        passId: params.value.passId
    }
    navigateTo({
        url: "/apps/traffic/personDetails",
        query: queryObj
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    params.value = options
    if (params.value.isAll != "true") getCardData()
    queryList()
})
</script>

<style lang="scss" scoped>
.details {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    :deep(.statistics_card) {
        padding: 30rpx;
        background: $uni-bg-color-grey;
    }
    :deep(.zp-scroll-view) {
        background: #fff;
        border-top: 1rpx solid #d8d8d8;
    }
    .list_box {
        background: #fff;
        display: flex;
        flex-direction: column;
        padding: 28rpx 30rpx;

        .list_item {
            padding: 30rpx 20rpx;
            display: flex;
            justify-content: space-between;
            border-bottom: 1rpx solid #d8d8d8;
            align-items: center;
            .name {
                font-weight: 400;
                font-size: 24rpx;
                color: #606266;
                line-height: 34rpx;
                flex: 1;
            }
            .look {
                font-weight: 400;
                font-size: 24rpx;
                color: #11c685;
                line-height: 34rpx;
            }
        }
        .tr_item {
            height: 80rpx;
            padding: 0 68rpx 0 20rpx;
            background: #f3fcf9;
            border-radius: 8rpx;
            border: none;
        }
    }
}
</style>

<template>
    <view class="grade_details">
        <!-- 列表 -->
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false" use-virtual-list>
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" :title="params.pageTitle || '通行数据详情'"> </uni-nav-bar>

                <!-- 统计卡片 -->
                <statistics-card :data="card" />
            </template>
            <view class="list_box">
                <view class="list_item" v-for="item in dataList" :key="item.businessId">
                    <view class="title_box">
                        <text class="title">{{ item.businessName }}</text>
                        <uni-icons @click="gotoDetails(item)" type="right" size="20" color="#999999"></uni-icons>
                    </view>
                    <view class="statistics_num">
                        <view class="num_item" :class="j.code == 'outPeopleNum' ? 'border_class' : ''" v-for="j in column" :key="item.businessId + j.code">
                            <text class="num">{{ item[j.code] || 0 }}</text>
                            <text class="label">{{ j.label }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import StatisticsCard from "./components/statisticsCard.vue"
const params = ref({})
const card = ref({})
const paging = ref(null)
const dataList = ref([])

const column = ref([
    {
        code: "totalPeopleNum",
        label: "通行人数"
    },
    {
        code: "outPeopleNum",
        label: "出校"
    },
    {
        code: "inPeopleNum",
        label: "入校"
    }
])
function getCardData() {
    const { businessId, queryDate, passId } = params.value
    http.post("/app/traffic/statistics/grade/count", {
        gradeId: businessId,
        queryDate,
        passId
    }).then((res) => {
        card.value = res.data
    })
}

function queryList() {
    const { businessId, queryDate, passId } = params.value
    http.post("/app/traffic/statistics/grade/eachClass", {
        gradeId: businessId,
        queryDate,
        passId
    }).then((res) => {
        paging.value.complete(res.data)
    })
}

function gotoDetails(item) {
    navigateTo({
        url: "/apps/traffic/details",
        query: {
            ...params.value,
            pageTitle: item.businessName,
            businessId: item.businessId
        }
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    params.value = options
    getCardData()
    queryList()
})
</script>

<style lang="scss" scoped>
.grade_details {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    :deep(.statistics_card) {
        padding: 30rpx 30rpx 0 30rpx;
        background: $uni-bg-color-grey;
    }
    .list_box {
        padding: 30rpx;
        .list_item {
            height: 224rpx;
            background: #ffffff;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            flex-direction: column;
            display: flex;
            margin-bottom: 20rpx;

            .title_box {
                padding: 28rpx 30rpx;
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 1rpx solid $uni-border-color;
                .title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    line-height: 42rpx;
                }
            }
            .statistics_num {
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex: 1;
                .num_item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    flex: 1;
                    .num {
                        font-weight: 500;
                        font-size: 34rpx;
                        color: #666666;
                        line-height: 36rpx;
                        margin-bottom: 20rpx;
                    }
                    .label {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #666666;
                        line-height: 26rpx;
                    }
                }
                .border_class {
                    border-left: 1rpx solid $uni-border-color;
                    border-right: 1rpx solid $uni-border-color;
                }
            }
        }
    }
}
</style>

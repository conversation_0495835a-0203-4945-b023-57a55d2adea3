<template>
    <view class="person_details">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" :title="pageTitle"> </uni-nav-bar>
        <view class="user_time">
            <view class="user">{{ params.userName }}</view>
            <view class="time">
                <uni-datetime-picker :border="false" :clearIcon="false" type="date" placeholder="选择日期" v-model="datetime" @change="getInfo" />
            </view>
        </view>
        <view class="pass_info">
            <view class="user_pass_today">
                <view class="user_info">
                    <div class="left_avatar">
                        <image v-if="info && info.image" :src="info.image" mode="scaleToFill" class="avatar_image" />
                        <view class="def_avatar" v-else>{{ params.userName ? params.userName.slice(-2) : "-" }}</view>
                    </div>
                    <div class="right">
                        <text class="name">{{ params?.userName }}</text>
                        <text class="pass_num">今日通行{{ info?.trafficDetailList?.length || 0 }}次</text>
                    </div>
                </view>
                <view class="pass_today">
                    <view v-for="item in directionPass" :key="item.direction">
                        <view
                            v-if="isShowDirection(item)"
                            class="pass_item"
                            :style="{
                                marginBottom: item == 'first' ? '32rpx' : 0
                            }"
                        >
                            <text
                                class="pass_time"
                                :style="{
                                    color: directionColor(item)
                                }"
                            >
                                {{ passItemText(item) }}
                            </text>
                            <text class="pass_type">{{ item == "first" ? "最早" : "最晚" }}通行时间</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="detail_record">
                <text class="record_title">详细记录</text>
                <view class="record_list" v-if="info && info.trafficDetailList && info.trafficDetailList.length">
                    <view class="record_item" v-for="(item, index) in info.trafficDetailList" :key="index">
                        <view class="left">
                            <text
                                class="left_direction"
                                :style="{
                                    color: directionTrafficColor[item.direction]
                                }"
                                >{{ directionTraffic[item.direction] }}</text
                            >
                            <view class="right_info">
                                <view class="step">
                                    <view
                                        class="circle"
                                        :style="{
                                            background: directionTrafficColor[item.direction]
                                        }"
                                    ></view>
                                    <view class="line" v-if="index != info.trafficDetailList.length - 1"></view>
                                </view>
                                <view class="record_info">
                                    <text class="date">{{ item.time }}</text>
                                    <text class="device">{{ item.deviceName }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="right">
                            <image @click="previewFaceUrl(index)" class="image" v-if="item.trafficFaceUrl" :src="item.trafficFaceUrl" mode="scaleToFill" />
                        </view>
                    </view>
                </view>
                <yd-empty v-else :isMargin="true" />
            </view>
        </view>
    </view>
</template>

<script setup>
import dayjs from "dayjs"
import { computed } from "vue"

const params = ref({})
const info = ref({})
const datetime = ref("")

const pageTitle = computed(() => {
    return `${params.value.userName}的通行数据详情` || "通行数据详情"
})

const directionPass = ["first", "last"]

const isShowDirection = computed(() => {
    return (item) => {
        if (info.value) {
            const { firstTrafficDetail, lastTrafficDetail } = info.value
            if (firstTrafficDetail && item == "first") {
                return !!firstTrafficDetail.time
            }
            if (lastTrafficDetail && item == "last") {
                return !!lastTrafficDetail.time
            }
        }
        return false
    }
})

function previewFaceUrl(index) {
    const urls = info.value?.trafficDetailList?.map((item) => item.trafficFaceUrl) || []
    uni.previewImage({
        urls,
        current: index
    })
}

const directionTraffic = ref({
    1: "出",
    2: "入"
})

const directionTrafficColor = ref({
    1: "#FAAD14",
    2: "#11C685"
})

// 出入方向颜色
const directionColor = computed(() => {
    return (item) => {
        if (info.value) {
            const { firstTrafficDetail, lastTrafficDetail } = info.value
            if (item == "first") {
                return directionTrafficColor.value[firstTrafficDetail.direction]
            } else {
                return directionTrafficColor.value[lastTrafficDetail.direction]
            }
        }
    }
})

const passItemText = computed(() => {
    return (item) => {
        if (info.value) {
            const { firstTrafficDetail, lastTrafficDetail } = info.value
            if (item == "first") {
                const time = firstTrafficDetail.time?.split(" ")[1]
                const siteName = firstTrafficDetail.siteName
                const directionText = directionTraffic.value[firstTrafficDetail.direction]
                return `${time}  ${directionText}-${siteName}`
            } else {
                const time = lastTrafficDetail.time?.split(" ")[1]
                const siteName = lastTrafficDetail.siteName
                const directionText = directionTraffic.value[lastTrafficDetail.direction]
                return `${time}  ${directionText}-${siteName}`
            }
        }
    }
})

function getInfo() {
    http.post("/app/traffic/statistics/user/detail", { ...params.value, queryDate: datetime.value }).then((res) => {
        info.value = res.data
        console.log(res.data)
    })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    params.value = options
    datetime.value = options.queryDate || dayjs().format("YYYY-MM-DD")
    getInfo()
})
</script>

<style lang="scss" scoped>
.person_details {
    min-height: 100vh;
    background: $uni-bg-color-grey;
    .user_time {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10rpx 30rpx;
        background: #ffffff;
        .user {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
        }
        .time {
            display: flex;
            align-items: center;

            :deep(.uni-icons) {
                display: none;
            }
            &::after {
                content: "";
                display: block;
                border: 10rpx solid transparent;
                border-top: 10rpx solid $uni-color-primary;
                border-bottom-width: 1px;
                margin-left: 10rpx;
            }
        }
    }
    .pass_info {
        padding: 30rpx;
        .user_pass_today {
            background: #ffffff;
            border-radius: 20rpx;
            padding: 30rpx;
            .user_info {
                padding-bottom: 30rpx;
                border-bottom: 1rpx solid $uni-border-color;
                display: flex;
                align-items: center;
                .left_avatar {
                    .avatar_image {
                        width: 100rpx;
                        height: 100rpx;
                    }
                    .def_avatar {
                        width: 100rpx;
                        height: 100rpx;
                        border-radius: 50%;
                        line-height: 100rpx;
                        text-align: center;
                        color: #fff;
                        font-size: 24rpx;
                        background: #bfbfbf;
                    }
                }
                .right {
                    margin-left: 20rpx;
                    display: flex;
                    flex-direction: column;
                    color: #1e1e1e;
                    .name {
                        font-weight: 500;
                        font-size: 32rpx;
                        line-height: 44rpx;
                        padding-bottom: 4rpx;
                    }
                    .pass_num {
                        padding-top: 4rpx;
                        font-weight: 400;
                        font-size: 24rpx;
                        line-height: 34rpx;
                    }
                }
            }
            .pass_today {
                padding-top: 34rpx;
                .pass_item {
                    display: flex;
                    flex-direction: column;
                }
                .pass_time {
                    font-weight: 400;
                    font-size: 30rpx;
                    line-height: 42rpx;
                }
                .pass_type {
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #909399;
                    line-height: 34rpx;
                    padding-top: 6rpx;
                }
            }
        }
        .detail_record {
            margin-top: 30rpx;
            padding: 30rpx;
            background: #ffffff;
            .record_title {
                font-weight: 500;
                font-size: 32rpx;
                color: #333333;
                line-height: 44rpx;
            }
            .record_list {
                margin-top: 50rpx;
                .record_item {
                    display: flex;
                    justify-content: space-between;
                    .left {
                        display: flex;
                        .left_direction {
                            font-weight: 500;
                            font-size: 28rpx;
                            line-height: 40rpx;
                            text-align: left;
                        }
                        .right_info {
                            display: flex;
                            .step {
                                height: 100%;
                                margin: 0 30rpx;
                                margin-top: 10rpx;
                                .circle {
                                    width: 18rpx;
                                    height: 18rpx;
                                    border-radius: 50%;
                                }
                                .line {
                                    width: 1rpx;
                                    height: 100%;
                                    margin: 0 7rpx;
                                    background: #d8d8d8;
                                }
                            }
                            .record_info {
                                display: flex;
                                flex-direction: column;
                                margin-bottom: 50rpx;

                                .date {
                                    font-weight: 400;
                                    font-size: 28rpx;
                                    color: #333333;
                                    line-height: 40rpx;
                                }
                                .device {
                                    margin-top: 10rpx;
                                    font-weight: 400;
                                    font-size: 26rpx;
                                    color: #999999;
                                    line-height: 36rpx;
                                }
                            }
                        }
                    }
                    .right {
                        width: 62rpx;
                        height: 62rpx;
                        .image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
        }
    }
}
</style>

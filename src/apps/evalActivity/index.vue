<template>
	<div>
		<!-- #ifdef H5-WEIXIN || H5 -->
		<iframe class="webview" :src="src" frameborder="0"></iframe>
		<!-- #endif -->
		<!-- #ifdef MP-WEIXIN || APP-PLUS-->
		<web-view class="webview" :src="src"></web-view>
		<!-- #endif -->
	</div>
</template>

<script setup>
import { getToken } from "@/utils/storageToken.js"
import { onLoad } from "@dcloudio/uni-app"
import useStore from "@/store"

const token = getToken()?.replace("Bearer ", "")
const { user } = useStore()

const identityType = computed(() => {
	const roleCode = user?.identityInfo?.roleCode
	if (roleCode == "eltern") {
		return "parent"
	} else if (roleCode == "student") {
		return "parent"
	} else {
		return "teacher"
	}
})
const src = ref('')


onLoad((options) => {
	if (options.code != "oaApprove") {
		src.value = `${import.meta.env.VITE_BASE_APPLET}/#/evaluate/${identityType.value}/home?token=${token}&code=${options.code}`
	}
})
</script>

<style lang="scss" scoped>
.webview {
	height: 100vh;
	width: 100vw;
}
</style>
<template>
    <div>
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="参与人员"> </uni-nav-bar>
        <div class="participants">
            <div class="user_info">
                <div class="left">
                    <div class="avatar">
                        <image mode="aspectFill" v-if="queryParams.avatar" class="avatar_img" :src="queryParams.avatar" alt="" />
                        <span v-else>{{ queryParams.toPersonName?.slice(0, 1) }}</span>
                    </div>
                    <div v-if="queryParams.operationFlag == 1" class="mask_layer">
                        {{ operationFlagStatus[queryParams.operationFlag] }}
                    </div>
                    <div class="info">
                        <div class="name">{{ queryParams.toPersonName }}</div>
                        <div class="class_dept">{{ queryParams.classesName || queryParams.orgName }}</div>
                    </div>
                </div>
                <div class="right">
                    <div class="num">{{ queryParams.thisCount }}/{{ queryParams.totalCount }}</div>
                    <div class="text">参与次数</div>
                </div>
            </div>
            <div class="evaluate">
                <span class="evaluate_title_num">第{{ queryParams.partakeCount }}次评价</span>
                <div class="rule" v-for="item in state.participants" :key="item.id">
                    <div class="rule_title_box">
                        <image class="rule_img" src="@nginx/workbench/evalActivity/teacher/rule_title.png" alt="" />
                        <span class="title">{{ item.name }}</span>
                    </div>
                    <div class="rule_item" v-for="(r, rIndex) in item.secondIndicators" :key="rIndex + 'rule'">
                        <div class="split_line" v-if="rIndex != 0"></div>
                        <span class="item_title">{{ rIndex + 1 }}.{{ r.indicatorScore.content }}</span>
                        <span class="scoring_range"> 评分范围：{{ r.indicatorScore.minScore }}分~{{ r.indicatorScore.maxScore }}分 </span>
                        <div class="num_input_box">
                            <div class="btn">
                                <image @click="handlerReducePoints(item, r)" class="btn_img" src="@nginx/workbench/evalActivity/teacher/minus_mark.png" alt="" />
                            </div>

                            <uni-easyinput class="num_input" type="digit" :inputBorder="false" :clearable="false" v-model="state.scoringObj[`${item.id}_${r.id}`].score" @change="handlerInput($event, item, r)"></uni-easyinput>
                            <div class="btn">
                                <image @click="handlerBonusPoints(item, r)" class="btn_img" src="@nginx/workbench/evalActivity/teacher/add_mark.png" alt="" />
                            </div>
                        </div>
                        <div class="score_box">
                            <span>本次评分：</span>
                            <span class="score">{{ r.thisIndicatorScore }}分</span>
                        </div>
                        <div class="score_box">
                            <span>他人评分：</span>
                            <span class="score">{{ r.othersIndicatorScore }}分</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="evaluate_footer">
                <span class="aggregate_score"
                    >总分： <span class="score" v-if="totalScoreCompt">{{ totalScoreCompt }}分</span>
                </span>
                <div class="footer_btn">
                    <button class="footer_btn" :loading="state.loading" type="primary" @click="handlerSave">确定</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const operationFlagStatus = {
    0: "无权限审批",
    1: "未评",
    2: "已评"
}
const state = reactive({
    participants: [],
    scoringList: [],
    scoringObj: {},
    loading: false
})

const queryParams = ref({})

const totalScoreCompt = computed(() => {
    let totalScore = 0
    if (Object.keys(state.scoringObj).length) {
        Object.values(state.scoringObj).forEach((v) => {
            if (v.evaluatePersonNum) {
                totalScore += (v.score + v.othersTotalScore) / v.evaluatePersonNum
            } else {
                totalScore += parseInt(v.score)
            }
        })
    }
    return totalScore
})
// 减积分
const handlerReducePoints = (item, it) => {
    const { minScore, othersTotalScore, evaluatePersonNum } = it.indicatorScore
    if (minScore > state.scoringObj[`${item.id}_${it.id}`].score) {
        state.scoringObj[`${item.id}_${it.id}`].score = minScore
        return
    }
    // 如果点的减法值等于输入值  则不让输入 提示已经是最小值
    if (minScore == state.scoringObj[`${item.id}_${it.id}`].score) {
        uni.showToast({
            title: "已到最小值",
            icon: "none"
        })
        return
    }
    // 如果已经等于最小值了，就不允许再减了 否则就减一
    if (state.scoringObj[`${item.id}_${it.id}`].score == minScore) {
        // （你的分数+其他人评价分数总和）/ 总人数
        state.scoringObj[`${item.id}_${it.id}`].score = minScore
        state.scoringObj[`${item.id}_${it.id}`].othersTotalScore = othersTotalScore
        state.scoringObj[`${item.id}_${it.id}`].evaluatePersonNum = evaluatePersonNum
    } else {
        state.scoringObj[`${item.id}_${it.id}`].score--
    }
}

function clickLeft() {
    uni.navigateBack()
}

// 加积分
const handlerBonusPoints = (item, it) => {
    // 初始如果等于0就设置为最小值 否则就加一
    const { minScore, maxScore, othersTotalScore, evaluatePersonNum } = it.indicatorScore
    if (state.scoringObj[`${item.id}_${it.id}`].score > maxScore) {
        state.scoringObj[`${item.id}_${it.id}`].score = maxScore
        return
    }
    // 如果点的减法值等于输入值  则不让输入 提示已经是最小值
    if (maxScore == state.scoringObj[`${item.id}_${it.id}`].score) {
        uni.showToast({
            title: "已到最大值",
            icon: "none"
        })
        return
    }
    // 如果小于0就设置为1 否则就加一
    if (state.scoringObj[`${item.id}_${it.id}`].score == 0 && minScore <= 0) {
        state.scoringObj[`${item.id}_${it.id}`].score = 1
        return
    }
    if (state.scoringObj[`${item.id}_${it.id}`].score == 0.0) {
        // （你的分数+其他人评价分数总和）/ 总人数
        state.scoringObj[`${item.id}_${it.id}`].score = minScore
        state.scoringObj[`${item.id}_${it.id}`].othersTotalScore = othersTotalScore
        state.scoringObj[`${item.id}_${it.id}`].evaluatePersonNum = evaluatePersonNum
    } else {
        state.scoringObj[`${item.id}_${it.id}`].score++
    }
}
// 手动输入
const handlerInput = (event, item, it) => {
    const _score = parseInt(event)
    const { minScore, maxScore } = it.indicatorScore
    if (_score < minScore) {
        state.scoringObj[`${item.id}_${it.id}`].score = minScore
        return
    }
    if (_score > maxScore) {
        state.scoringObj[`${item.id}_${it.id}`].score = maxScore
        return
    }
}

const getRulePersonScoreListInfo = () => {
    const { activityId, toPersonId, id } = queryParams.value
    const params = {
        activityId,
        toPersonId,
        rulePersonId: id,
        queryThisFrom: true
    }
    http.post("/app/evalDayRulePerson/getRulePersonScoreList", params).then(({ data }) => {
        state.participants = data
        data.forEach((v) => {
            v.secondIndicators.forEach((j) => {
                state.scoringObj[`${v.id}_${j.id}`] = {
                    id: j.id,
                    score: j.indicatorScore.minScore,
                    othersTotalScore: 0,
                    evaluatePersonNum: 0
                }
            })
        })
    })
}
// 提交评价活动评分
const handlerSave = () => {
    const params = {
        scoringList: Object.values(state.scoringObj)
    }
    state.loading = true
    http.post("/app/evalDayRulePerson/evalAddScoring", params)
        .then(({ message }) => {
            uni.showToast({
                title: message
            })
            uni.navigateBack()
        })
        .finally(() => {
            state.loading = false
        })
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    queryParams.value = options
    getRulePersonScoreListInfo()
})
</script>

<style lang="scss" scoped>
:deep(.uni-input-input) {
    text-align: center;
}

.participants {
    min-height: calc(100vh - 148rpx);
    background: $uni-bg-color-grey;
    padding: 30rpx;
    padding-bottom: 200rpx;

    .user_info {
        padding: 40rpx;
        min-height: 36rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        display: flex;
        justify-content: space-between;
        position: relative;

        .left {
            display: flex;
            flex: 1;

            .avatar {
                width: 100rpx;
                height: 100rpx;
                background: $uni-color-primary;
                border-radius: 50%;
                text-align: center;
                position: relative;
                font-weight: 600;
                font-size: 47rpx;
                color: $uni-bg-color;
                line-height: 100rpx;

                .avatar_img {
                    width: 100rpx;
                    border-radius: 50%;
                    height: 100rpx;
                }
            }

            .mask_layer {
                width: 100rpx;
                height: 100rpx;
                position: absolute;
                top: 40rpx;
                border-radius: 50%;
                left: 40rpx;
                background-color: $uni-bg-color-mask;
                font-weight: 400;
                font-size: 26rpx;
                color: $uni-bg-color;
                line-height: 100rpx;
                text-align: center;
            }

            .info {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                margin-left: 30rpx;
                flex: 1;

                .name {
                    font-weight: 600;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                .class_dept {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    padding-top: 10rpx;
                }
            }
        }

        .right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: flex-end;
            flex: 1;
            max-width: 150rpx;

            .num {
                font-size: 36rpx;
                color: $uni-text-color;
                line-height: 50rpx;
            }

            .text {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
            }
        }
    }

    .evaluate {
        margin-top: 20rpx;
        padding: 30rpx;
        min-height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;

        .evaluate_title_num {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }

        .rule {
            margin-top: 40rpx;

            .rule_title_box {
                display: flex;
                align-items: center;

                .rule_img {
                    margin-right: 10rpx;
                    width: 40rpx;
                    height: 40rpx;
                }

                .title {
                    font-weight: 500;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }
            }

            .rule_item {
                display: flex;
                flex-direction: column;
                margin-top: 30rpx;

                .split_line {
                    height: 1rpx;
                    background: $uni-border-color;
                    margin: 40rpx 0rpx;
                }

                .item_title {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-color-primary;
                    line-height: 40rpx;
                }

                .scoring_range {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    margin: 20rpx 0rpx 30rpx 0rpx;
                    line-height: 40rpx;
                }

                .num_input_box {
                    height: 88rpx;
                    background: $uni-bg-color;
                    border-radius: 10rpx;
                    border: 2rpx solid $uni-border-color;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 0rpx 20rpx;
                    margin-bottom: 20rpx;

                    .btn {
                        width: 48rpx;
                        height: 48rpx;

                        .btn_img {
                            width: 48rpx;
                            height: 48rpx;
                        }
                    }
                }

                .score_box {
                    display: flex;
                    justify-content: space-between;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                    margin: 10rpx 0rpx;

                    .score {
                        color: $uni-text-color;
                    }
                }
            }
        }
    }

    .evaluate_footer {
        padding: 0rpx 30rpx;
        padding-bottom: 30rpx;
        height: 126rpx;
        background: $uni-bg-color;
        position: fixed;
        bottom: 0;
        left: 0;
        width: calc(100vw - 60rpx);
        display: flex;
        justify-content: space-between;
        align-items: center;

        .aggregate_score {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;

            .score {
                font-weight: 600;
                font-size: 36rpx;
                color: $uni-color-primary;
                line-height: 50rpx;
            }
        }

        .footer_btn {
            width: 156rpx;
            height: 80rpx;
            background: $uni-color-primary;
            text-align: center;
            border-radius: 10rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-bg-color;
            line-height: 80rpx;
        }
    }
}
</style>

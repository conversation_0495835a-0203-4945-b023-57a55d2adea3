<template>
    <div class="evaluation_record">
        <z-paging ref="paging" v-model="state.evaluationRecordList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="评价档案"> </uni-nav-bar>
                <div class="tabs_class">
                    <div class="tabs student" :class="teacherStudentSwitch == 'student' ? 'active' : ''" @click="teacherStudentSwitch = 'student'">学生</div>
                    <div class="tabs teacher" :class="teacherStudentSwitch == 'teacher' ? 'active' : ''" @click="teacherStudentSwitch = 'teacher'">老师</div>
                </div>
                <view class="set-input">
                    <uni-easyinput v-model="state.toPersonName" :clearable="false" placeholder="搜索姓名" @change="searchFn"></uni-easyinput>
                </view>
                <div class="statistics">
                    <div class="statistics_img">
                        <image mode="aspectFill" class="img" src="@nginx/workbench/evalActivity/teacher/statistics_logo.png" alt="" />
                    </div>
                    <div class="statistics_content">
                        <div class="content" v-for="item in state.statisticsList" :key="item.code">
                            <image mode="aspectFill" class="type_logo" :src="item.logo" alt="" />
                            <span class="label">{{ item.label }}</span>
                            <span class="value">{{ item.value || 0 }}</span>
                        </div>
                    </div>
                </div>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div v-if="state.evaluationRecordList?.length" class="record_list">
                <view class="item" v-for="item in state.evaluationRecordList" :key="item.id">
                    <div class="title_box">
                        <div class="left">
                            <div class="avatar">
                                <image mode="aspectFill" v-if="item.avatar" class="avatar_img" :src="queryParams.avatar" alt="" />
                                <span v-else>{{ item.toPersonName?.slice(0, 1) }}</span>
                            </div>
                            <div class="info">
                                <div class="name">{{ item.toPersonName }}</div>
                                <div class="class_dept">{{ item.classesName }}</div>
                            </div>
                        </div>
                        <div class="right" @click="checkEvaluation(item)">
                            <uni-icons class="icon_class" type="right" size="18"></uni-icons>
                        </div>
                    </div>
                    <div class="split_line"></div>
                    <div class="record_content">
                        <span class="label">总获得积分：</span>
                        <span class="value">{{ item.totalScore }}</span>
                    </div>
                    <div class="record_content">
                        <span class="label">参与次数：</span>
                        <span class="value">{{ item.partakeCount }}</span>
                    </div>
                    <div class="record_content">
                        <span class="label">活动名称：</span>
                        <span class="value color_class ellipsis">{{ item.title }}</span>
                    </div>
                    <div class="record_content">
                        <span class="label">最近参与时间：</span>
                        <span class="value">{{ item.scoreTime }}</span>
                    </div>
                </view>
            </div>
        </z-paging>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const evalTypeId = ref("")

const paging = ref(null)
const teacherStudentSwitch = ref("student")
const state = reactive({
    toPersonName: "",
    evaluationRecordList: [],
    statisticsList: [
        {
            value: 0,
            label: "年级数：",
            code: "gradeCount",
            logo: "@nginx/workbench/evalActivity/teacher/grade_num.png"
        },
        {
            value: 0,
            label: "班级数：",
            code: "classesCount",
            logo: "@nginx/workbench/evalActivity/teacher/classes_num.png"
        },
        {
            value: 0,
            label: "总人数：",
            code: "personCount",
            logo: "@nginx/workbench/evalActivity/teacher/people_total.png"
        },
        {
            value: 0,
            label: "评价总数：",
            code: "evalCount",
            logo: "@nginx/workbench/evalActivity/teacher/evaluate_total.png"
        },
        {
            value: 0,
            label: "总积分：",
            code: "scoreCount",
            logo: "@nginx/workbench/evalActivity/teacher/score_total.png"
        }
    ]
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        pageNo,
        pageSize,
        toPersonName: state.toPersonName,
        identity: teacherStudentSwitch.value == "student" ? 0 : 1,
        evalTypeId: evalTypeId.value
    }
    http.post("/app/evalDayRulePerson/countRulePersonPage", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}

function checkEvaluation(item) {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/checkEvaluation",
        query: { ...item, isShowActivity: true }
    })
}

const searchFn = () => {
    paging.value.reload()
}

// 获取活动记录参与数据统计
const getRulePersonCountInfo = () => {
    const params = {
        identity: teacherStudentSwitch.value == "student" ? 0 : 1,
        evalTypeId: evalTypeId.value
    }
    http.get("/app/evalDayRulePerson/getRulePersonCount", params).then(({ data }) => {
        state.statisticsList = state.statisticsList.map((v) => {
            return {
                ...v,
                value: data[v.code] || 0
            }
        })
    })
}

function clickLeft() {
    uni.navigateBack()
}

watch(
    () => teacherStudentSwitch.value,
    (val, oldVal) => {
        if (val !== oldVal) {
            getRulePersonCountInfo()
            paging.value.reload()
        }
    }
)
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options?.evaluation_record || ""
    getRulePersonCountInfo()
})
</script>

<style lang="scss" scoped>
.evaluation_record {
    min-height: calc(100vh - 88rpx);
    background: $uni-bg-color-grey;

    .tabs_class {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60rpx;
        padding-top: 30rpx;
        background: $uni-bg-color;

        .tabs {
            height: 56rpx;
            width: 150rpx;
            border: 1rpx solid $uni-color-primary;
            text-align: center;
            line-height: 56rpx;
        }

        .student {
            border-radius: 32rpx 0rpx 0rpx 32rpx;
            color: $uni-color-primary;
        }

        .teacher {
            border-radius: 0rpx 32rpx 32rpx 0rpx;
            color: $uni-color-primary;
        }

        .active {
            color: $uni-bg-color;
            background: $uni-color-primary;
        }
    }

    .set-input {
        background: $uni-bg-color;
        padding: 30rpx;

        :deep(.is-input-border) {
            border: none;
        }

        :deep(.uni-easyinput__content-input) {
            border-radius: 30rpx;
            background: $uni-bg-color-grey;
        }
    }

    .statistics {
        background: $uni-bg-color;
        justify-content: center;
        padding: 30rpx 0rpx;
        display: flex;

        .statistics_img {
            margin-right: 45rpx;
            height: 278rpx;
            width: 294rpx;

            .img {
                width: 100%;
                height: 100%;
            }
        }

        .statistics_content {
            height: 278rpx;
            min-width: 194rpx;
            margin-left: 45rpx;

            .content {
                display: flex;
                align-items: center;
                margin: 10rpx 0rpx;
                width: 100%;

                .type_logo {
                    width: 24rpx;
                    height: 24rpx;
                }

                .label {
                    margin-left: 22rpx;
                    width: 130rpx;
                    text-align: left;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: #666666;
                    line-height: 36rpx;
                }

                .value {
                    text-align: right;
                    font-weight: 600;
                    font-size: 26rpx;
                    color: $uni-text-color;
                    line-height: 36rpx;
                }
            }
        }
    }

    .record_list {
        padding: 20rpx 30rpx;

        .item {
            min-height: 230rpx;
            background: $uni-bg-color;
            border-radius: 10rpx;
            padding: 30rpx;
            margin-bottom: 20rpx;

            .title_box {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;

                    .avatar {
                        width: 48rpx;
                        height: 48rpx;
                        background: $uni-color-primary;
                        border-radius: 50%;
                        text-align: center;
                        position: relative;
                        font-weight: 600;
                        font-size: 22rpx;
                        color: $uni-bg-color;
                        line-height: 32rpx;

                        .avatar_img {
                            width: 48rpx;
                            border-radius: 50%;
                            height: 48rpx;
                        }
                    }

                    .info {
                        display: flex;
                        justify-content: space-around;
                        align-items: center;
                        margin-left: 10rpx;

                        .name {
                            font-weight: 600;
                            font-size: 30rpx;
                            color: $uni-text-color;
                            line-height: 42rpx;
                            padding-right: 20rpx;
                        }

                        .class_dept {
                            font-weight: 400;
                            font-size: 26rpx;
                            color: $uni-text-color;
                            line-height: 36rpx;
                        }
                    }
                }
            }

            .split_line {
                height: 1rpx;
                background: $uni-border-color;
                width: 100%;
                margin: 30rpx 0rpx 20rpx 0rpx;
            }

            .record_content {
                display: flex;
                margin: 10rpx 0rpx;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
                align-items: center;

                .label {
                    min-width: 160rpx;
                }

                .value {
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    text-align: right;
                }

                .color_class {
                    color: $uni-color-primary;
                }
            }
        }
    }
}
</style>

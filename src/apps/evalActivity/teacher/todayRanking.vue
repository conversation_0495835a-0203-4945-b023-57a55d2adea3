<template>
    <div class="today_ranking">
        <z-paging ref="paging" v-model="state.rankingList" @query="queryList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="今日得分排行"> </uni-nav-bar>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
            <div v-if="state.rankingList?.length" class="ranking_list">
                <div
                    class="item"
                    v-for="(item, index) in state.rankingList"
                    :key="item.toPersonId"
                    :style="{
                        background: myBackground(index + 1)
                    }"
                >
                    <div class="left">
                        <div class="medal">
                            <image class="medal_img" v-if="index + 1 == 1" src="@nginx/workbench/evalActivity/teacher/one_ranking.png" alt />
                            <image v-else-if="index + 1 == 2" class="medal_img" src="@nginx/workbench/evalActivity/teacher/two_ranking.png" alt />
                            <image v-else-if="index + 1 == 3" class="medal_img" src="@nginx/workbench/evalActivity/teacher/three_ranking.png" alt />
                            <div class="index" v-else>{{ index + 1 }}</div>
                        </div>
                        <div class="avatar" v-if="item.avatar">
                            <image mode="aspectFill" class="avatar_img" :src="item.avatar" alt="" />
                        </div>
                        <div class="name">{{ item.toPersonName }}</div>
                        <div class="class_dept">（{{ item.orgName }}）</div>
                    </div>
                    <div class="right">
                        <div class="num">{{ item.maxScore }}分</div>
                    </div>
                </div>
            </div>
        </z-paging>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const evalTypeId = ref("")
const paging = ref(null)
const state = reactive({
    rankingList: []
})

const myBackground = computed(() => {
    return (index) => {
        const color = {
            1: "#FEF6DB",
            2: "#E9F1FF",
            3: "#FFF1E9"
        }
        if ([1, 2, 3].includes(index)) {
            return color[index]
        } else {
            return "#F0F2F5"
        }
    }
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        pageNo,
        pageSize,
        evalTypeId: evalTypeId.value,
        identity: "0"
    }
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/app/evalDayRulePerson/dayScoreMaxCountPage ", params)
        .then(({ data }) => {
            paging.value.complete(data.list)
        })
        .finally(() => {
            uni.hideLoading()
        })
}

function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options.evalTypeId || ""
})
</script>

<style lang="scss" scoped>
.today_ranking {
    min-height: calc(100vh - 88rpx);
    background: $uni-bg-color-grey;

    .ranking_list {
        margin-top: 20rpx;
        padding: 30rpx;
        min-height: 100rpx;
        background: $uni-bg-color;

        .item {
            height: 60rpx;
            border-radius: 50rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20rpx 30rpx;
            margin-bottom: 30rpx;

            .left {
                align-items: center;
                display: flex;

                .medal {
                    width: 48rpx;
                    height: 60rpx;
                    margin-right: 30rpx;

                    .medal_img {
                        width: 48rpx;
                        height: 60rpx;
                    }

                    .index {
                        width: 48rpx;
                        line-height: 60rpx;
                        font-size: 36rpx;
                        color: $uni-text-color-grey;
                        text-align: center;
                    }
                }

                .avatar {
                    width: 40rpx;
                    height: 40rpx;
                    background: $uni-color-primary;
                    border-radius: 50%;
                    text-align: center;
                    margin-right: 20rpx;

                    .avatar_img {
                        width: 40rpx;
                        height: 40rpx;
                        border-radius: 50%;
                    }
                }

                .name {
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                }

                .class_dept {
                    font-size: 28rpx;
                    color: $uni-text-color-grey;
                    line-height: 40rpx;
                }
            }

            .right {
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;
            }
        }
    }
}
</style>

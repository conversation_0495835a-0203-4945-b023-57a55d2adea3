<template>
    <div class="evaluate_home">
        <!-- 背景 -->
        <div class="background_img">
            <image class="evaluate_ban" mode="widthFix" src="@nginx/workbench/evalActivity/teacher/evaluateBan.png" alt="" />
        </div>
        <div class="evaluate_content">
            <!-- 顶部 -->
            <div class="top_nav_bar">
                <uni-icons @click="routerBack" class="icon_class" type="left" size="22"></uni-icons>
                <div class="bar_title">评价</div>
            </div>
            <!-- 模块入口 -->
            <div class="module_list">
                <div @click="handlerModular(item)" v-for="item in moduleList" :key="item.code" class="module_item">
                    <image class="img" :src="item.img" alt="" />
                    <span class="title">{{ item.title }}</span>
                    <uni-icons class="icon_class" type="right" size="18"></uni-icons>
                </div>
            </div>
            <!-- 统计 -->
            <div class="statistics">
                <div class="statistics_item" :class="item.code == 'medal' ? 'border' : ''" v-for="item in statisticsList" :key="item.code">
                    <span>{{ item.title }}</span>
                    <span class="num">{{ item.num }}</span>
                </div>
            </div>
            <!-- 今日得分排行 -->
            <day-ranking />
            <!-- 参与评价占比  -->
            <proportion />
            <!-- 评价活动 -->
            <activity v-model:activityParams="state.add_evaluation" />
        </div>
    </div>
</template>

<script setup>
import DayRanking from "./dayRanking.vue"
import Proportion from "./proportion.vue"
import Activity from "./activity.vue"
import { onLoad } from "@dcloudio/uni-app"

const moduleList = [
    {
        url: "/apps/evalActivity/immediateEvaluation/index",
        img: "@nginx/workbench/evalActivity/teacher/add_evaluation.png",
        title: "立即评价",
        code: "add_evaluation"
    },
    {
        url: "/apps/evalActivity/teacher/issueMedals",
        img: "@nginx/workbench/evalActivity/teacher/issue_medals.png",
        title: "发放勋章",
        code: "issue_medals"
    },
    {
        url: "/apps/evalActivity/teacher/evaluationRecord",
        img: "@nginx/workbench/evalActivity/teacher/evaluation_record.png",
        title: "评价记录",
        code: "evaluation_record"
    },
    {
        url: "/apps/evalActivity/teacher/medalRecord",
        img: "@nginx/workbench/evalActivity/teacher/medal_record.png",
        title: "勋章记录",
        code: "medal_record"
    }
]
const statisticsList = ref([
    {
        title: "兑换总次数",
        code: "exchangeCount",
        num: 0
    },
    {
        title: "勋章总数",
        code: "medalCount",
        num: 0
    },
    {
        title: "积分卡总数",
        code: "scoreCardCount",
        num: 0
    }
])
const evalTypeId = ref("")
const state = reactive({
    add_evaluation: {},
    issue_medals: null,
    evaluation_record: {},
    medal_record: {}
})

const handlerModular = (item) => {
    let params = {}
    // 立即评价
    if (item.code == "add_evaluation") {
        // 立即评价
        params = state[item.code]
    } else if (item.code == "issue_medals") {
        // 发放勋章
        params = { issue_medals: evalTypeId.value }
    } else if (item.code == "medal_record") {
        // 勋章记录
        params = { medal_record: evalTypeId.value }
    } else {
        // 评价记录
        params = { evaluation_record: evalTypeId.value }
    }
    navigateTo({
        url: item.url,
        query: params
    })
}
// 获取兑换次数、勋章总数、积分卡总数
const getEmsCountInfo = () => {
    http.post("/app/evalStatistic/getEmsCount", { evalTypeId: evalTypeId.value }).then(({ data }) => {
        statisticsList.value.forEach((v) => {
            v.num = data[v.code]
        })
    })
}
const getEvaluateInfo = () => {
    const params = {
        pageNo: 1,
        pageSize: 1,
        homePage: true,
        evalTypeId: evalTypeId.value
    }
    http.post("/app/evalActivity/page", params).then(({ data }) => {
        if (data.list.length) {
            state.add_evaluation = data.list[0]
        }
    })
}

onLoad((item) => {
    evalTypeId.value = item.code
    getEmsCountInfo()
    // 评价活动
    getEvaluateInfo()
})
</script>

<style lang="scss" scoped>
.evaluate_home {
    min-height: 100vh;
    background: #f0f2f5;
    width: 100%;
    // position: relative;

    .background_img {
        .evaluate_ban {
            width: 100vw;
            left: 0;
            position: absolute;
            top: 0;
        }
    }

    .evaluate_content {
        position: relative;
        width: calc(100% - 60rpx);
        min-height: 100vh;
        padding: 0rpx 30rpx;
        padding-bottom: 40rpx;
        // #ifdef MP-WEIXIN || APP-PLUS
        padding-top: var(--status-bar-height);
        // #endif
        .top_nav_bar {
            width: 100%;
            height: 120rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .icon_class {
                position: absolute;
                left: 30rpx;
            }

            .bar_title {
                font-weight: 500;
                font-size: 34rpx;
                color: $uni-text-color;
                line-height: 48rpx;
            }
        }
    }
}

.module_list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .module_item {
        padding: 20rpx;
        width: 290rpx;
        height: 100rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        box-shadow: 10rpx 10rpx 5rpx #c9ece3;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;

        .img {
            width: 116rpx;
            height: 116rpx;
        }

        .title {
            font-weight: 600;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }

        .icon_class {
            line-height: 40rpx;
            font-weight: 600;
            color: $uni-text-color;
        }
    }
}

.statistics {
    height: 120rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    display: flex;
    padding: 30rpx 0rpx;
    margin-bottom: 20rpx;

    .statistics_item {
        width: 30%;
        padding: 0rpx 30rpx;
        display: flex;
        flex-direction: column;
        font-weight: 400;
        font-size: 28rpx;
        color: #00000073;
        line-height: 40rpx;

        .num {
            font-family: Helvetica;
            font-size: 40rpx;
            color: $uni-text-color;
            padding-top: 12rpx;
            line-height: 48rpx;
        }
    }

    .border {
        border-left: 1rpx solid $uni-border-color;
        border-right: 1rpx solid $uni-border-color;
    }
}
</style>

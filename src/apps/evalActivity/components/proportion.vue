<template>
    <!-- 参与评价占比 -->
    <div class="proportion">
        <div class="proportion_title">
            <div class="left">
                <image src="@nginx/workbench/evalActivity/teacher/proportion_logo.png" class="proportion_logo" alt="" />
                <span class="text">参与评价占比</span>
            </div>
        </div>
        <div class="tabs_class">
            <div class="tabs student" :class="active == 'student' ? 'active' : ''" @click="active = 'student'">学生</div>
            <div class="tabs teacher" :class="active == 'teacher' ? 'active' : ''" @click="active = 'teacher'">老师</div>
        </div>
        <qiun-data-charts type="ring" v-if="state.chartData?.categories?.length" :opts="chartOpts" :chartData="state.chartData" />
        <yd-empty :isMargin="true" text="暂无数据" v-else />
    </div>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
import { onLoad } from "@dcloudio/uni-app"

const evalTypeId = ref("")
const active = ref("student")

const chartOpts = {
    backgroundColor: "#ffffff",
    dataLabel: false, // 关闭数据标签，这会去掉饼状图上的文字和百分比
    legend: {
        show: true,
        position: "right", // 将图例位置设置为右侧
        float: "center", // 在右侧居中显示
        margin: 5,
        itemGap: 10,
        itemWidth: 18,
        itemHeight: 10,
        columnNum: 2, // 每行显示两个图例项
        textStyle: {
            fontSize: 12,
            color: "#666666"
        }
    },
    title: {
        name: "",
        fontSize: 15,
        color: "#666666",
        offsetY: 20
    },
    subtitle: {
        name: "",
        fontSize: 12,
        color: "#666666"
    },
    extra: {
        ring: {
            ringWidth: 20, // 环形图的宽度
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0
        }
    }
}
const state = reactive({
    chartData: {}
})

const listEvalPersonPortionInfo = () => {
    const params = {
        evalTypeId: evalTypeId.value,
        identity: active.value == "teacher" ? 1 : 0
    }
    uni.showLoading({
        title: "加载中..."
    })
    http.post("/app/evalStatistic/listEvalPersonPortion", params)
        .then(({ data }) => {
            const _chartData = {
                categories: [],
                series: []
            }
            data?.forEach((item) => {
                _chartData.categories.push(item.objName)
                const _portion = (item.portion * 100).toFixed(2)
                // 保留两位小数
                _chartData.series.push({
                    name: `${item.objName.length > 5 ? item.objName.slice(0, 5) + "..." : item.objName} ${_portion}%`,
                    data: item.portion
                })
            })
            state.chartData = _chartData || JSON.parse(JSON.stringify(_chartData))
        })
        .finally(() => {
            uni.hideLoading()
        })
}
watch(
    () => active.value,
    () => {
        listEvalPersonPortionInfo()
    }
)
onLoad((item) => {
    evalTypeId.value = item.code
    listEvalPersonPortionInfo()
})
</script>

<style lang="scss" scoped>
.proportion {
    width: calc(100% - 60rpx);
    min-height: 530rpx;
    padding: 30rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    margin-top: 20rpx;

    .proportion_title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;

            .proportion_logo {
                width: 40rpx;
                height: 40rpx;
                margin-right: 20rpx;
            }
        }
    }

    .tabs_class {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60rpx;
        margin-top: 40rpx;

        .tabs {
            height: 56rpx;
            width: 150rpx;
            border: 1rpx solid $uni-color-primary;
            text-align: center;
            line-height: 56rpx;
        }

        .student {
            border-radius: 32rpx 0rpx 0rpx 32rpx;
            color: $uni-color-primary;
        }

        .teacher {
            border-radius: 0rpx 32rpx 32rpx 0rpx;
            color: $uni-color-primary;
        }

        .active {
            color: $uni-bg-color;
            background: $uni-color-primary;
        }
    }
}
</style>

<template>
    <div class="activity">
        <div class="activity_title">
            <div class="left">
                <image src="@nginx/workbench/evalActivity/teacher/activity_logo.png" class="activity_logo" alt="" />
                <span class="text">评价活动</span>
            </div>
            <div class="right" @click="evaluationActivity">
                <span>全部</span>
                <uni-icons class="icon_class" type="right" size="14"></uni-icons>
            </div>
        </div>
        <div class="activity_item">
            <view @click="handlerDetails('evaluationInfo')">
                <view class="title_box">
                    <view class="label" :style="{ background: activityStatus[state.activity.status].color }">
                        {{ activityStatus[state.activity.status].name }}
                    </view>
                    <view class="title ellipsis">
                        {{ state.activity.title }}
                    </view>
                </view>
                <div class="info_box">
                    <div class="date">
                        <template v-if="state.activity.endDate"> {{ state.activity.startDate }} - {{ state.activity.endDate }} </template>
                    </div>
                    <div class="participate_num">参与人数：{{ state.activity.partakePersonNum }}人</div>
                </div>
                <div class="info_box">
                    <div>{{ state.activity.names?.join("、") || "" }}</div>
                </div>
            </view>

            <div class="split_line"></div>
            <div class="click_text" @click="handlerDetails('index')">立即评价</div>
        </div>
    </div>
</template>

<script setup>
const activityStatus = {
    0: { name: "未开始", color: "#FFFFBB37" },
    1: { name: "进行中", color: "#00b781" },
    2: { name: "已结束", color: "#595959" }
}
const props = defineProps({
    activityParams: {
        type: Object,
        default: () => {}
    }
})
const emit = defineEmits(["update:activityParams"])
const state = reactive({
    activity: {
        title: "",
        names: [],
        status: 0, // (0.未开始 1.进行中 2.已结束)
        startDate: "",
        endDate: "",
        description: "",
        partakePersonNum: 0
    }
})

// 评价详情
const handlerDetails = (item) => {
    let url = "/apps/evalActivity/immediateEvaluation/index"
    if (item == "evaluationInfo") {
        // 立即评价
        url = "/apps/evalActivity/immediateEvaluation/evaluationInfo"
    }
    navigateTo({
        url,
        query: props.activityParams
    })
}

function evaluationActivity() {
    navigateTo({
        url: "/apps/evalActivity/immediateEvaluation/evaluationActivity",
        query: props.activityParams
    })
}

watch(
    () => props.activityParams,
    () => {
        for (let key in state.activity) {
            state.activity[key] = props.activityParams[key]
        }
    }
)
</script>

<style lang="scss" scoped>
.activity {
    width: calc(100% - 60rpx);
    min-height: 270rpx;
    padding: 30rpx;
    background: $uni-bg-color;
    border-radius: 20rpx;
    margin: 20rpx 0rpx;

    .activity_title {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left,
        .right {
            display: flex;
            align-items: center;
        }

        .left {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;

            .activity_logo {
                width: 40rpx;
                height: 40rpx;
                margin-right: 20rpx;
            }
        }

        .right {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;

            .icon_class {
                color: #8c8c8c;
            }
        }
    }

    .activity_item {
        width: 100%;
        margin-top: 28rpx;

        .title_box {
            display: flex;
            align-items: center;

            .title {
                font-weight: 500;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
                flex: 1;
            }

            .label {
                padding: 0rpx 4rpx;
                text-align: center;
                height: 36rpx;
                background: $uni-color-primary;
                border-radius: 6rpx;
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-bg-color;
                line-height: 36rpx;
                margin-right: 10rpx;
            }
        }

        .info_box {
            margin-top: 18rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 36rpx;
        }

        .split_line {
            height: 1rpx;
            background: #d9d9d9;
            width: 100%;
            margin: 30rpx 0rpx;
        }

        .click_text {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-color-primary;
            line-height: 40rpx;
            text-align: center;
        }
    }
}
</style>

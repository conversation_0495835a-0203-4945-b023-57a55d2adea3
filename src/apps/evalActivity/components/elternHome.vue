<template>
    <div class="home">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="劳动评价" :rightWidth="120" :leftWidth="120">
            <!-- #ifdef H5-WEIXIN || H5 -->
            <template v-slot:right>
                <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                    <span class="student_name">{{ state.studentName }}</span>
                    <image class="select_img" src="@nginx/components/select_child.png" />
                </div>
            </template>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <template v-slot:right>
                <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                    <span class="student_name">{{ state.studentName }}</span>
                    <image class="select_img" src="@nginx/components/select_child.png" />
                </div>
            </template>
            <!-- #endif -->
        </uni-nav-bar>
        <div class="page_content">
            <!-- #ifdef MP-WEIXIN -->
            <div class="bar_right" @click="changeStudent" v-if="state.selectList.length > 1">
                <span class="student_name">{{ state.studentName }}</span>
                <image class="select_img" src="@nginx/components/select_child.png" />
            </div>
            <!-- #endif -->
            <!-- 顶部卡片 -->
            <div class="top_card">
                <div class="item_card" @click="handlerDetailsPage(item.code)" v-for="(item, index) in cardList" :key="index">
                    <div class="img_box">
                        <image mode="widthFix" class="img" :src="item.imgBg" alt="" />
                    </div>
                    <div class="text_box">
                        <div class="title">
                            {{ item.title }}
                            <uni-icons class="icon_class" type="right" size="14" color="#fff"></uni-icons>
                        </div>
                        <div class="number">
                            {{ item.number }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- 评价活动 -->
            <div class="evaluation_activities" @click="gotoActivity">
                <image mode="widthFix" class="image" src="@nginx/workbench/evalActivity/parent/evaluation_activities.png" alt="" />
            </div>
            <div class="page_list">
                <!-- 标题 -->
                <div class="list_title">
                    <div class="current_score">
                        <img class="current_score_img" src="@nginx/workbench/evalActivity/parent/current_score.png" alt="" />
                        <span>当前得分</span>
                    </div>
                    <div class="all">
                        <span>全部</span>
                        <uni-icons class="icon_class" type="right" size="14" color="#8C8C8C"></uni-icons>
                    </div>
                </div>
                <z-paging ref="paging" v-model="state.laborList" @query="queryList" use-page-scroll>
                    <template #empty>
                        <yd-empty text="暂无数据" />
                    </template>
                    <!-- 数据 -->
                    <div class="list_item" v-for="(item, index) in state.laborList" :key="index">
                        <div class="item_text_box">
                            <span class="activities_title">{{ item.evalTypeName }}</span>
                            <span class="score">{{ item.score > 0 ? `+${item.score}` : item.score }}</span>
                        </div>
                        <div class="item_text_box">
                            <span class="date">{{ item.createTime }}</span>
                            <span class="all_score">
                                累计积分：<span class="total_score">{{ item.totalScore }}</span>
                            </span>
                        </div>
                    </div>
                </z-paging>
            </div>
        </div>
        <yd-select-popup :fieldNames="{ value: 'studentId', label: 'studentName' }" ref="selectPopupRef" title="请选择学生" :list="state.selectList" @closePopup="closePopup" :selectId="[state.studentId]"></yd-select-popup>
    </div>
</template>

<script setup>
import useStore from "@/store"
import { onLoad } from "@dcloudio/uni-app"

const { user } = useStore()
const paging = ref(null)
const selectPopupRef = ref(null)
const cardList = ref([
    {
        imgBg: "@nginx/workbench/evalActivity/parent/integration.png",
        title: "积分",
        number: 0,
        code: "totalScore"
    },
    {
        imgBg: "@nginx/workbench/evalActivity/parent/loyalty_card.png",
        title: "积分卡",
        number: 0,
        code: "scoreCardCount"
    },
    {
        imgBg: "@nginx/workbench/evalActivity/parent/medal.png",
        title: "勋章",
        number: 0,
        code: "medalCount"
    },
    {
        imgBg: "@nginx/workbench/evalActivity/parent/redeemed.png",
        title: "已兑换",
        number: 0,
        code: "exchangeCount"
    }
])
const state = reactive({
    laborList: [],
    selectList: [],
    studentName: "",
    studentId: "",
    evalTypeId: ""
})

function barLeft() {
    uni.navigateBack()
}
// 调用List数据
function queryList(pageNo, pageSize) {
    if (state.studentId) {
        const params = {
            pageNo,
            pageSize,
            evalTypeId: state.evalTypeId,
            personId: state.studentId,
            identity: 0
        }
        uni.showLoading({
            title: "加载中..."
        })
        http.post("/app/evalScoreRecord/page", params)
            .then(({ data }) => {
                paging.value?.complete(data.list || false)
            })
            .finally(() => {
                uni.hideLoading()
            })
    } else {
        paging.value.complete(false)
    }
}

const closePopup = (val) => {
    if (!val) return
    state.studentName = val.studentName
    state.studentId = val.studentId
}

// 选择学生
function changeStudent() {
    selectPopupRef.value.open()
}
// 获取兑换次数、勋章总数、积分卡总数
const getEmsCountInfo = () => {
    const params = {
        evalTypeId: state.evalTypeId,
        personId: state.studentId,
        identity: 0
    }
    http.post("/app/evalStatistic/getUserEmsCount", params).then(({ data }) => {
        cardList.value.forEach((v) => {
            v.number = data[v.code] || 0
        })
    })
}

const gotoActivity = () => {
    navigateTo({
        url: "/apps/evalActivity/eltern/activity",
        query: {
            evalTypeId: state.evalTypeId,
            personId: state.studentId
        }
    })
}

// 跳转页
const handlerDetailsPage = (code) => {
    const params = {
        type: code,
        evalTypeId: state.evalTypeId,
        personId: state.studentId
    }
    if (code === "exchangeCount") {
        navigateTo({
            url: "/apps/evalActivity/eltern/myExchange",
            query: params
        })
    } else {
        navigateTo({
            url: "/apps/evalActivity/eltern/detailsPage",
            query: params
        })
    }
}
watch(
    () => state.studentName,
    (val, oldVal) => {
        if (val !== oldVal) {
            getEmsCountInfo()
            paging.value.reload()
        }
    }
)
onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    state.selectList = user.studentInfo
    state.studentName = user.studentInfo[0]?.studentName
    state.studentId = user.studentInfo[0]?.studentId
    state.evalTypeId = options?.code || ""
    getEmsCountInfo()
})
</script>

<style lang="scss" scoped>
.bar_right {
    padding: 10rpx 0rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-color-primary;
    line-height: 40rpx;
    display: flex;
    justify-content: flex-end;

    .student_name {
        text-align: right;
    }

    .select_img {
        width: 44rpx;
        height: 44rpx;
        flex-shrink: 0;
    }
}
.home {
    .page_content {
        min-height: calc(100vh - 120rpx);
        background: $uni-bg-color-grey;
        padding: 16rpx 22rpx;
        max-height: calc(100vh - 120rpx);
        overflow-y: auto;

        .top_card {
            display: flex;
            flex-wrap: wrap;

            .item_card {
                flex: 1 0 50%;
                box-sizing: border-box;
                position: relative;
                height: 176rpx;

                .img_box {
                    position: absolute;
                    top: 0rpx;
                    left: 0;
                    width: 100%;
                    height: 176rpx;

                    .img {
                        width: 100%;
                        height: auto;
                    }
                }

                .text_box {
                    position: absolute;
                    top: 0rpx;
                    left: 0;
                    width: calc(100% - 76rpx);
                    height: 176rpx;
                    padding: 24rpx 38rpx;

                    .title {
                        font-size: 28rpx;
                        color: $uni-bg-color;
                        line-height: 40rpx;
                        display: flex;
                        align-items: center;
                    }

                    .number {
                        font-size: 40rpx;
                        color: $uni-bg-color;
                        line-height: 58rpx;
                        padding-top: 10rpx;
                    }
                }
            }
        }

        .evaluation_activities {
            width: 100%;
            margin: 8rpx 0rpx 10rpx 0rpx;

            .image {
                width: 100%;
                height: auto;
            }
        }

        .page_list {
            min-height: 192rpx;
            background: $uni-bg-color;
            border-radius: 20rpx;
            padding: 30rpx;

            .list_title {
                margin-bottom: 12rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .current_score {
                    display: flex;
                    align-items: center;
                    font-weight: 600;
                    font-size: 30rpx;
                    color: $uni-text-color;
                    line-height: 42rpx;

                    .current_score_img {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 10rpx;
                    }
                }

                .all {
                    display: flex;
                    align-items: center;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: $uni-text-color-grey;
                    line-height: 36rpx;
                }
            }

            .list_item {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 108rpx;
                padding: 30rpx 0rpx;
                border-bottom: 1rpx solid $uni-border-color;

                .item_text_box {
                    justify-content: space-between;
                    display: flex;
                    align-items: center;

                    .activities_title {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }

                    .score {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-color-primary;
                        line-height: 42rpx;
                    }

                    .date {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 36rpx;
                    }

                    .all_score {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color-grey;
                        line-height: 36rpx;
                        .total_score {
                            color: $uni-text-color;
                        }
                    }
                }
            }
        }
    }
}
</style>

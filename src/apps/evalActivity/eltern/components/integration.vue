<template>
    <div class="integration_com">
        <div class="statistics">
            <div class="statistics_item">
                <span class="number">{{ state.evalPersonScore.totalScore }}</span>
                <span class="text">累计获得积分</span>
            </div>
            <div class="split_line"></div>
            <div class="statistics_item">
                <span class="number">{{ state.evalPersonScore.convertedScore }}</span>
                <span class="text">已兑换积分</span>
            </div>
            <div class="split_line"></div>
            <div class="statistics_item">
                <span class="number">{{ state.evalPersonScore.remainingScore }}</span>
                <span class="text">剩余积分</span>
            </div>
        </div>
        <div class="bg_line"></div>
        <div class="integration_content">
            <div class="date_box">
                <span>得分时间</span>
                <span class="reset-datetime-picker">
                    <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="state.scoreStartEndTime" @change="calendarsConfirm" />
                    <uni-icons type="right" size="14" color="#8C8C8C"></uni-icons>
                </span>
            </div>
            <z-paging ref="paging" v-model="state.evalScoreRecordList" @query="queryList" :auto="false" use-page-scroll>
                <template #empty>
                    <yd-empty text="暂无数据" />
                </template>
                <div class="page_list">
                    <div class="list_item" v-for="(item, index) in state.evalScoreRecordList" :key="index">
                        <div class="item_text_box">
                            <span class="activities_title ellipsis">{{ item.evalTypeName || "-" }}</span>
                            <span class="score">{{ item.score > 0 ? `+${item.score}` : item.score }}</span>
                        </div>
                        <div class="item_text_box">
                            <span class="date">{{ item.createTime }}</span>
                            <span class="all_score">
                                累计积分：<span class="total_score">{{ item.totalScore }}</span>
                            </span>
                        </div>
                    </div>
                </div>
            </z-paging>
        </div>
    </div>
</template>

<script setup>
import { nextTick, watch } from "vue"

const props = defineProps({
    parame: {
        type: Object,
        default: () => {}
    }
})
const paramsIntegral = ref({})
const paging = ref(null)
const state = reactive({
    evalPersonScore: {
        convertedScore: 0,
        remainingScore: 0,
        totalScore: 0
    },
    evalScoreRecordList: [],
    scoreStartEndTime: []
})

// 调用List数据
function queryList(pageNo, pageSize) {
    const [scoreStartTime, scoreEndTime] = state.scoreStartEndTime
    const params = {
        scoreStartTime,
        scoreEndTime,
        ...paramsIntegral.value,
        identity: 0,
        pageNo,
        pageSize
    }

    http.post("/app/evalScoreRecord/page", params).then(({ data }) => {
        paging.value?.complete(data.list)
    })
}
// 获取用户积分详情
async function getEvalPersonScoreInfoFn() {
    const params = {
        ...paramsIntegral.value,
        identity: 0
    }
    await http.get("/app/evalPersonScore/getEvalPersonScoreInfo", params).then(({ data }) => {
        state.evalPersonScore = data
    })
}
// 得分时间筛选
const calendarsConfirm = () => {
    paging.value?.reload()
}

watch(
    () => props.parame,
    (value) => {
        if (value.personId) {
            paramsIntegral.value = value
            nextTick(async () => {
                await getEvalPersonScoreInfoFn()
                paging.value?.reload()
            })
        } else {
            paging.value?.complete(false)
        }
    },
    {
        immediate: true,
        deep: true
    }
)
</script>

<style lang="scss" scoped>
.integration_com {
    height: 100%;

    .statistics {
        display: flex;
        height: 146rpx;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20rpx;

        .split_line {
            width: 2rpx;
            height: 60rpx;
            background: #d7d7d7;
        }

        .statistics_item {
            width: 46%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-evenly;

            .number {
                font-size: 36rpx;
                color: #262626;
                line-height: 50rpx;
            }

            .text {
                font-weight: 400;
                font-size: 26rpx;
                color: rgba(0, 0, 0, 0.45);
                line-height: 36rpx;
            }
        }
    }

    .bg_line {
        width: 100%;
        height: 20rpx;
        background: $uni-bg-color-grey;
    }

    .integration_content {
        padding: 30rpx;

        .date_box {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            padding-bottom: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .reset-datetime-picker {
                display: flex;
                align-items: center;

                :deep(.uniui-calendar) {
                    display: none;
                }
            }
        }

        .page_list {
            min-height: 192rpx;
            background: $uni-bg-color;
            border-radius: 20rpx;

            .list_item {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                height: 108rpx;
                padding: 30rpx 0rpx;
                border-bottom: 1rpx solid $uni-border-color;

                .item_text_box {
                    justify-content: space-between;
                    display: flex;
                    align-items: center;

                    .activities_title {
                        font-weight: 600;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }

                    .score {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-color-primary;
                        line-height: 42rpx;
                    }

                    .date {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 36rpx;
                    }

                    .all_score {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color-grey;
                        line-height: 36rpx;

                        .total_score {
                            color: $uni-text-color;
                        }
                    }
                }
            }
        }
    }
}
</style>

<template>
    <z-paging>
        <view class="container_box">
            <view class="title_box">
                <view class="top">
                    <view class="l">
                        <img class="img" src="@nginx/workbench/schoolAssignment/kebiao.png" />
                        {{ state.classesTimetable.classesName }}
                    </view>
                </view>
                <view class="title">关联课程：{{ state.classesTimetable.timetableTime }} {{ state.classesTimetable.sequence }} {{ state.classesTimetable.startTime }} {{ state.classesTimetable.subjectName }}</view>
            </view>
            <view class="collapse_box">
                <view class="collapse_title">
                    <text>{{ state.title }}</text>
                </view>

                <view class="content">
                    <view v-html="state.content"></view>
                    <view class="img_box">
                        <img v-for="(item, index) in imgGroup" :key="index" :src="item" class="collapse_img" />
                    </view>
                </view>
            </view>
            <view class="mian_box">
                <view class="title">反馈</view>
                <view class="item_box">
                    <text>完成情况：</text>
                    <button :class="['item_btn', state.feedbackData.completion == item.value ? 'actice_btn' : '']" v-for="item in completion" :key="item.value" :disabled="state.feedbackData.isFeedback" @click="handleClick('completion', item)">{{ item.label }}</button>
                </view>
                <view class="item_box">
                    <text>难易程度：</text>
                    <button :class="['item_btn', state.feedbackData.difficulty == item.value ? 'actice_btn' : '']" v-for="item in difficulty" :key="item.value" :disabled="state.feedbackData.isFeedback" @click="handleClick('difficulty', item)">{{ item.label }}</button>
                </view>
            </view>
        </view>
        <template #bottom>
            <view class="footer">
                <button :class="['btn', state.feedbackData.isFeedback ? 'poiner_none' : '']" :disabled="disabled" @click="submit">{{ state.feedbackData.isFeedback ? "已提交反馈" : "提交反馈" }}</button>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const difficulty = [
    { label: "很简单", value: 1 },
    { label: "有难度", value: 2 },
    { label: "非常难", value: 3 }
]

const completion = [
    { label: "已完成", value: 1 },
    { label: "未完成", value: 2 }
]

const disabled = ref(false)

const state = reactive({
    id: null,
    feedbackData: {}
})

const imgGroup = computed(() => {
    if (!state.imgPaths) return []
    return state.imgPaths.split(",")
})

onLoad((option) => {
    state.id = option.id
    getSchoolWork(option.id)
})

const handleClick = (type, item) => {
    state.feedbackData[type] = item.value
}

const getSchoolWork = async (id) => {
    const { data } = await http.get("/app/work/getSchoolWorkFeedback", { id })
    if (!data && Object.keys(data).length == 0) return
    Object.assign(state, data)
}

const submit = async () => {
    if (vaild()) {
        const params = {
            id: state.id,
            isFeedback: true,
            completion: state.completion,
            difficulty: state.difficulty
        }
        try {
            disabled.value = true
            await http.post("/app/work/updateWorkFeedback", params)
            uni.showToast({
                title: "反馈成功",
                icon: "none"
            })
            uni.navigateBack({
                delta: 1,
                success: () => {
                    eventChannel.emit("backEvent")
                }
            })
        } catch (e) {
            disabled.value = false
        }
    }
}

function vaild() {
    if (!state.feedbackData.completion) {
        uni.showToast({
            title: "请选择完成情况",
            icon: "none"
        })
        return false
    }
    if (!state.feedbackData.difficulty) {
        uni.showToast({
            title: "请选择难易程度",
            icon: "none"
        })
        return false
    }
    return true
}
</script>

<style lang="scss" scoped>
.container_box {
    background: #f9faf9;
    .title_box {
        padding: 30rpx 30rpx 20rpx 30rpx;
        border-bottom: 2rpx solid #d8d8d8;
        background: #fff;
        .top {
            display: flex;
            align-items: center;
            padding-bottom: 20rpx;
            .l {
                font-size: 30rpx;
                font-weight: 600;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                .img {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 14rpx;
                    vertical-align: bottom;
                }
            }
        }
        .title {
            font-size: 24rpx;
            color: #666;
        }
    }
    .collapse_box {
        padding: 20rpx 30rpx 0 30rpx;
        background: #fff;
        .collapse_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 28rpx;
            .title_r {
                color: $uni-primary;
                font-size: 24rpx;
            }
        }
        .content {
            padding: 16rpx 0 40rpx 0;
            font-size: 26rpx;
            color: #999;
            .img_box {
                display: flex;
                flex-wrap: wrap;
                overflow: hidden;
                margin-top: 10rpx;
                .collapse_img {
                    width: 160rpx;
                    height: 160rpx;
                    border-radius: 8rpx;
                    margin-right: 16rpx;
                    margin-bottom: 10rpx;
                    &:nth-of-type(4n) {
                        margin-right: 0;
                    }
                }
            }
        }
    }
    .mian_box {
        margin-top: 26rpx;
        padding: 30rpx;
        background: #fff;
        .title {
            font-size: 28rpx;
            font-weight: 600;
            padding-bottom: 30rpx;
            border-bottom: 2rpx solid #d8d8d8;
            margin-bottom: 15rpx;
        }
        .item_box {
            display: flex;
            align-items: center;
            padding: 15rpx 0;
            .item_btn {
                height: 60rpx;
                display: flex;
                align-items: center;
                color: #999999;
                font-size: 28rpx;
                border-radius: 30rpx;
                margin-left: 0;
                margin-right: 30rpx;
                border: 2rpx solid #d8d8d8;
                background: #fff;
            }
            .actice_btn {
                background: $uni-primary;
                color: #fff;
            }
        }
    }
}
.footer {
    padding: 0 30rpx;
    background: #fff;
    font-size: 32rpx;
    .btn {
        color: $uni-text-color-inverse;
        height: 92rpx;
        line-height: 92rpx;
        border-radius: 10rpx;
        background: $uni-color-primary;
        margin-left: 0;
        margin-top: 30rpx;
        margin-bottom: 66rpx;
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .poiner_none {
        background-color: #d8d8d8;
        pointer-events: none;
    }
}
</style>

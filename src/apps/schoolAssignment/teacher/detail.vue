<template>
    <z-paging>
        <view class="container_box">
            <view class="back_btn"><uni-icons type="undo-filled" size="25" @click="handleCallback"></uni-icons></view>
            <view class="title_box">
                <view class="top">
                    <view class="l">
                        <img class="img" src="@nginx/workbench/schoolAssignment/kebiao.png" />
                        {{ state.info.classesTimetable.classesName }}
                    </view>
                    <view class="r"> {{ state.info.releaseTime }}发布 </view>
                </view>
                <view class="title">关联课程：{{ state.info.classesTimetable.timetableTime }} {{ state.info.classesTimetable.sequence }} {{ state.info.classesTimetable.startTime }} {{ state.info.classesTimetable.subjectName }}</view>
            </view>
            <view class="collapse_box">
                <uni-collapse v-model="state.collapse" @change="collapseStatusFn">
                    <uni-collapse-item titleBorder="none">
                        <template v-slot:title>
                            <view class="collapse_title">
                                <text>{{ state.info.title }}</text>
                                <text class="title_r">{{ state.collapse.length == 0 ? "展开" : "收起" }}</text>
                            </view>
                        </template>
                        <view class="content">
                            <view v-html="state.info.content"></view>
                            <view class="img_box">
                                <img v-for="(item, index) in imgGroup" :key="index" :src="item" class="collapse_img" />
                            </view>
                        </view>
                    </uni-collapse-item>
                </uni-collapse>
            </view>
            <view class="mian_box">
                <view class="line">
                    <uv-tabs lineWidth="20" lineColor="#00B781" :current="state.tabsCurrent" :scrollable="false" :activeStyle="{ color: '#00B781' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>
                </view>
                <view class="mian_container">
                    <view class="table_header">
                        <view class="item">学生</view>
                        <view class="item" v-show="state.tabsCurrent == 0">完成情况</view>
                        <view class="item" v-show="state.tabsCurrent == 0">难易程度</view>
                        <view class="item primery" v-show="state.tabsCurrent == 1" @click="handleClik(null)">一键提醒所有</view>
                    </view>
                    <view class="table_header container_item_box" v-for="(item, index) in state.info[type[state.tabsCurrent]]" :key="index">
                        <view class="item">{{ item.studentName }}</view>
                        <view class="item" v-show="state.tabsCurrent == 0">{{ completion[item.completion] }}</view>
                        <view class="item" v-show="state.tabsCurrent == 0">{{ difficulty[item.difficulty] }}</view>
                        <view :class="['item', item.isRemind ? 'poiner_none' : 'primery']" v-show="state.tabsCurrent == 1" @click="handleClik(item)">{{ item.isRemind ? "已发送" : "提醒" }}</view>
                    </view>
                </view>
            </view>
        </view>
    </z-paging>
</template>
<script setup>
const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const type = {
    0: "feedbackList",
    1: "noFeedbackList",
    2: "readList",
    3: "noReadList"
}

const difficulty = {
    1: "很简单",
    2: "有难度",
    3: "非常难"
}

const completion = {
    1: "已完成",
    2: "未完成"
}

const state = reactive({
    id: null,
    collapse: [],
    info: {
        classesTimetable: {},
        feedbackList: [],
        noFeedbackList: [],
        noReadList: [],
        readList: []
    },
    tabsCurrent: 0
})

const imgGroup = computed(() => {
    if (!state.info.imgPaths) return []
    return state.info.imgPaths.split(",")
})

const tabsList = computed(() => {
    return [
        { name: `已反馈(${state.info.feedbackList.length})`, id: 0 },
        { name: `未反馈(${state.info.noFeedbackList.length})`, id: 1 },
        { name: `已读(${state.info.readList.length})`, id: 2 },
        { name: `未读(${state.info.noReadList.length})`, id: 3 }
    ]
})

onLoad((option) => {
    state.id = option.id
    getSchoolWork(option.id)
})

// 点击折叠面板
const collapseStatusFn = (e) => {
    state.collapse = e
}

// tab切换
const tabsClick = (val) => {
    state.tabsCurrent = val.id
}

// 列表点击
const handleClik = async (item) => {
    console.log("item:", item)
    const params = {
        isWhole: item ? "1" : "0",
        workId: state.info.classesTimetable.workId,
        classesId: state.info.classesTimetable.classesId,
        studentId: item ? item.studentId : ""
    }
    await http.post("/app/work/updateRemind", params)
    uni.showToast({
        title: "提醒成功",
        icon: "none"
    })
    getSchoolWork(state.id)
}

// 撤回
const handleCallback = () => {
    showModal({
        title: "",
        content: "确定撤回该作业吗,撤回操作无法恢复",
        success: async () => {
            await http.get("/app/work/delete", { id: state.info.id })
            uni.showToast({
                title: "撤回成功",
                icon: "none"
            })
            uni.navigateBack({
                delta: 1,
                success: () => {
                    eventChannel.emit("backEvent")
                }
            })
        }
    })
}

const getSchoolWork = async (id) => {
    const { data } = await http.get("/app/work/getSchoolWork", { id })
    if (!data && Object.keys(data).length == 0) return
    state.info = data
}
</script>

<style lang="scss" scoped>
.container_box {
    background: #f9faf9;
    .back_btn {
        text-align: right;
        padding: 20rpx 30rpx;
    }
    .title_box {
        padding: 30rpx 30rpx 20rpx 30rpx;
        border-bottom: 2rpx solid #d8d8d8;
        background: #fff;
        .top {
            display: flex;
            align-items: center;
            padding-bottom: 20rpx;
            .l {
                font-size: 30rpx;
                font-weight: 600;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                .img {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 14rpx;
                    vertical-align: bottom;
                }
            }
            .r {
                font-size: 28rpx;
                color: $uni-primary;
            }
        }
        .title {
            font-size: 24rpx;
            color: #666;
        }
    }
    .collapse_box {
        padding: 20rpx 30rpx 0 30rpx;
        background: #fff;
        .collapse_title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 28rpx;
            .title_r {
                color: $uni-primary;
                font-size: 24rpx;
            }
        }
        .content {
            padding: 16rpx 0 40rpx 0;
            .img_box {
                display: flex;
                flex-wrap: wrap;
                overflow: hidden;
                margin-top: 10rpx;
                .collapse_img {
                    width: 160rpx;
                    height: 160rpx;
                    border-radius: 8rpx;
                    margin-right: 16rpx;
                    margin-bottom: 10rpx;
                    &:nth-of-type(4n) {
                        margin-right: 0;
                    }
                }
            }
        }
    }
    .mian_box {
        margin-top: 22rpx;
        .line {
            border-bottom: 2rpx solid #d8d8d8;
        }
        .mian_container {
            padding: 30rpx;
            background: #fff;
            .table_header {
                height: 80rpx;
                color: #999;
                font-size: 28rpx;
                background: #f3fcf9;
                border-radius: 8rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .item {
                    width: 33.33%;
                    text-align: center;
                }
                .primery {
                    color: $uni-primary;
                }
                .poiner_none {
                    pointer-events: none;
                    color: #606266;
                }
            }
            .container_item_box {
                border-bottom: 2rpx solid #d8d8d8;
                color: #333;
                background: #fff;
                border-radius: 0;
            }
        }
    }
}
</style>

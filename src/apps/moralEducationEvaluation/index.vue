<template>
    <div class="moral_education">
        <!-- 头部自定义导航栏 -->
        <view class="head">
            <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" :leftWidth="100" title="班级德育" :rightWidth="100">
                <!-- #ifdef H5 || H5-WEIXIN-->
                <template v-slot:right>
                    <view class="right_class" @click="selectWeekFn">
                        <span class="text">{{ state.newest }}</span>
                        <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
                    </view>
                </template>
                <!-- #endif -->
                <!-- #ifdef APP-PLUS -->
                <template v-slot:right>
                    <view class="right_class" @click="selectWeekFn">
                        <span class="text">{{ state.newest }}</span>
                        <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
                    </view>
                </template>
                <!-- #endif -->
            </uni-nav-bar>
        </view>
        <view class="select_class" v-if="state.myClassRanking && state.myClassRanking.length > 0 && state.myClassRanking[0]?.childrenId">
            <uni-data-picker v-model="state.classId" :localdata="state.myClassRanking" @change="onChangeClass" :map="{ text: 'childrenName', value: 'childrenId' }" v-slot:default="{ data, error }" popup-title="请选择学生">
                <view v-if="error" class="error">
                    <text>{{ error }}</text>
                </view>
                <view v-else-if="data.length" class="selected">
                    <div class="triangle_class">
                        <view v-for="(item, index) in data" :key="index" class="selected-item">
                            <text>{{ item.text }}</text>
                        </view>
                        <span class="triangle_down"></span>
                    </div>
                </view>
                <view v-else>
                    <div class="triangle_class">
                        <text>请选择</text>
                        <span class="triangle_down"></span>
                    </div>
                </view>
            </uni-data-picker>
            <!-- #ifdef MP-WEIXIN -->
            <view class="right_class" @click="selectWeekFn">
                <span class="text">{{ state.newest }}</span>
                <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
            </view>
            <!-- #endif -->
        </view>
        <yd-empty :isMargin="true" text="管理员暂未设置德育评价" v-if="state.notSet" />
        <div v-else>
            <!-- 排行榜 -->
            <banking :firstRankingList="state.firstRanking" :secondRankingList="state.secondRanking" :thirdRankingList="state.thirdRanking" :defaultClass="state.defaultClass" :cycleType="state.info.cycleType" />
            <!-- 列表 -->
            <div class="banking_list" @touch.stop v-if="(state.myClassRanking && state.myClassRanking.length > 0) || (state.rankingList && state.rankingList.length > 0)">
                <div @click="bankingFn(item)" v-for="(item, index) in state.myClassRanking" :key="index">
                    <div class="list_item" v-if="state.classId ? item.childrenId === state.classId : true">
                        <div class="left">
                            <span class="text">本班排名：</span>
                            <span class="num">{{ item?.ranking }}</span>
                        </div>
                        <div class="middle">
                            <img class="image" :src="item?.avatar || state.defaultClass" alt="班级头像" />
                            <span class="gradeclass">{{ item?.name }}</span>
                        </div>
                        <div class="right">
                            <span class="fraction">{{ item?.score + "分" }}</span>
                            <img v-if="state.info?.identityType === 1" class="image" src="@nginx/workbench/moralEducationEvaluation/rightIcon.png" alt="" />
                        </div>
                    </div>
                </div>
                <div class="list_item" v-for="(item, index) in state.rankingList" :key="index">
                    <div class="middle no_myclass">
                        <span class="num">{{ item.ranking }}</span>
                        <img class="image" :src="item?.avatar || state.defaultClass" alt="班级头像" />
                        <span class="gradeclass">{{ item.name }}</span>
                    </div>
                    <div class="right no_myclass">
                        <span class="fraction">{{ item.score + "分" }}</span>
                    </div>
                </div>
            </div>
            <yd-empty :isMargin="true" text="暂无更多排名" v-else />
            <!-- 底部 -->
            <div class="footer_box" v-if="state.info.canScore && state.info.identityType === 1">
                <button @click="goScoreFn">去评分</button>
            </div>
        </div>
        <!-- #ifdef MP-WEIXIN -->
        <div v-if="!state.myClassRanking || state.myClassRanking.length == 0 || !state.myClassRanking[0].childrenId" class="change_week" @click="selectWeekFn">
            <span class="text">{{ state.newest }}</span>
            <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
        </div>
        <!-- #endif -->
        <moral-select :cycleType="state.info.cycleType" :isShow="state.isShow" :list="state.info.cycleList" @closePopup="closeFn" @selectPopup="changeWeek" />
    </div>
</template>

<script setup>
import MoralSelect from "./components/moralSelect.vue"
import Banking from "./components/banking.vue"

import { onShow, onLoad, onPullDownRefresh } from "@dcloudio/uni-app"

const state = reactive({
    firstRanking: [],
    secondRanking: [],
    thirdRanking: [],
    info: {
        identityType: 1, // 0=学生 1=老师 2=家长
        schoolId: "", // 学校id
        fromGroupId: "", // 评分组标识
        fromPersonId: "", // 评分人标识
        activityId: "", // 活动标识
        scoreTargetList: [], // 班级列表
        cycleType: 1, // 1-周 2-月
        canScore: true, // 是否可以评分
        cycleId: "", // 周数/月数
        cycleList: [] // 切换周列表
    },
    classList: [], // 选择学生
    classId: "", // 选择的学生Id
    notSet: false, // 是否设置了班级德育 false-已设置 true-未设置
    rankingList: [], // 所有班级排行榜
    myClassRanking: [], // 我的班级
    isShow: false, // 弹框状态
    newest: "", // 当前周
    defaultClass: "@nginx/workbench/moralEducationEvaluation/gradeclass.png" // 班级默认头像
})

function weekNewestFn(list) {
    let week = []
    list.forEach((item) => {
        if (item.newest) {
            week.push(item.seq)
        }
    })
    return week[0]
}

function rankingFn(list, ranking) {
    let arr = []
    list.map((item) => {
        if (item.ranking == ranking) {
            arr.push(item)
        }
    })
    return arr
}

function removeTopThree(list) {
    let arr = []
    let topThreeArr = ["1", "2", "3"]
    list.map((item) => {
        if (!topThreeArr.includes(item.ranking)) {
            arr.push(item)
        }
    })
    return arr
}

async function getDataFn(cycleId, title) {
    const params = {
        cycleId: cycleId ? cycleId : state.info.cycleId, // 周数/月数
        type: 1 // type 评价类型(1-班级德育 2-宿舍德育)
    }
    await http
        .post("/app/moralEducationActivityMobile/competition", params)
        .then((res) => {
            if (res?.data) {
                state.notSet = false
                const { cycleList, currentObjList, resultList } = res?.data
                state.info = res.data
                state.info.cycleId = cycleId ? cycleId : res.data.cycleId
                state.firstRanking = rankingFn(resultList, "1")
                state.secondRanking = rankingFn(resultList, "2")
                state.thirdRanking = rankingFn(resultList, "3")
                state.newest = !cycleId ? weekNewestFn(cycleList || []) : title
                state.myClassRanking = currentObjList || []
                state.classId = currentObjList[0]?.childrenId || ""
                state.rankingList = removeTopThree(resultList || [])
            } else {
                state.notSet = true
            }
        })
        .catch(() => {
            state.notSet = true
        })
}

function changeWeek(data) {
    state.info.cycleId = data.id
    state.info.cycleList = state.info.cycleList.map((item) => {
        return {
            ...item,
            checked: item.id === data.id
        }
    })
    getDataFn(data.id, data.seq)
    setTimeout(() => {
        state.isShow = false
    }, 500)
}

function bankingFn(item) {
    if (state.info.identityType === 1) {
        let params = {
            ...item,
            activityId: state.info.activityId, // 活动标识
            cycleId: state.info.cycleId, // 周期标识
            targetId: item.id, // 我的班级
            title: state.newest, // 第几周？
            cycleType: state.info.cycleType // 当前为月？/周？
        }
        navigateTo({
            url: "/apps/moralEducationEvaluation/scoreInfo/info",
            query: params
        })
    }
}

function selectWeekFn() {
    if (!state.notSet) {
        state.isShow = true
    } else {
        uni.showToast({
            title: "管理员暂未设置德育评价",
            duration: 2000,
            icon: "none"
        })
    }
}

// 点击选择学生
function onChangeClass({ detail: { value } }) {
    console.log("点击选择学生")
}

function closeFn() {
    state.isShow = false
}
function goScoreFn() {
    navigateTo({
        url: "/apps/moralEducationEvaluation/score"
    })
}

onPullDownRefresh(async () => {
    await getDataFn()
    uni.stopPullDownRefresh()
})

onShow((res) => {
    getDataFn()
})
</script>

<style lang="scss" scoped>
@import "./css/banking.scss";
.select_class {
    background: $uni-bg-color;
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    :deep(.selected) {
        display: flex;
    }
}
.triangle_class {
    display: flex;
    align-items: center;

    .triangle_down {
        width: 0;
        height: 0;
        overflow: hidden;
        font-size: 0;
        line-height: 0;
        border-width: 10rpx;
        margin-top: 10rpx;
        margin-left: 10rpx;
        border-style: solid dashed dashed dashed;
        border-color: $uni-color-primary transparent transparent transparent;
    }
}

.change_week {
    position: fixed;
    top: 200rpx;
    right: 0rpx;
    width: 152rpx;
    height: 56rpx;
    background: #ffffff;
    border-radius: 28rpx 0rpx 0rpx 28rpx;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .text {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
    }

    .image {
        width: 44rpx;
        height: 44rpx;
    }
}
</style>

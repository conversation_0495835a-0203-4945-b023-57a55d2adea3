<template>
    <!-- 排行榜 -->
    <div class="banking_box">
        <div class="background_class" :class="cycleType === 1 ? 'banking_week' : 'banking_moon'">
            <div class="banking_image">
                <div class="first_photo">
                    <swiper v-if="firstRankingList && firstRankingList.length > 0" class="photo" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in firstRankingList" :key="item?.id || index">
                            <img class="photo_item_img" :src="item?.avatar || defaultClass" alt="" />
                        </swiper-item>
                    </swiper>
                    <div v-else class="photo_item">
                        <div>虚位</div>
                        <div>以待</div>
                    </div>
                    <swiper class="text_class" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in firstRankingList" :key="item?.id || index">
                            <div class="grade" v-if="item?.name">
                                {{ item?.name || "" }}
                            </div>
                            <div class="score" v-if="item?.score">{{ item?.score || "" }}分</div>
                        </swiper-item>
                    </swiper>
                </div>

                <div class="second_photo">
                    <swiper class="photo" v-if="secondRankingList && secondRankingList.length > 0" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in secondRankingList" :key="item?.id || index">
                            <img v-if="item?.id" class="photo_item_img" :src="item?.avatar || defaultClass" alt="" />
                        </swiper-item>
                    </swiper>
                    <div v-else class="photo_item">
                        <div>虚位</div>
                        <div>以待</div>
                    </div>
                    <swiper class="text_class" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in secondRankingList" :key="item?.id || index">
                            <div class="grade" v-if="item?.name">
                                {{ item?.name || "" }}
                            </div>
                            <div class="score" v-if="item?.score">{{ item?.score || "" }}分</div>
                        </swiper-item>
                    </swiper>
                </div>
                <div class="third_photo">
                    <swiper class="photo" v-if="thirdRankingList && thirdRankingList.length > 0" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in thirdRankingList" :key="item?.id || index">
                            <img v-if="item?.name" class="photo_item_img" :src="item?.avatar || defaultClass" alt="" />
                        </swiper-item>
                    </swiper>
                    <div v-else class="photo_item">
                        <div>虚位</div>
                        <div>以待</div>
                    </div>
                    <swiper class="text_class" circular :indicator-dots="false" :autoplay="true" :interval="3000" :vertical="true">
                        <swiper-item v-for="(item, index) in thirdRankingList" :key="item?.id || index">
                            <div class="grade" v-if="item?.name">
                                {{ item?.name || "" }}
                            </div>
                            <div class="score" v-if="item?.score">{{ item?.score || "" }}分</div>
                        </swiper-item>
                    </swiper>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    // 默认头像
    defaultClass: {
        type: String,
        default: ""
    },
    // 列表
    firstRankingList: {
        type: Array,
        default: () => []
    },
    secondRankingList: {
        type: Array,
        default: () => []
    },
    thirdRankingList: {
        type: Array,
        default: () => []
    },
    // 月排行还是周排行
    cycleType: {
        type: Number,
        default: 1
    }
})

const cycleType = computed(() => {
    return props.cycleType
})

const firstRankingList = computed(() => {
    return props.firstRankingList
})

const secondRankingList = computed(() => {
    return props.secondRankingList
})

const thirdRankingList = computed(() => {
    return props.thirdRankingList
})

const defaultClass = computed(() => {
    return props.defaultClass
})
</script>

<style lang="scss" scoped>
.banking_box {
    pointer-events: none;
    .background_class {
        position: relative;
        height: 566rpx;

        .banking_image {
            position: absolute;
            width: 100%;
            bottom: 0rpx;
            left: 0rpx;
            background: url("@nginx/workbench/moralEducationEvaluation/bankingImage.png") no-repeat;
            background-size: cover;
            height: 266rpx;
            .first_photo {
                position: absolute;
                top: -145rpx;
                left: 310rpx;
                width: 140rpx;
                height: 162rpx;
                background: url("@nginx/workbench/moralEducationEvaluation/firstPhoto.png") no-repeat;
                background-size: cover;
                .photo_item {
                    top: 22%;
                    left: 6%;
                    height: 120rpx;
                    width: 120rpx;
                }

                .photo {
                    height: 120rpx;
                    width: 120rpx;
                    top: 22%;
                    left: 6%;
                    .photo_item_img {
                        border-radius: 50%;
                        height: 120rpx;
                        width: 120rpx;
                    }
                }

                .text_class {
                    overflow: hidden;
                    max-height: 190rpx;
                    position: absolute;
                    top: 190rpx;
                    left: -32rpx;
                    width: 200rpx;
                    text-align: center;

                    .grade {
                        font-size: 27rpx;
                        font-weight: 600;
                        color: $uni-text-color-inverse;
                        line-height: 38rpx;
                        margin-top: 18rpx;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                    }

                    .score {
                        font-size: 53rpx;
                        font-family: DINPro-Bold, DINPro;
                        font-weight: bold;
                        color: $uni-text-color-inverse;
                        line-height: 68rpx;
                        margin-top: 12rpx;
                    }
                }
            }

            .second_photo {
                position: absolute;
                top: -60rpx;
                left: 80rpx;
                background: url("@nginx/workbench/moralEducationEvaluation/secondPhoto.png") no-repeat;
                width: 124rpx;
                height: 144rpx;
                background-size: cover;
                .photo_item {
                    top: 24%;
                    left: 5%;
                    height: 104rpx;
                    width: 104rpx;
                }
                .photo {
                    top: 24%;
                    left: 5%;
                    height: 104rpx;
                    width: 104rpx;
                    .photo_item_img {
                        border-radius: 50%;
                        height: 104rpx;
                        width: 104rpx;
                    }
                }

                .text_class {
                    position: absolute;
                    top: 190rpx;
                    left: -54rpx;
                    max-height: 130rpx;
                    width: 200rpx;
                    text-align: center;

                    .grade {
                        font-size: 27rpx;
                        font-weight: 600;
                        color: $uni-text-color-inverse;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                    }

                    .score {
                        font-size: 38rpx;
                        font-family: DINPro-Bold, DINPro;
                        font-weight: bold;
                        color: $uni-text-color-inverse;
                        line-height: 50rpx;
                        margin-top: 12rpx;
                    }
                }
            }

            .third_photo {
                position: absolute;
                width: 124rpx;
                height: 148rpx;
                top: -35rpx;
                right: 60rpx;
                background: url("@nginx/workbench/moralEducationEvaluation/thirdPhoto.png") no-repeat;
                background-size: cover;
                .photo_item {
                    top: 27%;
                    left: 5%;
                    height: 104rpx;
                    width: 104rpx;
                }

                .photo {
                    height: 104rpx;
                    width: 104rpx;
                    top: 27%;
                    left: 5%;
                    .photo_item_img {
                        border-radius: 50%;
                        height: 104rpx;
                        width: 104rpx;
                    }
                }

                .text_class {
                    position: absolute;
                    top: 180rpx;
                    left: -40rpx;
                    max-height: 110rpx;
                    width: 200rpx;
                    text-align: center;

                    .grade {
                        font-size: 27rpx;
                        font-weight: 600;
                        color: $uni-text-color-inverse;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                    }

                    .score {
                        font-size: 38rpx;
                        font-family: DINPro-Bold, DINPro;
                        font-weight: bold;
                        color: $uni-text-color-inverse;
                        margin-top: 12rpx;
                    }
                }
            }

            .first_photo,
            .second_photo,
            .third_photo {
                .photo,
                .photo_item {
                    overflow: hidden;
                    position: absolute;
                    border-radius: 50%;
                    font-size: 6rpx;
                    font-weight: 400;
                    color: #bfbfbf;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                    flex-direction: column;
                }
            }
        }
    }
    .banking_moon {
        background: url("@nginx/workbench/moralEducationEvaluation/bankingMoon.png") no-repeat;
        background-size: cover;
    }
    .banking_week {
        background: url("@nginx/workbench/moralEducationEvaluation/bankingWeek.png") no-repeat;
        background-size: cover;
    }
}

@keyframes move {
    0% {
        transform: translateY(40);
    } /* 设置开始位置为0 */
    100% {
        transform: translateY(-40%);
    } /* 设置结束位置为滚动容器的高度的负值 */
}
</style>

<template>
    <view class="y-checkbox">
        <view v-for="(c, i) in $props.options" class="cell" :class="{ active: c === modelValue }" @click="handleClick(c)" :key="i">
            <text class="ellipsis">{{ c }}</text>
        </view>
    </view>
</template>

<script setup>
import { ref, reactive } from "vue"
const $props = defineProps({
    modelValue: {
        type: String || Number
    },
    options: {
        type: Array,
        default: []
    }
})
const $emit = defineEmits(["update:modelValue"])

const handleClick = (val) => {
    $emit("update:modelValue", val)
}
</script>

<style lang="scss" scoped>
.y-checkbox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .cell {
        width: 330rpx;
        height: 64rpx;
        background: $uni-bg-color;
        border-radius: 8rpx;
        border: 1px solid #d8d8d8;
        font-size: 28rpx;
        font-weight: 400;
        color: #666666;
        text-align: center;
        line-height: 64rpx;
        margin-bottom: 30rpx;
    }
}
.active {
    background: $uni-color-primary !important;
    color: #ffffff !important;
}
</style>

<template>
    <view class="y-input">
        <input :placeholder="placeholder" :maxlength="_props.maxlength" :value="modelValue" @input="handleInputEvent" />
    </view>
</template>

<script setup>
const $props = defineProps({
    modelValue: {
        type: String || Number
    },
    placeholder: {
        type: String || Number,
        default: "请输入"
    },
    _props: {
        type: Object,
        default: () => {}
    }
})
const $emit = defineEmits(["update:modelValue"])

const handleInputEvent = (e) => {
    console.log("e :>> ", e)
    $emit("update:modelValue", e.detail.value)
}
</script>

<style lang="scss" scoped>
.y-input {
    input {
        height: 64rpx;
        background: $uni-bg-color;
        border-radius: 8rpx;
        border: 1px solid #d8d8d8;
        box-sizing: border-box;
        padding: 12rpx 20rpx;

        &::placeholder {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
        }
    }
}
</style>

<template>
    <view class="y-checkbox">
        <!--    :class="{ active: activeVal.indexOf(c.value) > -1 }"-->
        <view v-for="(c, i) in $props.options" class="cell" :class="{ active: $props.modelValue.indexOf(c) > -1 }" @click="handleClick(c)">
            <text class="ellipsis">{{ c }}</text>
        </view>
    </view>
</template>

<script setup>
const $props = defineProps({
    modelValue: {
        type: Array
    },
    options: {
        type: Array,
        default: []
    }
})
const $emit = defineEmits(["update:modelValue"])

let activeVal = ref([])

const handleClick = (val) => {
    activeVal.value = JSON.parse(JSON.stringify($props.modelValue))
    const index_ = activeVal.value.indexOf(val)
    if (index_ >= 0) {
        activeVal.value.splice(index_, 1)
    } else {
        activeVal.value.push(val)
    }
    $emit("update:modelValue", activeVal.value)
}
</script>

<style lang="scss" scoped>
.y-checkbox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .cell {
        width: 330rpx;
        height: 64rpx;
        background: $uni-bg-color;
        border-radius: 8rpx;
        border: 1px solid #d8d8d8;
        font-size: 28rpx;
        font-weight: 400;
        color: #666666;
        text-align: center;
        line-height: 64rpx;
        margin-bottom: 30rpx;
    }
}
.active {
    background: $uni-color-primary !important;
    color: #ffffff !important;
}
</style>

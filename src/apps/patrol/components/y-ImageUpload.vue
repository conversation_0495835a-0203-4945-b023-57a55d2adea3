<template>
    <uni-file-picker class="reset-picker" :limit="_props.maxlength" @select="handleInputEvent" @delete="handleInputDelete"></uni-file-picker>
</template>

<script setup>
const $props = defineProps({
    modelValue: {
        type: String || Number
    },
    _props: {
        type: Object,
        default: () => {}
    },
    saveLoading: {
        type: Boolean,
        default: false
    }
})

let state = reactive({
    _images: {}
})
const $emit = defineEmits(["update:modelValue", "update:saveLoading"])

const handleInputEvent = (e) => {
    uni.showLoading({
        title: "正在上传中..."
    })
    e.tempFiles?.forEach(async (v, index) => {
        $emit("update:saveLoading", true)
        await http
            .uploadFile("/file/common/upload", v.path, { folderType: "app" })
            .then((res) => {
                state._images[v.uuid] = res
                $emit("update:modelValue", Object.values(state._images))
                if (e.tempFiles.length - 1 == index) {
                    $emit("update:saveLoading", false)
                    uni.hideLoading()
                }
            })
            .catch(() => {
                $emit("update:saveLoading", false)
                uni.hideLoading()
            })
    })
}
const handleInputDelete = (item) => {
    delete state._images[item.tempFile.uuid]
    $emit("update:modelValue", Object.values(state._images))
}
</script>

<style lang="scss" scoped>
.reset-picker {
    :deep(.file-picker__box-content) {
        border-radius: 20rpx !important;

        .icon-del-box {
            background-color: #eb624b;
            width: 40rpx;
            height: 40rpx;
        }
    }
}
</style>

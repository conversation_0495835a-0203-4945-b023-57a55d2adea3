<template>
    <view class="y-textarea">
        <textarea :placeholder="placeholder" :maxlength="_props.maxlength" :value="modelValue" @input="handleInputEvent" auto-height></textarea>
    </view>
</template>

<script setup>
import { ref, reactive, computed } from "vue"

const $props = defineProps({
    modelValue: {
        type: String || Number
    },
    placeholder: {
        type: String || Number,
        default: "请输入"
    },
    _props: {
        type: Object,
        default: () => {}
    }
})
const $emit = defineEmits(["update:modelValue"])

const handleInputEvent = (e) => {
    $emit("update:modelValue", e.detail.value)
}
</script>

<style lang="scss" scoped>
.y-textarea {
    min-height: 144rpx;
    background: $uni-bg-color;
    border-radius: 8rpx;
    border: 1px solid #d8d8d8;
    box-sizing: border-box;
    padding: 12rpx 20rpx;
}

uni-textarea {
    width: 100%;
}
</style>

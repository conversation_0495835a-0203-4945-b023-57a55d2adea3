<template>
    <view class="shop">
        <z-paging ref="paging" :auto="false" :show-scrollbar="false" use-virtual-list :refresher-enabled="false" @query="getAlbumList" v-model="state.dataList">
            <template #top>
                <uni-nav-bar class="nav-bar" statusBar fixed left-icon="left" :border="false" title="">
                    <template #left>
                        <view class="left">
                            <uni-icons type="left" size="22" color="#ffffff" @click="back"></uni-icons>
                        </view>
                    </template>
                </uni-nav-bar>
            </template>
            <view class="shop-content">
                <view class="shop-content-header">
                    <swiper class="swiper" indicator-color="#999999" indicator-active-color="#ffffff" circular :indicator-dots="true" touchable :autoplay="true" :interval="2000" :duration="500">
                        <swiper-item v-for="item in state.banNerList" :key="item.id" @click="handleClick(item)">
                            <image style="width: 100%; height: 100%" :src="item.imageUrl"></image>
                        </swiper-item>
                    </swiper>
                </view>
                <view class="shop-content-body">
                    <uni-easyinput suffixIcon="search" v-model="state.easyinput" trim both :clearable="true" :adjust-position="true" placeholder="输入你想问的内容" @iconClick="handlerEasyinput" @clear="handlerEasyinput"> </uni-easyinput>
                    <view class="shop-content-body-list">
                        <uni-list-item thumb-size="58" :border="false" v-for="item in state.dataList" :key="item.id">
                            <template v-slot:header>
                                <image class="slot-image" :src="item.iconUrl || avatar" mode="widthFix"></image>
                            </template>
                            <template v-slot:body>
                                <view class="slot-text" @click="handleClick(item)">
                                    <view class="text-title">
                                        <text> {{ item.name }}</text>
                                        <text class="text-tag" :class="{ private: !item.isOfficial }">
                                            {{ item.isOfficial ? "官方" : "私有" }}
                                        </text>
                                    </view>
                                    <view class="text-note">{{ item.description }}</view>
                                </view>
                            </template>
                        </uni-list-item>
                    </view>
                </view>
            </view>

            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
        </z-paging>
    </view>
</template>

<script setup>
const avatar = "https://file.1d1j.cn/ai-icon/aiDefault.png"
const state = reactive({
    easyinput: "",
    dataList: [],
    banNerList: []
})
const paging = ref(null)
// banner图列表
async function getBanNerList() {
    await http
        .post("/ai/navigation-image/list", { group: "app_slide" })
        .then((res) => {
            state.banNerList = res.data
        })
        .catch(() => {
            paging.value.setLocalPaging([])
        })
}
// 获取列表
async function getAlbumList() {
    const params = {
        name: state.easyinput,
        homeType: 3,
        status: "published",
        ignoreCodeList: ["sp_aphorism_image", "sp_code_gen", "sp_exam_paper_gen"]
    }
    await http
        .post("/ai/assistant/public/list", params)
        .then((res) => {
            paging.value.setLocalPaging(res.data)
        })
        .catch(() => {
            paging.value.setLocalPaging([])
        })
}
// 搜索
const handlerEasyinput = () => {
    paging.value.reload()
}
const handleClick = (item) => {
    let assistantId = item.id
    if (item.navigateUrl) {
        assistantId = item.navigateUrl.split("://")[1]
    }
    uni.navigateTo({ url: `/apps/intelligence/index?assistantId=${assistantId}` })
}
const back = () => {
    uni.navigateBack()
}

onMounted(() => {
    getBanNerList()
    getAlbumList()
})
</script>

<style lang="scss" scoped>
.shop {
    width: 100vw;
    height: 100vh;
    .nav-bar {
        margin: -1px !important;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        :deep(.uni-navbar--fixed) {
            border: none;
            background-color: transparent !important;
            .uni-navbar__header {
                background-color: transparent !important;
            }
        }
        .left {
            width: 400rpx;
            display: flex;
            align-items: center;
            position: relative;
            .logo {
                width: 68rpx;
                height: 68rpx;
            }
            .uni-badge-left-margin {
                position: absolute;
                top: -18rpx;
                right: 0;
            }
        }
    }
    .shop-content-header {
        width: 100%;
        height: 388rpx;
        background: #6a6be7;
        .swiper {
            width: 100%;
            height: 100%;
        }
    }
    .shop-content-body {
        padding: 24rpx 30rpx;
        :deep(.is-input-border) {
            border-radius: 38rpx;
            padding: 6rpx;
            &.is-focused {
                border-color: $uni-color-primary !important;
            }
        }
        .shop-content-body-list {
            :deep(.uni-list-item__container) {
                display: flex;
                align-items: flex-start;
                padding: 30rpx 0 0;
            }
            .slot-image {
                width: 116rpx;
                height: 116rpx;
                min-height: 116rpx;
                margin-right: 24rpx;
                border-radius: 30rpx;
                overflow: hidden;
            }
            .slot-text {
                border-bottom: 1rpx solid $uni-border-color;
                padding-bottom: 30rpx;
                flex: 1;
                height: 120rpx;
                .text-title {
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #000000;
                    margin-bottom: 10rpx;
                    display: flex;
                    align-items: center;
                    .text-tag {
                        font-weight: 400;
                        font-size: 22rpx;
                        padding: 1rpx 14rpx;
                        border-radius: 4rpx;
                        color: $uni-color-primary;
                        background-color: #c6efe3ff;
                        margin-left: 8rpx;
                        &.private {
                            color: #fe9349ff;
                            background-color: #fedec8ff;
                        }
                    }
                }
                .text-note {
                    font-weight: 400;
                    font-size: 22rpx;
                    color: $uni-text-color-grey;
                    line-height: 32rpx;
                    overflow: hidden;
                    display: -webkit-box;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    word-break: break-all;
                    word-wrap: break-word;
                    white-space: normal;
                }
            }
        }
    }
}
</style>

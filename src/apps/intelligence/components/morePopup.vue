<template>
    <uni-popup ref="popup" background-color="#fff" width="100%" @maskClick="handleClick('close')">
        <view class="popup-content">
            <z-paging ref="paging" :auto="false" use-virtual-list @query="getMoreAssistants" v-model="state.historyList">
                <template #top>
                    <uni-list-item class="reset-list-item" :ellipsis="1" v-for="(item, index) in moreAssistants" :key="item.id || index" :title="item.name" :showArrow="item.showArrow" :thumb="item.avatar" :clickable="true" @click="handleClick(item.link)" />
                </template>
                <view class="history-content">
                    <template v-for="(item, idx) in _historyList" :key="idx">
                        <uni-group class="history-group" :title="item.name" v-if="item.children.length">
                            <uni-list-item class="uni-group-content" :class="{ active: it.id == state.activeId }" :border="false" :ellipsis="1" v-for="(it, idx) in item.children" :key="idx" :title="it.name" :clickable="true" @click="historyClick(it)">
                                <template v-slot:footer>
                                    <span class="delete" @click.stop="handlerNavigate(it)">X</span>
                                </template>
                            </uni-list-item>
                        </uni-group>
                    </template>
                </view>
            </z-paging>
        </view>
    </uni-popup>
</template>

<script setup>
import { reactive, ref, shallowRef } from "vue"
import dayjs from "dayjs"

const moreAssistants = [
    { name: "小壹助手", avatar: "https://file.1d1j.cn/ai-icon/xiaozhi.png", showArrow: true, link: "close" },
    { name: "智能体商店", avatar: "https://file.1d1j.cn/ai-icon/shop.png", showArrow: true, link: "shop/index" },
    { name: "历史记录", avatar: "https://file.1d1j.cn/ai-icon/history.png", showArrow: false, link: "" }
]
const props = defineProps({
    assistantId: {
        type: String,
        default: ""
    },
    morePopupOpen: {
        type: Boolean,
        default: false
    },
    inUseId: {
        type: String,
        default: ""
    }
})
const emit = defineEmits(["update:morePopupOpen", "updatePrompt"])
const state = reactive({
    activeId: "",
    assistantId: "",
    form: {
        assistantId: ""
    },
    historyList: []
})

const popup = shallowRef()
const paging = ref(null)
const day = ref([]) // 今天
const yesterday = ref([]) // 昨天
const historyObj = ref({}) // 其他日期
// 更多历史对话列表
async function getMoreAssistants(pageNo = 1, pageSize = 12) {
    const params = {
        ...state.form,
        pageNo,
        pageSize
    }
    //
    await http
        .post("/ai/chat/session/page", params)
        .then(({ data }) => {
            paging.value?.complete(data.list || false)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const updatePrompt = (item) => {
    state.activeId = item.id || ""
    emit("updatePrompt", item)
    emit("update:morePopupOpen", false)
}
// 删除历史记录
const handlerNavigate = async (item) => {
    uni.showModal({
        title: "删除",
        content: "您确定要删除该条历史记录吗？",
        confirmColor: "#00b781",
        success(res) {
            if (res.confirm) {
                http.post("/ai/chat/session/delete", { id: item.id }).then((res) => {
                    state.activeId = ""
                    uni.showToast({
                        title: res.message
                    })
                    paging.value?.refresh()
                })
            }
        }
    })
}
const historyClick = (item) => {
    if (state.activeId !== item.id) {
        updatePrompt(item)
    } else {
        emit("update:morePopupOpen", false)
    }
}
const handleClick = (link) => {
    if (link === "close") {
        if (state.assistantId) {
            uni.reLaunch({
                url: "/apps/intelligence/index"
            })
        } else {
            updatePrompt({ assistantId: "default" })
        }
    } else if (link === "shop/index") {
        navigateTo({
            url: link
        })
    }
}

watch(
    () => props.morePopupOpen,
    (val) => {
        if (val) {
            state.form.assistantId = props.assistantId
            popup.value.open("left")
            getMoreAssistants()
        } else {
            popup.value.close()
        }
    }
)
const _historyList = computed(() => {
    // 如果是第一页，清空之前的数据 防止数据重复，否则不会清空 会继续追加.
    day.value = []
    yesterday.value = []
    historyObj.value = {}
    if (state.historyList.length === 0) return []
    // 遍历list，判断是否是今天 昨天 其他日期
    state.historyList.forEach((item) => {
        // 判断是否是今天
        const today = dayjs().format("YYYY-MM-DD")
        const createTime = dayjs(item.createTime).format("YYYY-MM-DD")
        // 判断是否是昨天 不能大于昨天
        const yesterdayTime = dayjs().subtract(1, "day").format("YYYY-MM-DD")
        if (today === createTime) {
            day.value.push(item)
        } else if (yesterdayTime == createTime) {
            // 判断是否是昨天 不能大于昨天
            yesterday.value.push(item)
        } else {
            // 否则用显示年月日
            if (!historyObj.value[createTime]) {
                historyObj.value[createTime] = [item]
            } else {
                historyObj.value[createTime].push(item)
            }
        }
    })
    let data = [
        { name: "今天", children: day.value },
        { name: "昨天", children: yesterday.value }
    ]
    return data.concat(Object.keys(historyObj.value).map((key) => ({ name: key, children: historyObj.value[key] })))
})

onLoad((item) => {
    state.assistantId = item.assistantId || ""
})
</script>

<style lang="scss" scoped>
.popup-content {
    width: 80vw;

    .history-group {
        margin: 0 !important;

        :deep(.uni-group__title) {
            background-color: $uni-bg-color;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
        }

        :deep(.uni-group__content) {
            padding: 0 16rpx;
        }

        .uni-group-content {
            border-radius: 8rpx;
            padding: 16rpx;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;

            &.active {
                background: #f4f4f4 !important;
            }

            :deep(.uni-list-item__container) {
                padding: 0;
            }
            :deep(.uni-list-item__content-title) {
                padding-top: 8rpx;
                font-size: 28rpx;
            }
        }
    }
    .reset-list-item {
        :deep(.uni-list-item__content-title) {
            font-size: 32rpx;
        }
    }
}
.delete {
    padding: 12rpx;
    font-size: 24rpx;
}
</style>

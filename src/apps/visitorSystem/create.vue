<template>
    <view class="create" v-show="!isSelection">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="发起申请" :leftWidth="100" :rightWidth="100">
            <template #left>
                <view class="back" @click="clickLeft">查看记录</view>
            </template>
        </uni-nav-bar>
        <!-- 选择访客列表 -->
        <view class="select-visitors">
            <view class="title_box form">
                <view>
                    <text class="color_red">*</text>
                    <text>第一步：选择访客</text>
                </view>
                <view>
                    <text class="form-text">已选择：</text>
                    <text class="form-text" :class="{ color_red: !state.visitorUserIds.length }">
                        {{ state.visitorUserIds.length }}
                    </text>
                    <text class="form-text" style="padding: 20rpx">人</text>
                </view>
            </view>
            <view class="uni-list">
                <checkbox-group @change="radioChange" v-if="fromUserNameList?.length">
                    <label class="uni-list-cell" v-for="item in fromUserNameList" :key="item.id">
                        <view class="radio" :class="{ active: state.visitorUserIds.includes(item.id) }">
                            <checkbox :value="item" color="#07C160" :checked="state.visitorUserIds.includes(item.id)" />
                        </view>
                        <view class="cell-info">
                            <view class="user">
                                <view class="icon">{{ item.name.substring(0, 1) }}</view>
                                <view class="user-id">
                                    <view>{{ item.name }}</view>
                                    <view class="ID">身份证：{{ item.idCard }}</view>
                                </view>
                            </view>
                            <view class="three-point" @click.stop="clickMore(item)">...</view>
                        </view>
                    </label>
                </checkbox-group>
                <view class="add-visitor" @click="clickAddVisitor">+ 新增访客</view>
            </view>
        </view>
        <template v-if="state.forms?.length">
            <view class="title_box form">
                <view>
                    <text class="color_red">*</text>
                    <text> 第二步：填写访问信息</text>
                </view>
            </view>
            <uni-forms class="content-form" ref="contentFormRef" label-position="top" label-width="100vw" :modelValue="state.createForm" :rules="fromRules">
                <template v-for="(item, idx) in state.forms" :key="idx">
                    <uni-forms-item :key="idx" v-if="item.attribute !== 'picture'" :label="item.title" :required="item.props.required" :name="item.attribute">
                        <view v-if="['businessName', 'reason'].includes(item.attribute)">
                            <uni-easyinput @change="handerEasyinput(item)" :type="['number', 'Phone'].includes(item.name) ? 'number' : ''" v-model="state.createForm[item.attribute]" :inputBorder="state.inputBorder" :maxlength="item.props.maxlength" :placeholder="item.props.placeholder" />
                        </view>

                        <template v-else-if="item.attribute === 'toUserName'">
                            <uni-list class="reset-uni-list" v-if="state.route.ticket" @click="handerSelection">
                                <uni-list-item v-if="state.createForm[item.attribute]" showArrow :title="state.createForm[item.attribute]" />
                                <uni-list-item class="placeholder" v-else showArrow title="请选择拜访对象" />
                            </uni-list>

                            <text v-else>{{ state.createForm[item.attribute] }} </text>
                        </template>
                        <!-- 来访开始时间 -->
                        <uni-datetime-picker v-else-if="item.attribute === 'startTime'" :border="state.inputBorder" :safe-area="false" :hide-second="true" type="datetime" :start="state.minDate" v-model="state.createForm[item.attribute]" :placeholder="item.props.placeholder" @change="handerDatetimePicker($event, item.attribute)" />

                        <!-- 来访结束时间 -->
                        <uni-datetime-picker v-else-if="item.attribute === 'endTime'" :border="state.inputBorder" :safe-area="false" :hide-second="true" type="datetime" :start="state.minEndDate" :end="state.minEndDateEnd" v-model="state.createForm[item.attribute]" :placeholder="item.props.placeholder" @change="handerDatetimePicker($event, item.attribute)" />
                    </uni-forms-item>
                    <view class="btm10s" v-if="item.attribute == 'businessName'"> </view>
                </template>
                <view class="ellipsiss">注意：申请通过后，在访问当天需携带身份证原件进行人证比对</view>
            </uni-forms>
            <view class="footer">
                <button class="button" type="primary" :loading="state.confirmLoading" @click="visitorSubmit">提交</button>
            </view>
        </template>
        <yd-empty text="暂无数据" :isMargin="true" v-else />

        <!-- 弹出插件 -->
        <view class="unit">
            <!-- 中间弹出 -->
            <uni-popup ref="inputDialog" type="bottom" :maskClick="false">
                <view class="check_form">
                    <text class="title">访客申请需要校验你的手机号</text>
                    <uni-forms label-position="top" label-width="60px" :modelValue="state.checkForm">
                        <uni-forms-item label="手机号" name="phone">
                            <uni-easyinput type="number" v-model="state.checkForm.phone" :inputBorder="state.inputBorder" :maxlength="11" placeholder="请选择" />
                            <p style="color: red" v-if="state.errorPhone">手机号格式不正确！</p>
                        </uni-forms-item>
                        <uni-forms-item label="验证码" name="code">
                            <view class="flx">
                                <uni-easyinput type="number" v-model="state.checkForm.smsCode" :inputBorder="state.inputBorder" :maxlength="6" placeholder="请选择" />
                                <text class="uni-icon" v-if="!state.errorPhone && state.checkForm.phone.length > 10 && !state.checkForm.smsCode" @click="onObtainCode">{{ state.codeTitle }}</text>
                            </view>
                        </uni-forms-item>
                    </uni-forms>
                    <button class="button" style="margin: 0 20rpx" type="primary" :disabled="!state.checkForm.smsCode || state.isEmpty" :loading="state.checkFormLoading" @click="onCheckFormCode">校验</button>
                </view>
            </uni-popup>

            <uni-popup ref="visitorOperatin" type="bottom" class="safe-area-inset-bottom" :safe-area="false" :maskClick="false">
                <view class="check_form">
                    <view>
                        <view class="title">访客操作</view>
                        <uni-icons class="icons" type="closeempty" @click="visitorOperatin.close()"></uni-icons>
                    </view>
                    <view class="visitor-item edit" @click="cickeEedit">编辑</view>
                    <view class="visitor-item delete" @click="cickeDelete">删除</view>
                </view>
            </uni-popup>
        </view>
    </view>
    <selection v-if="isSelection" @selected="handlerSelected" />
</template>

<script setup>
import dayjs from "dayjs"
import { decodeURI, encodeURI, setCache, getCache } from "./utils.js"
import { checkPlatform } from "@/utils/sendAppEvent.js"
import Selection from "./selection.vue"

const isSelection = shallowRef(false)
const state = reactive({
    errorPhone: false,
    isEmpty: true,
    timer: null,
    route: {
        // id: '',
        _fromKey: "",
        schoolId: "", //学校id
        manageUserId: "",
        toUserName: "",
        toUserId: ""
    },
    countdown: 0,
    codeTitle: "立即获取",
    verifyType: [],
    isValid: false,
    imageStyles: {
        width: 100,
        height: 100
    },
    checkFormLoading: false,
    confirmLoading: false,
    //  new Date()
    minDate: dayjs().format("YYYY/MM/DD HH:MM"),
    minEndDate: dayjs().format("YYYY/MM/DD HH:MM"),
    minEndDateEnd: dayjs().format("YYYY/MM/DD HH:MM"),
    inputBorder: false,
    toUserNameActions: [],
    visitorUserIds: [],
    createForm: {
        inviteId: "", //邀请人id
        fromUserName: "", //访客姓名
        phone: "", //手机号
        idCardType: null, //证件类型 1：身份证 2：其他
        idCardTypeName: "",
        idCard: "", //证件号
        startTime: "", //开始时间
        endTime: "", //结束时间
        businessName: "", //班级或场地名称
        picture: "", //人脸照片
        reason: "", //到访原因
        personType: 1, //人员类型 1：访客 2：临时人员
        toUserName: ""
    },

    checkForm: { phone: "", smsCode: "" },
    IDCardTypeActions: [
        { name: "身份证", key: "check", id: 1 },
        { name: "其他", key: "other", id: 2 }
        // { name: '香港身份证', key: 'hgID' , id: 3},
        // { name: '香港身份证', key: 'macaoID', id: 4 },
        // { name: '港澳护照', key: 'hgmacaoID' , id: 5},
    ],

    dataFrom: {},
    currentPhone: "",
    visitorFormList: [],
    forms: [],
    visitorObj: {},
    assigneeName: "",
    isAuthorization: false
})
// 访客人员列表
const fromUserNameList = shallowRef([])
const contentFormRef = shallowRef()
const inputDialog = shallowRef()
const visitorOperatin = shallowRef("")

const fromRules = {
    toUserName: {
        rules: [{ required: true, errorMessage: "拜访对象不能为空！" }]
    },
    reason: { rules: [{ required: true, errorMessage: "到访原因不能为空！" }] },
    startTime: {
        rules: [
            { required: true, errorMessage: "来访开始时间不能为空！" },
            {
                validateFunction: (rule, value, data, callback) => {
                    if (value) {
                        if (state.createForm.entTiem) {
                            // 开始时间是否在结束时间之前
                            if (!dayjs(value).isBefore(dayjs(state.createForm.entTiem))) {
                                callback("来访开始时间不能晚于结束时间！")
                            }
                            return true
                        }
                        return true
                    }
                    callback("来访开始时间不能为空！")
                }
            }
        ]
    },
    endTime: {
        rules: [
            { required: true, errorMessage: "来访结束时间不能为空！" },
            {
                validateFunction: (rule, value, data, callback) => {
                    if (value) {
                        if (state.createForm.entTiem) {
                            // 开始时间是否在结束时间之前
                            if (!dayjs(value).isAfter(dayjs(state.createForm.startTiem))) {
                                callback("来访结束时间不能早于开始时间！")
                            }
                            return true
                        }
                        return true
                    }
                    callback("来访结束时间不能为空！")
                }
            }
        ]
    }
}
// 给createForm赋值 以便校验用
const handerEasyinput = (item) => {
    const { key, value } = item
    state.createForm[key] = value
}
const handerDatetimePicker = (event, key) => {
    state.createForm[key] = event
    if (key == "endTime" && !event) {
        event = state.createForm["startTime"]
    }
    state.minEndDate = dayjs(event).format("YYYY/MM/DD HH:MM")
    const lastD = dayjs(event).endOf("day").format("YYYY/MM/DD")
    state.minEndDateEnd = lastD + " 23:59"
}

const visitorSubmit = async () => {
    if (!state.visitorUserIds.length) {
        uni.showToast({
            title: "请先完成第一步：选择访客",
            duration: 2000,
            icon: "none"
        })
        return
    }
    contentFormRef.value.validate().then(async (res) => {
        state.confirmLoading = true
        if (state.dataFrom.children.length) {
            state.dataFrom.children.forEach((v) => {
                v.value = state.createForm[v.attribute]
                if (v.attribute === "picture") {
                    v.props.required = state.verifyType.includes("2")
                }
                if (["fromUserName", "phone", "idCardType", "idCard", "picture"].includes(v.attribute)) {
                    v.props.required = false
                }
            })
        }
        const { manageUserId, toUserId, schoolId, toUserName, inviteId, ticket } = state.route
        const params = {
            ...state.createForm,
            visitorUserIds: state.visitorUserIds, // 访客人员列表
            inviteId, // 邀请人id
            manageUserId, // 发起人用户id
            schoolId, //学校id
            toUserId, //受访人-教职工ID
            toUserName, //受访人-教职工名称
            isInvite: !ticket // 访客是否通过邀请  false不是 true是
        }
        http.post("/app/visitor/records/create", params)
            .then(({ message }) => {
                uni.showToast({
                    title: message,
                    duration: 2000,
                    icon: "none"
                })
                navigateTo({ url: "/apps/visitorSystem/index", query: state.route })
            })
            .finally(() => (state.confirmLoading = false))
    })
}
// 访客机验证方式设置
const getViteSettingGlobalInfo = async () => {
    const params = {}
    if (state.route.schoolId) {
        params.schoolId = state.route.schoolId
    }
    await http.get("/cloud/visitor/setting/global/get", params, state.isAuthorization).then(({ data }) => {
        const { verifyType = "", schoolId = "" } = data
        state.verifyType = verifyType
        state.route._fromKey = `visitorApply_${schoolId}`
        state.route.schoolId = schoolId
    })
}
watch(
    () => state.checkForm.phone,
    (val) => {
        if (val) {
            // 只要13、14、15、16、17、18、19开头即可
            const check = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
            state.errorPhone = !check.test(val)
            state.isEmpty = !check.test(val)
        } else {
            state.errorPhone = false
            state.isEmpty = false
        }
    }
)
// 获取验证码
const onObtainCode = () => {
    if (state.codeTitle === "立即获取") {
        http.post("/app/sms/visitor/message", { phone: state.checkForm.phone }, true)
            .then(({ data }) => {
                if (data) {
                    // 重置倒计时时间
                    state.codeTitle = "60s"
                    state.countdown = 59
                    // 设置定时器，每秒更新倒计时
                    let timer = setInterval(function () {
                        // 倒计时结束
                        if (state.countdown <= 0) {
                            // 清除定时器
                            clearInterval(timer)
                            // 按钮显示文本
                            state.codeTitle = "立即获取"
                        } else {
                            // 更新按钮显示文本，显示倒计时
                            state.codeTitle = state.countdown + "s"
                            // 倒计时减一
                            state.countdown--
                        }
                    }, 1000) // 每秒更新一次
                }
            })
            .finally(() => {
                state.checkFormLoading = false
            })
    }
}

// 获取表单
const getByFormKeyInfo = async () => {
    const { schoolId, _fromKey } = state.route
    const params = {
        formKey: _fromKey,
        schoolId
    }
    await http
        .post("/app/visitor/records/getByFormKey", params, state.isAuthorization)
        .then(({ data }) => {
            const { formItems } = data
            const dataFrom = JSON.parse(formItems)
            if (dataFrom[0]?.children.length) {
                dataFrom[0].children.forEach((v) => {
                    if (["fromUserName", "phone", "idCardType", "idCard", "picture"].includes(v.attribute)) {
                        state.visitorFormList.push(v)
                    } else {
                        state.forms.push(v)
                    }
                })
            }
            state.dataFrom = dataFrom[0]
        })
        .finally(() => {})
}
// 访客列表
const radioChange = (event) => {
    let assigneeName = []
    state.visitorUserIds = []
    const params = { fromUserName: [], phone: [], idCardType: [], idCard: [], picture: [] }
    const cardTypeFn = (idType) => state.IDCardTypeActions.find((v) => v.id == idType)
    event.detail.value?.forEach((v) => {
        const { name, id, phone, idCardType, idCard, picture } = v
        assigneeName.push(name)
        state.visitorUserIds.push(id)
        params.fromUserName.push(name)
        params.phone.push(phone)
        params.idCardType.push(cardTypeFn(idCardType).name || "")
        params.idCard.push(idCard)
        params.picture.push(picture)
    })

    state.assigneeName = assigneeName?.join(",") || ""
    Object.entries(params).forEach(([key, val]) => {
        state.createForm[key] = val.join(",") || ""
    })
}
// 访客人员-列表
const posttUserListInfo = async () => {
    const { manageUserId, schoolId } = state.route
    const params = { schoolId, manageUserId }
    await http
        .post("/app/visitor/user/list", params, state.isAuthorization)
        .then(({ data }) => {
            fromUserNameList.value = data
        })
        .finally(() => {})
}
// 获取用户信息
const getByPhoneInfo = async (phone) => {
    await http.post("/app/visitor/manageUser/getByPhone", { phone }).then(({ data }) => {
        const { id } = data
        state.route.manageUserId = id
    })
}

// 赋值
const voluation = (val) => {
    const { id, toUserId, toUserName, schoolId } = val
    if (schoolId) {
        state.route._fromKey = `visitorApply_${schoolId}`
        state.route.schoolId = val.schoolId
    }
    if (id) {
        state.route.inviteId = id
    }

    if (toUserId) {
        state.route.toUserId = toUserId
    }

    if (toUserName) {
        state.route.toUserName = toUserName
        state.createForm.toUserName = toUserName
    }
}

function clickLeft() {
    // #ifdef H5 || H5-WEIXIN
    const roleArr = ["yide-ios-app", "yide-android-app", "wx-miniprogram"]
    if (roleArr.includes(checkPlatform())) {
        navigateTo({ url: "/apps/visitorSystem/index", query: { ...state.route, noTodo: "create" } })
    } else {
        uni.navigateBack()
    }
    // #endif
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.navigateBack()
    // #endif
}
const clickAddVisitor = () => {
    const visitorFormList = JSON.stringify(state.visitorFormList)
    navigateTo({
        url: "/apps/visitorSystem/createVisitorIdentity",
        query: { ...state.route, visitorFormList, visitorObj: "" }
    })
}
// 选择访客更多操作
const clickMore = (item) => {
    state.visitorObj = item
    visitorOperatin.value.open("butoom")
}
// 编辑访客
const cickeEedit = () => {
    const visitorObj = JSON.stringify(state.visitorObj)
    const visitorFormList = JSON.stringify(state.visitorFormList)
    visitorOperatin.value.close()
    navigateTo({
        url: "/apps/visitorSystem/createVisitorIdentity",
        query: { ...state.route, visitorFormList, visitorObj }
    })
}
// 删除访客
const cickeDelete = () => {
    http.get("/app/visitor/user/delete", { id: state.visitorObj.id }).then(() => {
        visitorOperatin.value.close()
        posttUserListInfo()
        uni.showToast({
            title: "删除成功！"
        })
    })
}

// 提交验证身份
const onCheckFormCode = () => {
    state.checkFormLoading = true
    http.post("/app/visitor/records/checkSMS", state.checkForm)
        .then(async ({ data }) => {
            inputDialog.value.close()
            if (data) {
                await getByPhoneInfo(state.checkForm.phone)
                if (state.route.ticket && state.route.ticket != "undefined") {
                    const ticket = decodeURI(state.route.ticket)
                    state.route.schoolId = Object.keys(ticket)[0]
                }
                let encrypt = `phone=${state.checkForm.phone}${state.route.schoolId ? "&schoolId=" + state.route.schoolId : ""}&manageUserId=${state.route.manageUserId}`
                encrypt = encodeURI(encrypt)
                // || window.btoa(window.encodeURIComponent(encrypt))
                setCache("encrypt", encrypt, 1, "")
                navigateTo({ url: "/apps/visitorSystem/index", query: {} })
                init()
            }
        })
        .finally(() => {
            state.checkFormLoading = false
        })
}
const init = async () => {
    // encrypt 缓存是否有值 或者是否有token
    const encrypt = getCache("encrypt")
    if (encrypt || state.route.token) {
        if (encrypt && encrypt != "undefined") {
            // encrypt 缓存有值
            let obj = decodeURI(encrypt)
            state.route.manageUserId = obj.manageUserId

            voluation(obj)
        }
        if (state.route.token && state.route.token !== "undefined") {
            await getByPhoneInfo("")
        }
        await getViteSettingGlobalInfo()

        // 如有state.route.schoolId 获取表单
        if (state.route.schoolId) {
            await getByFormKeyInfo()
            await posttUserListInfo()
        } else {
            // 如果state.route.schoolId为空 则原路返回
            uni.showToast({
                title: "未能读取到正确的访客系统设置，请联系管理员在平台端访客应用修改系统设置",
                duration: 3000,
                icon: "none"
            })
            navigateTo({ url: "/apps/visitorSystem/index", query: state.route })
            return
        }
    } else {
        // 没有缓存 则弹出输入框 输入手机号 验证身份
        inputDialog.value.open("butoom")
    }
    // 需要在onReady中设置规则
    contentFormRef.value?.setRules(fromRules)
}
const handerSelection = () => {
    if (!state.visitorUserIds.length) {
        uni.showToast({
            title: "请先选择访客！",
            duration: 3000,
            icon: "none"
        })
        return
    }
    isSelection.value = true
}
const checkValidFn = async () => {
    await http.get("/app/visitor/inviteSetting/checkValid", { id: state.route.inviteId }).then(({ code }) => {
        code == "1003006019" && navigateTo({ url: "/apps/visitorSystem/invitationExpired" })
    })
}

const handlerSelected = (item) => {
    isSelection.value = false
    const { name, deptName, id } = item
    state.createForm.toUserName = `${name}（${deptName}）`
    state.route.toUserName = name
    state.route.toUserId = id
}
onReady(async () => {
    state.route.inviteId && (await checkValidFn())
    await init()
})

onLoad(async (item) => {
    state.route = item
    uni.setNavigationBarTitle({
        title: "发起申请" // 新标题内容
    })
    // 这判断缓存学校id 和 传入的ticket 是否一致
    const _encrypt = getCache("encrypt")
    if (item.ticket && item.ticket != "undefined" && _encrypt) {
        // 旧的缓存的数据
        const _obj = decodeURI(_encrypt)
        const ticket = decodeURI(item.ticket)
        const _schoolId = Object.keys(ticket)[0]
        if (_schoolId != _obj.schoolId) {
            uni.removeStorageSync("encrypt")
            state.isAuthorization = true
        }
    }

    if (item.encrypt && item.encrypt != "undefined") {
        state.isAuthorization = false
        // 旧的缓存的数据
        const _obj = _encrypt ? decodeURI(_encrypt) : {}
        // 正则替换空格
        const encryptNew = item.encrypt?.replace(/\s/g, "+") || ""
        // 对比加密数据 不一致 则删除缓存
        const obj = decodeURI(encryptNew)
        //  对比schoolId 不一致 则删除缓存
        const objSchoolId = obj?.schoolId
        const _objSchoolId = _obj?.schoolId
        if (_objSchoolId && objSchoolId && objSchoolId !== "undefined" && objSchoolId !== _objSchoolId) {
            uni.removeStorageSync("encrypt")
            uni.removeStorageSync("token")
            state.isAuthorization = true
        }
        state.isAuthorization = false
        if (obj.schoolId) {
            voluation(obj)
        }
    }
})
</script>

<style scoped lang="scss">
.create {
    .back {
        font-size: 14px;
        color: $uni-color-primary;
    }
    .content-form {
        background: $uni-bg-color;

        .reset-uni-list {
            :deep(.uni-list--border-bottom),
            :deep(.uni-list--border-top) {
                display: none;
            }

            :deep(.uni-list-item__container) {
                padding-left: 0;
            }

            .placeholder {
                :deep(.uni-list-item__content-title) {
                    color: $uni-text-color-grey;
                }
            }

            :deep(.uni-icons) {
                padding: 0;
            }
        }
    }

    .select-visitors {
        border: 20rpx solid $uni-bg-color-grey;
        border-right-width: 0;
        border-left-width: 0;
        background: $uni-bg-color;
    }

    .uni-list {
        background: $uni-bg-color;

        .add-visitor {
            text-align: center;
            padding-bottom: 20rpx;
            color: $uni-color-primary;
        }

        .three-point {
            color: $uni-color-primary;
            font-size: 50rpx;
        }

        .uni-list-cell {
            display: flex;
            align-items: center;
            margin: 20rpx;
            padding-bottom: 20rpx;
            border-bottom: 3rpx solid $uni-border-color;

            :deep(.uni-checkbox-input) {
                border-radius: 50%;
                overflow: hidden;

                &:hover {
                    border-color: $uni-color-primary;
                }

                svg {
                    color: $uni-bg-color;
                    background: $uni-color-primary;
                    transform: translate(-50%, -50%) scale(1);

                    &:focus {
                        outline-color: $uni-color-primary;
                        outline-style: auto;
                        outline-width: 5px;
                    }

                    path {
                        fill: $uni-bg-color;
                    }
                }
            }

            .active {
                :deep(.uni-checkbox-input) {
                    border-color: $uni-color-primary;
                }
            }
        }

        .radio {
            width: 70rpx;
        }

        .cell-info {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .user {
                display: flex;
                align-items: center;

                .icon {
                    width: 60rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    margin-right: 20rpx;
                    border-radius: 50%;
                    font-size: 28rpx;
                    text-align: center;
                    background: $uni-color-primary;
                    color: $uni-bg-color;
                }

                .user-id {
                    font-size: 28rpx;

                    .ID {
                        color: $uni-text-color-placeholder;
                        margin-top: 10rpx;
                        font-size: 28rpx;
                    }
                }
            }
        }
    }

    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-weight: 500;
        color: $uni-text-color;

        &.form {
            font-weight: 600;
            font-size: 32rpx;
            text-align: initial;
            text-indent: 20rpx;
            background: $uni-bg-color;
            display: flex;
            justify-content: space-between;

            .form-text {
                font-weight: 400;
                padding: 0;
                font-size: 26rpx;
            }

            .color_red {
                color: red;
            }
        }
    }

    .btm10s {
        height: 20rpx;
        width: 100%;
        background-color: $uni-bg-color-grey;
    }

    :deep(.uni-forms) {
        padding-bottom: 140rpx;
        overflow: hidden auto;

        .uni-forms-item {
            padding: 0 20rpx;

            .uni-easyinput__content-input,
            .uni-forms-item__label {
                padding: 0 !important;
            }
        }
    }

    uni-button[type="primary"] {
        background-color: $uni-color-primary;
    }

    .footer {
        padding: 30rpx 30rpx 40rpx;
        background: $uni-bg-color;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .ellipsiss {
        font-size: 24rpx;
        padding: 20rpx;
        background-color: $uni-bg-color-grey;
        color: $uni-text-color-grey;
    }

    .interviewee {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 30rpx;
    }

    .hender {
        .title {
            flex: 1;
            text-align: center;
            font-size: 32rpx;
        }

        .icons {
            align-items: center;
            display: flex;
            margin-right: 18rpx;
        }
    }

    .is_disableds {
        :deep(.is-disabled) {
            background-color: $uni-bg-color !important;
            color: $uni-text-color;
        }
    }

    // 小程序
    // #ifdef MP-WEIXIN
    :deep(.is-disabled) {
        background-color: $uni-bg-color !important;
        color: $uni-text-color;
    }
    // #endif
    .check_form {
        background-color: $uni-bg-color;
        border-radius: 25rpx 25rpx 0 0;
        padding: 20rpx 0;

        .title {
            display: block;
            text-align: center;
            font-weight: 600;
            font-size: 34rpx;
            margin-bottom: 10rpx;
        }

        .uni-forms {
            padding: 0;

            :deep(uni-text) {
                color: $uni-text-color;
            }

            .uni-easyinput {
                border-bottom: 1rpx solid $uni-border-color;
            }

            .flx {
                position: relative;

                .uni-icon {
                    position: absolute;
                    top: 10rpx;
                    right: 0;
                    color: $uni-color-primary;
                    z-index: 999;
                }
            }
        }

        .icons {
            position: absolute;
            top: 38rpx;
            right: 28rpx;
            font-size: 40rpx;
            font-weight: 600;
        }

        .visitor-item {
            text-align: center;
            font-size: 28rpx;
            padding: 22rpx 0;

            &.edit {
                border-bottom: 0.5rpx solid $uni-border-color;
            }

            &.delete {
                color: $uni-color-error;
                padding-bottom: 60rpx;
            }
        }

        .confirm {
            border-top: 1rpx solid $uni-border-color;
            text-align: center;
            color: $uni-color-primary;
            padding: 20rpx;
        }
    }

    .subimt-prompt {
        padding: 20rpx;

        .cotent {
            padding: 20rpx 0;
            font-weight: 400;
            font-size: 28rpx;
        }
    }
}

.item-image {
    position: reactive;
    height: 260rpx;

    .pointer-events {
        position: absolute;
        top: 0;
        bottom: 0;
    }
}

:deep(.uni-date-btn--ok) {
    height: 55px !important;
}

// .safe-area-inset-bottom {
//   padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
// }

@media (prefers-color-scheme: dark) {
    .uni-picker-view-mask {
        background-image: none !important;
    }
}
</style>

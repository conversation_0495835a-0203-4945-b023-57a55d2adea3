<template>
    <view class="visitor_invitation">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="访客邀请"> </uni-nav-bar>
        <view class="code_info">
            <view class="code_info_content">
                <view class="tip_title">分享二维码邀请访问</view>
                <view class="title">{{ state.invitation.schoolName }}</view>
                <image class="code_iamge" :src="state.invitation.qrCode"></image>
                <view class="tip_title expiration_date">
                    <view class="name">邀请有效期</view>
                    <view class="name" @click="lifespanRef.open('butoom')">
                        {{ state.lifespanId > 30 ? "永久" : `${state.lifespanId}天` }}
                        <uni-icons class="icons" type="right"></uni-icons>
                    </view>
                </view>
            </view>
            <view class="update-time">
                <view class="tip">
                    失效时间：{{ expiredTime }}
                    <div class="update-btn" @click="closePopup(null)">
                        <uni-icons type="loop" size="14" color="#11C685"></uni-icons>
                        刷新
                    </div>
                </view>
            </view>
        </view>
        <view class="footer">
            <!-- <button plain block type="primary" :loading="state.confirmLoading" @click="shareRef.open('butoom')">分享</button> -->
            <view class="footer-update-btn" @click="copyInvitationLink">复制邀请链接</view>
        </view>
        <!-- 分享 -->
        <!-- <uni-popup ref="shareRef" type="bottom" background-color="#fff" :safe-area="false">
            <view class="interviewee hender">
                <view class="title">分享</view>
                <uni-icons class="icons" type="closeempty" @click="shareRef.close()"></uni-icons>
            </view>
            <view class="share_conent">
                <view class="share_conent_item" @click="handerShareChange(item)" v-for="item in state.shares" :key="item.key">
                    <button open-type="share">分享到</button>
                    <image class="code_iamge" :src="item.icon"></image>
                    <text class="name">{{ item.name }}</text>
                </view>
            </view>
        </uni-popup> -->
        <!-- 邀请有效期 -->
        <yd-select-popup ref="lifespanRef" title="邀请有效期" @closePopup="closePopup" :list="state.lifespans" :fieldNames="{ value: 'key', label: 'name' }" :selectId="[state.lifespanId]" />
    </view>
</template>

<script setup>
// import qrcodeParser from "qrcode-parser"
import dayjs from "dayjs"
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import { copyUrl } from "@/utils"

const lifespanRef = ref()
const shareRef = ref()
const state = reactive({
    isWx: "false",
    loadMore: false,
    confirmLoading: false,
    invitation: {},
    lifespanId: null,
    lifespans: [
        { name: "3天", key: 3 },
        { name: "7天", key: 7 },
        { name: "30天", key: 30 },
        { name: "永久", key: 9999 }
    ],
    shares: [
        // { name: '转发到微信', key: 1, icon: 'https://alicdn.1d1j.cn/announcement/20230825/4af8a46d1e7a451cb6dedbdae8a94cfa.png' },
        // { name: '转发二维码图片', key: 3, icon: 'https://alicdn.1d1j.cn/announcement/20230825/da8afbf9b10a488b96a4e4ea44415ba1.png' },
        {
            name: "复制链接",
            key: 2,
            icon: "@nginx/workbench/visitorSystem/copyLink.png"
        }
    ]
})

const expiredTime = computed(() => {
    return state.invitation.expiredTime ? dayjs(state.invitation.expiredTime)?.format("YYYY-MM-DD") : "-"
})

const getApplet = () => {
    let pages = getCurrentPages()
    let routes = pages[pages.length - 1].$page.fullPath //完整路由地址
    return routes
}

const copyInvitationLink = () => {
    // 设置剪贴板数据
    uni.setClipboardData({
        data: state.invitation.inviteUrl,
        success: function () {
            console.log("复制成功")
        }
    })
}

// 分享
// const handerShareChange = async (item) => {
//     if (item.key === 2) {
//         const qrCodeUrl = await qrcodeParser(state.invitation.qrCode)
//         if (qrCodeUrl) {
//             if (["yide-ios-app", "yide-android-app"].includes(checkPlatform())) {
//                 sendAppEvent("copyInfo", { message: qrCodeUrl })
//             } else {
//                 copyUrl(qrCodeUrl)
//             }
//             uni.showToast({
//                 title: "已复制",
//                 duration: 2000,
//                 icon: "none"
//             })
//         }
//     }
//     shareRef.value.close()
// }

onShareAppMessage((res) => {
    if (res.from === "button") {
        // 来自页面内分享按钮
    }
    return {
        title: "自定义分享标题",
        path: "/pages/test/test?id=123"
    }
})
// 获取二维码
const getViteSettingGetInfo = () => {
    uni.showLoading({ title: "加载中" })
    http.get("/app/visitor/inviteSetting/get")
        .then(({ data }) => {
            state.invitation = data
            state.lifespanId = data.expired
        })
        .finally(() => uni.hideLoading())
}

// 更新有效期
const closePopup = (val) => {
    if (val) {
        state.lifespanId = val.key
    }
    const prams = {
        id: state.invitation.id,
        expired: state.lifespanId
    }
    http.post("/cloud/visitor/inviteSetting/update", prams).then(({ data, message }) => {
        uni.showToast({
            title: message,
            duration: 2000,
            icon: "none"
        })
        data && getViteSettingGetInfo()
    })
}

function clickLeft() {
    uni.navigateBack()
}

onLoad((res) => {
    state.isWx = res?.isWx
    // 停止当前页面下拉刷新
    uni.stopPullDownRefresh()
    uni.setNavigationBarTitle({
        title: "访客邀请" // 新标题内容
    })
    getViteSettingGetInfo()
    uni.pageScrollTo({
        scrollTop: 0
    })
})
</script>

<style scoped lang="scss">
.visitor_invitation {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;

    .code_info {
        width: 690rpx;
        height: 700rpx;
        margin: 130rpx auto;
        border-radius: 20rpx;
        background-color: $uni-bg-color;
    }

    .code_info_content {
        text-align: center;
        padding-top: 40rpx;
        .expiration_date {
            display: flex;
            justify-content: space-between;
            padding: 24rpx 50rpx;
            border-top: 2rpx solid $uni-border-color;
        }

        .title {
            font-size: 40rpx;
            font-weight: 600;
            min-height: 40rpx;
            max-height: 116rpx;
            margin: 20rpx auto;
            color: $uni-text-color;
        }

        .code_iamge {
            width: 400rpx;
            height: 400rpx;
            margin: 10rpx auto;
        }
    }

    .update-time {
        display: flex;
        justify-content: center;
        padding: 32rpx 50rpx;

        .update-btn {
            color: $uni-color-primary;
            margin-left: 10rpx;
        }
    }

    .tip {
        font-size: 28rpx;
        color: $uni-text-color-grey;
        display: flex;
        align-items: center;
    }
    .tip_title {
        font-size: 28rpx;
        color: $uni-text-color-grey;
    }

    .invitation_time,
    .time_update {
        margin-top: 40rpx;
        display: flex;
        justify-content: center;

        .time {
            color: $uni-text-color-grey;
            font-size: 28rpx;
        }

        .update {
            margin: 0 20rpx;
            color: $uni-color-primary;
        }
    }

    .invitation_time {
        margin-top: 0;
        padding: 40rpx 30rpx;
        border-top: 1px solid $uni-border-color;
        justify-content: space-between;
    }

    .footer {
        padding: 30rpx;
        background: $uni-bg-color;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;

        uni-button[type="primary"][plain] {
            color: $uni-color-primary;
            border: 1px solid $uni-color-primary;
            background-color: transparent;
        }
    }

    // 邀请有效期
    .interviewee {
        display: flex;
        justify-content: space-between;
        padding: 20rpx 30rpx;
    }

    .group_itme {
        border-bottom: 1rpx solid $uni-border-color;
    }

    .hender {
        align-items: center;
        display: flex;
        .title {
            flex: 1;
            text-align: center;
            font-size: 32rpx;
            margin-left: 18rpx;
        }
    }

    // 分享
    .share_conent_item {
        background-color: $uni-bg-color-grey;
        display: flex;
        align-items: center;
        margin: 20rpx 30rpx;
        border-radius: 20rpx;

        .code_iamge {
            width: 92rpx;
            height: 92rpx;
            border-radius: 50%;
            margin: 20rpx;
        }
    }

    // 小程序
    // #ifdef MP-WEIXIN || APP-PLUS
    .conent {
        margin-top: -500rpx;
    }

    // #endif
}

.footer-update-btn {
    color: $uni-color-primary;
    border: 1px solid $uni-color-primary;
    background-color: transparent;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    border-radius: 16rpx;
}
</style>

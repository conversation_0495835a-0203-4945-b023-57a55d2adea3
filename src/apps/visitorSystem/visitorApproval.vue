<template>
    <view class="visitor_approval">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="访客审批"> </uni-nav-bar>
        <view class="approval_tabs">
            <text class="tab" :class="{ active: state.tabsCurrent === item.key }" v-for="item in state.approvalTabs" :key="item.key" @click="onClickTab(item.key)">
                {{ item.label }}
            </text>
        </view>
        <view class="approval_conent" v-if="state.approvalList.length">
            <uni-list v-for="items in state.approvalList" :key="items.id" :border="false">
                <div class="approval_conent_item">
                    <view class="time"></view>
                    <view class="list" @click="handlerDetails(items)">
                        <view class="list_hander">
                            <view class="user_iamge">
                                <image class="iamge" src="@nginx/workbench/visitorSystem/defaultAvatar.png"></image>
                                <text class="user_name">{{ items.title }} </text>
                            </view>
                        </view>
                        <view class="list_conent">
                            <text class="user_name">{{ items.subtitle }} </text>
                            <view v-if="items.content">
                                <view class="item" v-for="(item, idx) in approvalContent(items.content)" :key="idx">
                                    {{ item.label }}：
                                    <text
                                        :style="{
                                            color: item.label === '审批状态' ? state.statusColor[items.status] : ''
                                        }"
                                    >
                                        {{ item.key }}
                                    </text>
                                </view>
                            </view>
                        </view>
                    </view>
                </div>
            </uni-list>
            <uni-load-more iconType="circle" :status="state.status" :contentText="state.contentText" />
        </view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>

<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
const state = reactive({
    route: {},
    status: "more",
    contentText: {
        contentdown: "查看更多", //more
        contentrefresh: "加载中", // loading
        contentnomore: "没有更多" //noMore
    },
    approvalList: [],
    // 1: 待办 2： 已办 3： 已撤销
    statusColor: ["", "#f0ad4e", "#11C685", "#999999"],
    tabsCurrent: 1,
    approvalTabs: [
        { label: "全部", key: 0 },
        { label: "待审批", key: 1 },
        { label: "已审批", key: 2 }
    ],
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
})

const getVisitorList = async () => {
    const params = {
        ...state.pagination,
        status: !state.tabsCurrent ? null : state.tabsCurrent
    }
    state.status = "loading"
    await http
        .post("/cloud/visitor/approve/page", params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data
            state.pagination = { pageNo, pageSize, total }
            if (state.approvalList.length) {
                state.approvalList = state.approvalList.concat(list)
            } else {
                state.approvalList = list || []
            }
        })
        .finally(() => {
            state.status = "noMore"
            uni.stopPullDownRefresh()
        })
}
// 后端不肯改 说是他们原生app 是这样写的。要骂就骂原生和后端 我是无辜的
const approvalContent = computed(() => {
    return (item) => {
        if (item) {
            let items = JSON.parse(item.replaceAll(" ", " "))
            return items.map((v) => {
                let it = v.split("：")
                return {
                    label: it[0],
                    key: it[1]
                }
            })
        }
        return { label: "", key: "" }
    }
})

// 重置数据 查询
const handerConfirm = () => {
    state.pagination = {
        pageNo: 1,
        pageSize: 10,
        total: 0
    }
    state.approvalList = []
    uni.pageScrollTo({
        scrollTop: 0,
        duration: 300
    })
    getVisitorList()
}

const clickLeft = () => {
    // #ifdef H5 || H5-WEIXIN
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    const { noTodo, _noTodo } = state.route
    // 如果是内部应用跳转， 直接返回上一页。 state.route.noTodo  是上一页的路由
    if (roleArr.includes(checkPlatform()) && !noTodo) {
        sendAppEvent("backApp", {})
    } else {
        // _noTodo防止visitorApproval details页面  点击会返回visitorApproval 页面所以加了个判断
        if (_noTodo) {
            navigateTo({ url: `/apps/visitorSystem/${_noTodo}`, query: { ...state.route, _noTodo: "visitorRecord" } })
        } else {
            uni.navigateBack()
        }
    }
    // #endif
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.navigateBack()
    // #endif
}
const handlerDetails = (items) => {
    navigateTo({
        url: "/apps/visitorSystem/details",
        query: {
            ...state.route,
            id: items.id,
            callId: items.callId,
            isApproval: true,
            isTeacher: true,
            noTodo: "visitorApproval"
        }
    })
}

const onClickTab = (item) => {
    state.tabsCurrent = item
    handerConfirm()
}

onPullDownRefresh(() => {
    handerConfirm()
})
onLoad((item) => {
    state.route = item
    handerConfirm()
    uni.setNavigationBarTitle({
        title: "访客审批" // 新标题内容
    })
})
onReachBottom(() => {
    const { pagination } = state
    if (pagination.total > pagination.pageSize * pagination.pageNo) {
        state.pagination.pageNo++
        getVisitorList()
    }
})
</script>

<style scoped lang="scss">
.visitor_approval {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;

    .approval_tabs {
        background-color: $uni-bg-color;
        display: flex;
        justify-content: space-around;
        // position: fixed;
        // top: 80rpx;
        // left: 0;
        // right: 0;
        // z-index: 1;

        .tab {
            padding: 20rpx 0;
            color: $uni-text-color-grey;

            &.active {
                color: $uni-color-primary;
                border-bottom: 4rpx solid $uni-color-primary;
            }
        }
    }

    .segmented-control__text,
    :deep(.segmented-control__text) {
        color: $uni-text-color-grey !important;

        &.segmented-control__item--text {
            color: $uni-color-primary !important;
        }
    }

    .approval_conent {
        .time {
            color: $uni-text-color-grey;
            font-size: 24rpx;
            text-align: center;
            margin: 20rpx auto 10rpx;
            color: #b3b3b3;
        }

        .approval_conent_item {
            background-color: $uni-bg-color-grey;
            padding: 0 28rpx;

            .list {
                padding: 28rpx;
                border-radius: 20rpx;
                background-color: $uni-bg-color;

                .user_name {
                    font-size: 30rpx;
                    font-weight: 600;
                }

                .list_hander {
                    border-bottom: 1rpx solid $uni-bg-color-grey;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-bottom: 28rpx;

                    .user_iamge {
                        display: flex;
                        align-items: center;

                        .iamge {
                            width: 60rpx;
                            height: 60rpx;
                            margin-right: 10rpx;
                            border-radius: 50%;
                        }
                    }

                    .time {
                        color: $uni-text-color-grey;
                        font-size: 24rpx;
                    }
                }

                .list_conent {
                    .user_name {
                        margin: 28rpx 0;
                        display: block;
                    }

                    .item {
                        margin: 10rpx 0;
                        // color: $uni-text-color-grey;
                        color: #666666;
                        font-size: 24rpx;
                    }
                }
            }
        }

        :deep(.uni-load-more) {
            background-color: $uni-bg-color-grey;
        }
    }
}

.title_box {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    font-size: 34rpx;
    font-weight: 500;
    color: $uni-text-color;
}
</style>

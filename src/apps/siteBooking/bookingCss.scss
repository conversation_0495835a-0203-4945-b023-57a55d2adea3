.site_booking_list {
    padding: 30rpx;

    .site_booking_item {
        background: #ffffff;
        box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
        padding: 30rpx;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        margin-bottom: 20rpx;

        .title_box {
            padding-bottom: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
                flex: 1;
            }

            .type_status {
                display: flex;
                align-items: center;

                .type {
                    padding: 6rpx 20rpx;
                    background: #f3fcf9;
                    border-radius: 24rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: $uni-color-primary;
                    line-height: 36rpx;
                    margin-left: 20rpx;
                    max-width: 134rpx;
                }

                .status {
                    padding: 6rpx 20rpx;
                    font-weight: 400;
                    font-size: 26rpx;
                    color: $uni-text-color-inverse;
                    line-height: 36rpx;
                    border-radius: 24rpx;
                    margin-left: 20rpx;
                }
            }
        }

        .info_item {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 40rpx;
            margin-top: 20rpx;

            .lable_text {
                color: #999999;
            }

            .value_text {
                color: #333333;
                flex: 1;
            }
        }
    }
}

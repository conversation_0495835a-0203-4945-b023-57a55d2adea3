<template>
    <view class="sign_in_page">
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" title="签到表" :border="false" @clickLeft="clickLeft"> </uni-nav-bar>
                <view class="sign_in_status" @click="selectStatusRef.open()">
                    <text>{{ signInText[statusId] }}</text>
                </view>
            </template>
            <view class="sign_in_list">
                <view class="sign_in_item item_tr">
                    <view
                        class="item_td"
                        :style="{
                            textAlign: tdIx == tdList.length - 1 ? 'right' : 'left'
                        }"
                        v-for="(td, tdIx) in tdList"
                        :key="td.value"
                        >{{ td.label }}</view
                    >
                </view>
                <view class="sign_in_item" v-for="(item, index) in dataList" :key="index">
                    <view
                        class="item_td ellipsis"
                        v-for="(td, tdIx) in tdList"
                        :key="td.value"
                        :style="{
                            textAlign: tdIx == tdList.length - 1 ? 'right' : 'left'
                        }"
                    >
                        <text v-if="td.value == 'index'">
                            {{ index + 1 }}
                        </text>
                        <text v-else-if="td.value == 'status'">
                            {{ signInText[item[td.value]] }}
                        </text>
                        <text v-else>{{ item[td.value] || "-" }}</text>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <yd-select-popup title="请选择预约状态" ref="selectStatusRef" :list="statusOptions" @closePopup="closeStatus" :selectId="[statusId]" />
    </view>
</template>

<script setup>
const siteBookingId = ref(null)
const selectStatusRef = ref(null)
const statusId = ref(0)
const paging = ref(null)
const dataList = ref([])
const signInText = {
    0: "未签到",
    1: "已签到",
    2: "未开始"
}
const statusOptions = [
    {
        value: 0,
        label: "未签到"
    },
    {
        value: 1,
        label: "已签到"
    },
    {
        value: 2,
        label: "未开始"
    }
]

const tdList = [
    {
        value: "index",
        label: "序号"
    },
    {
        value: "userName",
        label: "姓名"
    },
    {
        value: "dept",
        label: "所在部门"
    },
    {
        value: "status",
        label: "状态"
    }
]

function clickLeft() {
    uni.navigateBack()
}

// 调用List数据
async function queryList(pageNo, pageSize) {
    const parame = {
        pageNo,
        pageSize,
        siteBookingId: siteBookingId.value,
        status: statusId.value
    }
    await http
        .post("/app/siteBooking/bookingSignPage", parame)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch(() => {
            paging.value.complete(false)
        })
}

function closeStatus(val) {
    if (!val || val.value == statusId.value) return
    statusId.value = val.value
    paging.value?.reload()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    siteBookingId.value = options.siteBookingId
    nextTick(async () => {
        await paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.sign_in_page {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .sign_in_status {
        padding: 30rpx;
        background: $uni-bg-color;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;

        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid $uni-color-primary;
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
    }

    :deep(.zp-empty-view) {
        background: $uni-bg-color !important;
    }

    .sign_in_list {
        margin-top: 20rpx;
        background: $uni-bg-color;
        padding: 30rpx;

        .sign_in_item {
            display: flex;
            justify-content: space-between;
            padding: 30rpx;
            border-bottom: 1rpx solid $uni-border-color;

            .item_td {
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
                line-height: 40rpx;
                flex: 1;
            }
        }

        .item_tr {
            height: 80rpx;
            background: #f3fcf9;
            border-radius: 8rpx;
            padding: 0 30rpx;
            border: none;

            .item_td {
                font-weight: 400;
                font-size: 28rpx;
                color: #999999;
                line-height: 80rpx;
                flex: 1;
            }
        }
    }
}
</style>

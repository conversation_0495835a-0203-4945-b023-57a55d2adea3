<template>
    <view class="booking_record">
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" title="预约记录" :border="false" @clickLeft="clickLeft"> </uni-nav-bar>
                <!-- 筛选条件 -->
                <select-date @changeDate="changeDate">
                    <template #left>
                        <view class="status_view" @click="selectStatusRef.open()">
                            <text>{{ statusText[statusId] }}</text>
                        </view>
                    </template>
                </select-date>
            </template>
            <view class="site_booking_list">
                <view class="site_booking_item" v-for="(item, index) in dataList" :key="index" @click="bookingDetail(item)">
                    <view class="title_box">
                        <text class="title ellipsis">{{ item.name }}</text>
                        <view class="type_status">
                            <view class="type ellipsis">{{ item.siteBookingTypeName }}</view>
                            <view
                                class="status"
                                :style="{
                                    background: statusBg[item.status]
                                }"
                                >{{ statusText[item.status] }}</view
                            >
                        </view>
                    </view>
                    <view class="info_item">
                        <text class="lable_text">预约时间：</text>
                        <text class="value_text ellipsis">{{ item.bookingTime }}</text>
                    </view>
                    <view class="info_item">
                        <text class="lable_text">场地位置：</text>
                        <text class="value_text ellipsis">
                            {{ item.siteName }}
                            <text v-if="item.floor">-{{ item.floor }}</text>
                            <text v-if="item.roomNum">-{{ item.roomNum }}</text>
                        </text>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <yd-select-popup title="请选择预约状态" ref="selectStatusRef" :list="statusList" @closePopup="closeStatus" :fieldNames="{ value: 'id', label: 'name' }" :selectId="[statusId]" />
    </view>
</template>

<script setup>
import SelectDate from "./components/selectDate.vue"
import { statusList, statusText, statusBg } from "./data"

const dataList = ref([])
const selectStatusRef = ref(null) // 选择状态的弹框
const statusId = ref(null) // 选择的状态
const paging = ref(null)
const query = ref({})

// 调用List数据
async function queryList(pageNo, pageSize) {
    const parame = {
        pageNo,
        pageSize,
        ...query.value,
        status: statusId.value
    }
    http.post("/app/siteBooking/bookingRecordPage", parame)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((res) => {
            paging.value.complete(false)
        })
}

function clickLeft() {
    uni.navigateBack()
}

function closeStatus(val) {
    if (!val || val.id == statusId.value) return
    statusId.value = val.id
    paging.value?.reload()
}

function bookingDetail(item) {
    navigateTo({
        url: "/apps/siteBooking/bookingDetail",
        query: {
            id: item.id,
            examineStatus: false,
            status: item.status,
            isExamine: false // 首页列表查看
        }
    })
}

function changeDate(time) {
    query.value.startTime = time.startDate || ""
    query.value.endTime = time.endDate || ""
    nextTick(() => {
        paging.value?.reload()
    })
}
</script>

<style lang="scss" scoped>
@import "./bookingCss.scss";

.booking_record {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .status_view {
        background: $uni-bg-color;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;

        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid $uni-color-primary;
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
    }
}
</style>

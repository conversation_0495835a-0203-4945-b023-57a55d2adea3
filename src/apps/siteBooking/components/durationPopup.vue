<template>
	<uni-popup ref="durationPopupRef" type="bottom" :is-mask-click="false" :safe-area="false">
		<view class="duration_page">
			<view class="duration_title">
				<view class="close" @click="close">取消</view>
				<view class="title">预约时长</view>
				<view class="confirm" @click="confirm">确认</view>
			</view>
			<picker-view :value="selectTime" @change="bindChange" class="picker-view">
				<picker-view-column>
					<view class="item" v-for="(item, index) in hourTime" :key="index">{{ item.label }}</view>
				</picker-view-column>
				<picker-view-column>
					<view class="item" v-for="(item, index) in minutesTime" :key="index">{{ item.label }}</view>
				</picker-view-column>
			</picker-view>
		</view>
	</uni-popup>
</template>

<script setup>
const emit = defineEmits(['confirm'])
const durationPopupRef = ref(null)
const selectTime = ref([0, 0])
const hourTime = computed(() => {
	const hour = [];
	for (let h = 0; h <= 23; h++) {
		const hourLabel = `${h < 10 ? '0' + h : h}小时`;
		const hourValue = `${h < 10 ? '0' + h : h}`;
		hour.push({
			label: hourLabel,
			value: hourValue,
		});
	}
	return hour;
})

const minutesTime = computed(() => {
	const minutes = ["00", "15", "30", "45"];
	const min = [];
	for (let m of minutes) {
		min.push({
			label: `${m}分钟`,
			value: m
		});
	}
	return min
})

const duration = ref({
	hour: 0,
	min: 0
})

function bindChange(e) {
	const val = e.detail.value
	selectTime.value = val
	duration.value.hour = hourTime.value[val[0]]?.value
	duration.value.min = minutesTime.value[val[1]]?.value
}


function confirm() {
	if (duration.value.hour == 0 && duration.value.min == 0) {
		uni.showToast({
			title: '请选择预约时长',
			icon: 'none'
		})
		return
	}
	const obj = {
		hourItem: hourTime.value[selectTime.value[0]],
		minItem: minutesTime.value[selectTime.value[1]]
	}
	emit('confirm', duration.value, obj)
}

// 打开选择组件
const open = () => {
	durationPopupRef.value.open()

}

const close = () => {
	duration.value = {}
	durationPopupRef.value.close()

}
defineExpose({ open, close })

</script>

<style lang="scss" scoped>
.duration_page {
	height: 40vh;
	width: 100vw;
	background: $uni-bg-color;
	border-radius: 20rpx 20rpx 0rpx 0rpx;

	.duration_title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 28rpx;
		font-weight: 400;
		line-height: 40rpx;
		padding: 30rpx;

		.close {
			color: #666;
		}

		.title {
			font-weight: 500;
			font-size: 34rpx;
			color: #333333;
			line-height: 48rpx;
		}

		.confirm {
			color: $uni-color-primary;
		}
	}

	.picker-view {
		height: calc(100% - 108rpx);

		.item {
			text-align: center;
		}
	}
}
</style>
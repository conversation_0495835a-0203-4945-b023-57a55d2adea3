<template>
    <view class="site_booking">
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" title="场地预约" :border="false" @clickLeft="routerBack" :leftWidth="130" :rightWidth="130">
                    <!-- #ifdef H5 || APP-PLUS-->
                    <template #right>
                        <view v-if="hasRouters && identityType != 'eltern'" class="record_icon" @click="reservationRecord">
                            <image class="record_image" src="@nginx/workbench/siteBooking/record_icon.png" mode="scaleToFill" />
                            <text>预约记录</text>
                        </view>
                    </template>
                    <!-- #endif -->
                </uni-nav-bar>

                <!-- 日期选择 -->
                <select-date @changeDate="changeDate">
                    <!-- #ifdef  MP-WEIXIN || H5-WEIXIN -->
                    <template #left>
                        <view v-if="hasRouters && identityType != 'eltern'" class="record_icon" @click="reservationRecord">
                            <image class="record_image" src="@nginx/workbench/siteBooking/record_icon.png" mode="scaleToFill" />
                            <text>预约记录</text>
                        </view>
                    </template>
                    <!-- #endif -->
                </select-date>

                <!-- 预约类型 -->
                <site-type-list @changeType="selectType" v-if="identityType != 'eltern'" />

                <!-- tabs状态切换 -->
                <uv-tabs :list="tabs" :current="activeTab" @click="clickTabs" :activeStyle="{ color: '#00b781' }" :inactiveStyle="{ color: '#606266' }" lineWidth="20" :customStyle="{ background: '#fff' }" lineColor="#00b781" :scrollable="false"></uv-tabs>
            </template>
            <view class="site_booking_list">
                <view class="site_booking_item" @click="bookingDetail(item)" v-for="(item, index) in dataList" :key="index">
                    <view class="title_box">
                        <text class="title ellipsis">{{ item.name }}</text>
                        <view class="type_status">
                            <view class="type ellipsis">{{ item.siteBookingTypeName }}</view>
                            <view
                                class="status"
                                v-if="activeTab != 0"
                                :style="{
                                    background: statusBg[item.status]
                                }"
                                >{{ statusText[item.status] }}</view
                            >
                        </view>
                    </view>
                    <view class="info_item">
                        <text class="lable_text">预约时间：</text>
                        <text class="value_text ellipsis">{{ item.bookingTime }}</text>
                    </view>
                    <view class="info_item">
                        <text class="lable_text">场地位置：</text>
                        <text class="value_text ellipsis">
                            {{ item.siteName }}
                            <text v-if="item.floor">-{{ item.floor }}</text>
                            <text v-if="item.roomNum">-{{ item.roomNum }}</text>
                        </text>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { statusBg, statusText, tabs, identityType } from "./data"
import SelectDate from "./components/selectDate.vue"
import SiteTypeList from "./components/siteTypeList.vue"

const activeTab = ref(0) // tabs的选中下标
const paging = ref(null)
const query = ref({})
const hasRouters = ref(false) // 是否有预约记录权限
const dataList = ref([])

// 调用List数据
async function queryList(pageNo, pageSize) {
    const urlObj = {
        0: "/app/siteBooking/myParticipatePage",
        1: "/app/siteBooking/myBookingPage",
        2: "/app/siteBooking/bookingApprovalPage"
    }
    const parame = {
        pageNo,
        pageSize,
        ...query.value,
        status: activeTab.value == 2 ? 1 : null
    }
    http.post(urlObj[activeTab.value], parame)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((res) => {
            paging.value.complete(false)
        })
}

function selectType(item) {
    navigateTo({
        url: "/apps/siteBooking/addBooking",
        query: {
            siteBookingTypeId: item.id,
            isEdit: false
        }
    })
}

function changeDate(time) {
    query.value.startTime = time.startDate || ""
    query.value.endTime = time.endDate || ""
    nextTick(() => {
        paging.value?.reload()
    })
}

function reservationRecord() {
    navigateTo({
        url: "/apps/siteBooking/bookingRecord"
    })
}
function getRouters() {
    http.get("/app/siteBooking/getSiteBookingRouters").then((res) => {
        hasRouters.value = res.data?.some((i) => i.path.includes("appointmentRecord"))
    })
}

function bookingDetail(item) {
    navigateTo({
        url: "/apps/siteBooking/bookingDetail",
        query: {
            id: item.id,
            examineStatus: activeTab.value,
            isExamine: true // 首页列表查看
        }
    })
}

function clickTabs(item) {
    activeTab.value = item.index
    paging.value?.reload()
}

onShow(() => {
    getRouters()
})
</script>

<style lang="scss" scoped>
@import "./bookingCss.scss";

.site_booking {
    background: $uni-bg-color-grey;
    min-height: 100vh;

    .record_icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
        text-align: center;

        .record_image {
            width: 40rpx;
            height: 40rpx;
            margin-top: 4rpx;
        }
    }
}
</style>

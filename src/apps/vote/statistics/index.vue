<!-- 投票统计的主页面入口 投票统计往这里进入 -->
<template>
    <view class="statisticsPage">
        <!-- 条件筛选的组件 -->
        <!-- 筛选 -->
        <DropDown :menu="menu" @clickItem="clickItem" ref="dropDownRef"> </DropDown>
        <!-- 卡片页面 -->
        <!-- 滑动加载区域 -->
        <scroll-view class="scroll-Y" :scrollTop="state.scrollTop" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom">
            <view class="vote-item" v-for="(item, index) in voteList" :key="index" @click="handleClick(item)">
                <!-- 投票活动图片 -->
                <view class="vote-image-container">
                    <image class="vote-image" :src="item.image" mode="aspectFill" />
                    <!-- 进行中状态标签 -->
                    <view class="status-tag">
                        <text class="status-text">进行中</text>
                    </view>
                </view>

                <!-- 投票活动信息 -->
                <view class="vote-info">
                    <view class="vote-title">{{ item.title }}</view>
                    <!-- uni-countdown倒计时组件 -->
                    <view class="countdown-container">
                        <view class="time-icon"></view>
                        <view class="countdown-wrapper">
                            <text class="countdown-label">距离结束还剩：</text>
                            <uni-countdown :timestamp="item.endTimestamp" color="#666" background-color="transparent" border-color="transparent" :show-day="true" :show-colon="false" splitor-color="#666" @timeup="onTimeUp(item)" />
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
</template>

<script setup>
import DropDown from "../comp/dropDown.vue"

import { reactive, ref } from "vue"

const state = reactive({
    scrollTop: 0
})

const menu = ref({
    type: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "单选投票", value: "1" },
            { label: "多选投票", value: "2" },
            { label: "二选一PK投票", value: "3" },
            { label: "评选投票活动", value: "4" }
        ]
    },
    status: {
        label: "全部",
        child: [
            { label: "全部", value: "all" },
            { label: "未开始", value: "1" },
            { label: "进行中", value: "2" },
            { label: "已结束", value: "3" }
        ]
    }
})

/**
 * 投票活动列表数据
 */
const voteList = reactive([
    {
        id: 1,
        title: "活动标题活动标题",
        image: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        endTime: "2025-06-23 15:45:00", // 结束时间
        status: "ongoing"
    },
    {
        id: 2,
        title: "活动标题活动标题活动标题活动标题活动...",
        image: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        endTime: "2025-06-25 10:30:00", // 结束时间
        status: "ongoing"
    }
])

const onTimeUp = () => {}


const handleClick = (item) => {
    navigateTo({
        url: "/apps/vote/statistics/details",
        query: item
    })
}
</script>

<style lang="scss" scoped>
.statisticsPage {
    min-height: 100vh;
    background: $uni-bg-color-grey;
}

// 每个投票项的样式
.vote-item {
    margin: 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

    // 投票图片容器
    .vote-image-container {
        position: relative;
        width: 100%;
        height: 320rpx;

        .vote-image {
            width: 100%;
            height: 100%;
        }

        // 状态标签
        .status-tag {
            position: absolute;
            bottom: 24rpx;
            left: 24rpx;
            background-color: #07c160;
            border-radius: 24rpx;
            padding: 8rpx 20rpx;

            .status-text {
                font-size: 24rpx;
                color: #fff;
                font-weight: 500;
            }
        }
    }

    // 投票信息区域
    .vote-info {
        padding: 24rpx;

        // 投票标题
        .vote-title {
            font-size: 32rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
            margin-bottom: 16rpx;
            // 超出部分显示省略号
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        // 倒计时容器样式
        .countdown-container {
            display: flex;
            align-items: center;

            .time-icon {
                width: 24rpx;
                height: 24rpx;
                background-color: #07c160;
                border-radius: 50%;
                margin-right: 12rpx;
                flex-shrink: 0;
            }

            .countdown-wrapper {
                display: flex;
                align-items: center;
                flex: 1;

                .countdown-label {
                    font-size: 28rpx;
                    color: #666;
                    margin-right: 8rpx;
                }
            }
        }
    }
}

.scroll-Y {
    background-color: #f9faf9;
    // padding: 30rpx;
}
</style>

# 倒计时组件 (Countdown)

一个基于 Vue 3 和 dayjs 的灵活倒计时组件，支持多种时间格式和自定义显示配置。

## 功能特性

- ✅ 支持时间戳和时间字符串格式
- ✅ 可配置显示天、时、分、秒
- ✅ 自定义颜色和字体大小
- ✅ 自动处理时间到达事件
- ✅ 响应式设计，适配移动端
- ✅ 基于 dayjs 精确计算时间差

## 属性配置

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| endTime | String/Number | - | ✅ | 结束时间，支持时间戳或时间字符串 |
| showDay | Boolean | true | ❌ | 是否显示天数 |
| showHour | Boolean | true | ❌ | 是否显示小时 |
| showMinute | Boolean | true | ❌ | 是否显示分钟 |
| showSecond | Boolean | true | ❌ | 是否显示秒数 |
| numberColor | String | '#333' | ❌ | 数字颜色 |
| labelColor | String | '#666' | ❌ | 标签颜色 |
| numberSize | String | '28rpx' | ❌ | 数字字体大小 |
| labelSize | String | '24rpx' | ❌ | 标签字体大小 |

## 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| timeup | 倒计时结束时触发 | 无 |

## 使用示例

### 基础用法

```vue
<template>
  <Countdown 
    :end-time="'2025-12-31 23:59:59'"
    @timeup="handleTimeUp"
  />
</template>

<script setup>
import Countdown from '@/components/Countdown.vue'

const handleTimeUp = () => {
  console.log('倒计时结束！')
}
</script>
```

### 使用时间戳

```vue
<template>
  <Countdown 
    :end-time="1735689599000"
    @timeup="handleTimeUp"
  />
</template>
```

### 自定义显示

```vue
<template>
  <!-- 只显示时分秒 -->
  <Countdown 
    :end-time="endTime"
    :show-day="false"
    :show-hour="true"
    :show-minute="true"
    :show-second="true"
    number-color="#07c160"
    label-color="#999"
    @timeup="handleTimeUp"
  />
</template>
```

### 自定义样式

```vue
<template>
  <Countdown 
    :end-time="endTime"
    number-color="#e03131"
    label-color="#495057"
    number-size="40rpx"
    label-size="32rpx"
    @timeup="handleTimeUp"
  />
</template>
```

## 支持的时间格式

### 时间字符串格式
- `'2025-12-31 23:59:59'`
- `'2025/12/31 23:59:59'`
- `'2025-12-31T23:59:59'`
- 其他 dayjs 支持的时间格式

### 时间戳格式
- `1735689599000` (毫秒时间戳)
- `1735689599` (秒时间戳，dayjs 会自动识别)

## 注意事项

1. 组件会自动处理时区问题，使用本地时间进行计算
2. 当倒计时结束时，所有时间单位会自动归零
3. 组件会在卸载时自动清理定时器，避免内存泄漏
4. 如果传入的时间格式无效，组件会在控制台输出错误信息并将时间归零

## 替换 uni-countdown

如果你之前使用的是 `uni-countdown` 组件，可以按照以下方式进行替换：

### 原来的代码
```vue
<uni-countdown 
  :day="1" 
  :hour="1" 
  :minute="12" 
  color="#666" 
  background-color="transparent" 
  border-color="transparent" 
  :show-day="true" 
  :show-colon="false" 
  splitor-color="#333333" 
  @timeup="onTimeUp" 
/>
```

### 替换后的代码
```vue
<Countdown 
  :end-time="item.endTime" 
  :show-day="true" 
  :show-hour="true" 
  :show-minute="true" 
  :show-second="true"
  number-color="#666"
  label-color="#666"
  @timeup="onTimeUp" 
/>
```

主要区别：
- 不再需要手动计算天、时、分，只需传入结束时间
- 使用 `endTime` 属性替代分别设置天时分
- 颜色配置更加直观
- 支持更多的时间格式

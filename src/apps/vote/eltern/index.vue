<template>
  <view class="vote-page">
    <!-- 选项卡切换 -->
    <view class="tab-bar">
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'ongoing' }"
        @click="switchTab('ongoing')"
      >
        <text class="tab-text">进行中</text>
      </view>
      <view 
        class="tab-item" 
        :class="{ active: currentTab === 'finished' }"
        @click="switchTab('finished')"
      >
        <text class="tab-text">已结束</text>
      </view>
    </view>

    <!-- 投票列表内容 -->
    <scroll-view class="vote-list" scroll-y>
      <view class="vote-item" v-for="(item, index) in voteList" :key="index">
        <!-- 投票活动图片 -->
        <view class="vote-image-container">
          <image 
            class="vote-image" 
            :src="item.image" 
            mode="aspectFill"
          />
          <!-- 进行中状态标签 -->
          <view class="status-tag">
            <text class="status-text">进行中</text>
          </view>
        </view>
        
        <!-- 投票活动信息 -->
        <view class="vote-info">
          <view class="vote-title">{{ item.title }}</view>
          <!-- uni-countdown倒计时组件 -->
          <view class="countdown-container">
            <view class="time-icon"></view>
            <view class="countdown-wrapper">
              <text class="countdown-label">距离结束还剩：</text>
              <uni-countdown 
                :timestamp="item.endTimestamp"
                color="#666"
                background-color="transparent"
                border-color="transparent"
                :show-day="true"
                :show-colon="false"
                splitor-color="#666"
                @timeup="onTimeUp(item)"
              />
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

/**
 * 当前选中的选项卡
 * ongoing: 进行中
 * finished: 已结束
 */
const currentTab = ref('ongoing')

/**
 * 投票活动列表数据
 */
const voteList = reactive([
  {
    id: 1,
    title: '活动标题活动标题',
    image: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    endTime: '2025-06-23 15:45:00', // 结束时间
    status: 'ongoing'
  },
  {
    id: 2,
    title: '活动标题活动标题活动标题活动标题活动...',
    image: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    endTime: '2025-06-25 10:30:00', // 结束时间
    status: 'ongoing'
  }
])

/**
 * 计算每个活动的结束时间戳
 * uni-countdown需要时间戳格式
 */
voteList.forEach(item => {
  item.endTimestamp = new Date(item.endTime).getTime()
})

/**
 * 切换选项卡
 * @param {string} tab - 选项卡类型
 */
const switchTab = (tab) => {
  currentTab.value = tab
  // 这里可以根据不同的tab加载不同的数据
  console.log('切换到:', tab)
}

/**
 * 倒计时结束回调
 * @param {Object} item - 投票活动项
 */
const onTimeUp = (item) => {
  console.log('投票活动结束:', item.title)
  // 可以在这里处理倒计时结束后的逻辑
  // 比如更新活动状态、显示结束提示等
  item.status = 'finished'
  uni.showToast({
    title: '活动已结束',
    icon: 'none'
  })
}
</script>

<style lang="scss" scoped>
.vote-page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;

  // 选项卡栏样式
  .tab-bar {
    display: flex;
    background-color: #fff;
    border-bottom: 1rpx solid #f0f0f0;

    .tab-item {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .tab-text {
        font-size: 32rpx;
        color: #999;
        transition: color 0.3s;
      }

      // 选中状态样式
      &.active {
        .tab-text {
          color: #07c160;
          font-weight: 500;
        }

        // 底部绿色指示线
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 4rpx;
          background-color: #07c160;
          border-radius: 2rpx;
        }
      }
    }
  }

  // 投票列表容器
  .vote-list {
    padding: 24rpx 32rpx;

    // 每个投票项的样式
    .vote-item {
      background-color: #fff;
      border-radius: 16rpx;
      overflow: hidden;
      margin-bottom: 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);

      // 投票图片容器
      .vote-image-container {
        position: relative;
        width: 100%;
        height: 320rpx;

        .vote-image {
          width: 100%;
          height: 100%;
        }

        // 状态标签
        .status-tag {
          position: absolute;
          bottom: 24rpx;
          left: 24rpx;
          background-color: #07c160;
          border-radius: 24rpx;
          padding: 8rpx 20rpx;

          .status-text {
            font-size: 24rpx;
            color: #fff;
            font-weight: 500;
          }
        }
      }

      // 投票信息区域
      .vote-info {
        padding: 24rpx;

        // 投票标题
        .vote-title {
          font-size: 32rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          margin-bottom: 16rpx;
          // 超出部分显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // 倒计时容器样式
        .countdown-container {
          display: flex;
          align-items: center;

          .time-icon {
            width: 24rpx;
            height: 24rpx;
            background-color: #07c160;
            border-radius: 50%;
            margin-right: 12rpx;
            flex-shrink: 0;
          }

          .countdown-wrapper {
            display: flex;
            align-items: center;
            flex: 1;

            .countdown-label {
              font-size: 28rpx;
              color: #666;
              margin-right: 8rpx;
            }
          }
        }
      }
    }
  }
}

// 自定义uni-countdown组件样式
:deep(.uni-countdown) {
  .uni-countdown__number {
    background-color: transparent !important;
    border: none !important;
    color: #666 !important;
    font-size: 28rpx !important;
    font-weight: normal !important;
  }
  
  .uni-countdown__splitor {
    color: #666 !important;
    font-size: 28rpx !important;
  }
}
</style>

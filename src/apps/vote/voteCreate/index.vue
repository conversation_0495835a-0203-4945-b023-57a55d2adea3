<template>
    <view class="container">
        <!-- 单选投票卡片 -->
        <view class="vote-card" @click="handleVoteType(1)">
            <view class="card-content">
                <view class="title">单选投票</view>
                <view class="desc">每个参与者只能选择一个选项投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 多选投票卡片 -->
        <view class="vote-card" @click="handleVoteType(2)">
            <view class="card-content">
                <view class="title">多选投票</view>
                <view class="desc">每个参与者只能选择多个选项投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 二选一PK投票卡片 -->
        <view class="vote-card" @click="handleVoteType(3)">
            <view class="card-content">
                <view class="title">二选一PK投票</view>
                <view class="desc">快速创建红方、蓝方二选一对比投票</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>

        <!-- 评选投票活动卡片 -->
        <view class="vote-card" @click="handleVoteType(4)">
            <view class="card-content">
                <view class="title">评选投票活动</view>
                <view class="desc">用于优秀人物评选等场景</view>
            </view>
            <uni-icons type="arrowright" size="18" color="#C0C4CC"></uni-icons>
        </view>
        <BottomTabBar></BottomTabBar>
    </view>
</template>

<script setup>
import { ref } from "vue"

import useVoteStore from "@/store/vote"

import BottomTabBar from "../comp/bottomTabBar.vue"

// 使用 Pinia store 获取表单数据
const voteStore = useVoteStore()

/**
 * 处理投票类型选择
 * @param {string} type - 投票类型 1|2|3|4
 */
const handleVoteType = (type) => {
    voteStore.setVoteType(type)
    // voteStore.resetData()
    uni.navigateTo({
        url: "/apps/vote/voteCreate/createForm"
    })
}
</script>

<style lang="scss" scoped>
// 容器样式
.container {
    background: #f9faf9;
    min-height: 100vh;
    padding: 30rpx;
    box-sizing: border-box;
}

// 投票卡片样式
.vote-card {
    height: 194rpx;
    background: #ffffff;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    padding: 40rpx 30rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    // 添加点击效果
    &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    // 最后一个卡片不需要下边距
    &:last-child {
        margin-bottom: 0;
    }
}

// 卡片内容区域
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

// 标题样式
.title {
    font-size: 32rpx;
    font-weight: 600;
    color: #303133;
    line-height: 44rpx;
    margin-bottom: 16rpx;
}

// 描述样式
.desc {
    font-size: 28rpx;
    color: #909399;
    line-height: 40rpx;
    opacity: 0.8;
}

// 箭头图标容器
.arrow-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
}
</style>

<template>
    <view class="vote-detail-page">
        <!-- 筛选 -->
        <!-- <drop-down :menu="menu" @clickItem="clickItem" ref="dropDownRef"> </drop-down> -->
        <!-- 视频播放区域 -->
        <view class="video-container">
            <image class="video-placeholder" :src="videoPlaceholder" mode="aspectFill" />

            <view class="vote-status">
                <image src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" class="status-dot"></image>
                <text class="status-text">距离结束还剩：</text>
                <text class="countdown">
                    <uni-countdown format="HH:mm:ss" :show-second="false" :show-colon="false" :font-size="18" color="#FFFFFF" background-color="#333333" :show-day="true" :hour="12" :minute="12" :second="12" />
                </text>
            </view>
        </view>

        <!-- 投票信息区域 -->
        <view class="vote-info">
            <view claas="vote-title">
                {{ state.voteInfo.title }}
            </view>

            <view class="vote-description">
                <text class="description-text"> {{ state.voteInfo.desc }} </text>
            </view>
        </view>

        <!-- 投票选项卡片区域 -->
        <view class="vote-options">
            <uni-row class="options-grid">
                <uni-col :span="12" v-for="(option, index) in state.voteInfo.options" :key="option.id">
                    <view class="option-card-wrapper">
                        <view class="option-card" :class="{ selected: selectedOption === option.id }" @click="selectOption(option)">
                            <!-- 图片占位区域 -->
                            <view class="avatar-container" v-if="option.files[0] && option.files[0].url">
                                <image class="candidate-avatar" :src="option.files[0].url" mode="aspectFill" />
                            </view>
                            <view class="avatar-container" v-else> </view>
                            <!-- 左上角选择标识 -->
                            <view class="selection-indicator" v-if="state.voteInfo.type === 1">
                                <radio style="pointer-events: none" color="#11C685" v-if="selectedOption === option.id" value="r1" :checked="true" />
                                <radio style="pointer-events: none" color="#11C685" v-else value="r1" :checked="false" />
                            </view>

                            <view class="candidate-number_box"> {{ index + 1 }}号 </view>

                            <!-- 信息区域 -->
                            <view class="card-info">
                                <text class="candidate-name">{{ option.title }}</text>
                                <text class="vote-count">{{ option.countNum }} <text class="vote-count-unit">票</text></text>
                                <text class="candidate-description">{{ option.desc }}</text>

                                <!-- 根据类型存在的一个票数选择器 -->
                                <uni-number-box v-if="state.voteInfo.type === 2 || state.voteInfo.type === 4" class="number-box" @change="changeValue" />
                            </view>
                        </view>
                    </view>
                </uni-col>
            </uni-row>
        </view>

        <!-- 底部固定按钮 -->
        <view class="bottom-actions">
            <button class="vote-button" type="primary" @click="handleVote">投票</button>
            <!-- <button class="share-button" @click="handleShare">分享</button> -->
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue"
import DropDown from "../comp/dropDown.vue"
import { onLoad } from "@dcloudio/uni-app"

const state = reactive({
    voteId: null,
    voteInfo: {}
})

// 响应式数据定义
const days = ref(12)
const hours = ref(32)
const minutes = ref(45)

const menu = ref({
    type: {
        label: "全部活动",
        child: [
            { label: "全部活动", value: "all" },
            { label: "我创建的", value: "2" },
            { label: "我报名的", value: "1" }
        ]
    },

    status: {
        label: "报名状态",
        child: [
            { label: "报名状态", value: "all" },
            { label: "未开始", value: "1" },
            { label: "报名中", value: "2" },
            { label: "已结束", value: "3" },
            { label: "已关闭", value: "4" }
        ]
    }
})

// 视频占位图
const videoPlaceholder = ref("https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png")

// 当前选中的投票选项
const selectedOption = ref(null)

// 投票选项数据
const voteOptions = ref([
    {
        id: 1,
        number: 1,
        name: "名称名称",
        avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        voteCount: 68,
        description: "参赛描述参赛描述"
    },
    {
        id: 2,
        number: 2,
        name: "名称名称",
        avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        voteCount: 45,
        description: "参赛描述参赛描述"
    },
    {
        id: 3,
        number: 3,
        name: "名称名称",
        avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        voteCount: 32,
        description: "参赛描述参赛描述"
    },
    {
        id: 4,
        number: 4,
        name: "名称名称",
        avatar: "https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png",
        voteCount: 28,
        description: "参赛描述参赛描述"
    }
])

// 计算是否可以投票
const canVote = computed(() => {
    // 根据投票状态判断，这里假设投票尚未开始所以不能投票
    return false
})

const selectOption = (item) => {
    selectedOption.value = item.id

    // 添加触觉反馈
    uni.vibrateShort()

    // 显示选择提示
    uni.showToast({
        title: `已选择${item.title}`,
        icon: "none",
        duration: 1500
    })
}

/**
 * 处理投票提交
 */
const handleVote = () => {
    // if (!selectedOption.value) {
    //     uni.showToast({
    //         title: "请先选择一个选手",
    //         icon: "none"
    //     })
    //     return
    // }

    // if (!canVote.value) {
    //     uni.showToast({
    //         title: "投票尚未开始",
    //         icon: "none"
    //     })
    //     return
    // }

    // // 显示确认对话框
    // uni.showModal({
    //     title: "确认投票",
    //     content: `确定为${selectedOption.value}号选手投票吗？`,
    //     success: function (res) {
    //         if (res.confirm) {
    //             // 执行投票逻辑
    //             console.log("投票给选手：", selectedOption.value)
    //             uni.showToast({
    //                 title: "投票成功",
    //                 icon: "success"
    //             })

    //             // 这里可以调用API提交投票
    //             // submitVote(selectedOption.value)
    //         }
    //     }
    // })

    // 如果是单选投票的话 只有一个
    const voteDetails = [
        {
            optionId: selectedOption.value,
            countNum: 1
        }
    ]
    console.log(voteDetails, "voteDetails")

    http.post("/app/vote/clickVote", {
        voteId: state.voteId,
        voteDetails: voteDetails
    })
}

// 页面加载时初始化
onMounted(() => {
    console.log("投票详情页面加载完成")
    // 这里可以添加页面初始化逻辑，比如获取投票数据等
})

// 获取投票详情
const getVote = () => {
    http.post("/app/vote/get", {
        voteId: state.voteId
    }).then((res) => {
        console.log(res, "详情详情详情详情432432432423")
        state.voteInfo = res.data
    })
}

onLoad((options) => {
    console.log("options", options)
    state.voteId = options.voteId
    getVote()
})
</script>

<style lang="scss" scoped>
.vote-detail-page {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding-bottom: 160rpx; // 为底部固定按钮预留空间
}

// 视频播放区域样式
.video-container {
    position: relative;

    padding: 30rpx;

    background-color: #ffffff;
    margin-bottom: 20rpx;

    .video-placeholder {
        width: 100%;
        height: 280rpx;
        border-radius: 20rpx;
    }
    .vote-status {
        display: flex;
        align-items: center;
        padding-top: 20rpx;
        padding-bottom: 20rpx;

        .status-dot {
            width: 50rpx;
            height: 50rpx;

            margin-right: 16rpx;
        }

        .status-text {
            font-weight: 600;
            font-size: 36rpx;
            color: #333333;
            margin-right: 20rpx;
        }

        .countdown {
            font-size: 28rpx;
            color: #666;
        }
    }
}

// 投票信息区域样式
.vote-info {
    background-color: #fff;
    padding: 30rpx;
    margin-bottom: 20rpx;

    .vote-description {
        .description-text {
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
        }
    }
}

// 投票选项卡片区域样式
.vote-options {
    padding: 0 20rpx;

    .options-grid {
        .option-card-wrapper {
            margin: 10rpx;

            .option-card {
                position: relative;
                background-color: #fff;
                border-radius: 20rpx;

                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
                border: 3rpx solid transparent;

                // 选中状态样式
                &.selected {
                    border-color: #11c685;
                }

                // 左上角选择标识
                .selection-indicator {
                    position: absolute;
                    top: 20rpx;
                    left: 20rpx;
                }

                // 头像容器
                .avatar-container {
                    display: flex;
                    justify-content: center;
                    height: 223rpx;
                    width: 100%;
                    .candidate-avatar {
                        height: 223rpx;
                        border-radius: 20rpx;
                    }
                }

                // 信息区域
                .card-info {
                    text-align: center;

                    padding: 30rpx;

                    .candidate-name {
                        display: block;

                        margin-bottom: 8rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #333333;
                    }

                    .vote-count {
                        display: block;
                        font-size: 28rpx;
                        color: #11c685;
                        font-weight: 400;
                        margin-bottom: 12rpx;
                        .vote-count-unit {
                            color: #333333;
                        }
                    }

                    .candidate-description {
                        display: block;
                        margin-bottom: 12rpx;
                        font-weight: 400;
                        font-size: 26rpx;
                        color: #333333;
                    }
                }

                // 点击效果
                &:active {
                    transform: scale(0.98);
                }
            }
        }
    }
}

// 底部固定按钮样式
.bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;

    padding-top: 30rpx;
    padding-right: 30rpx;
    padding-bottom: calc(44rpx + env(safe-area-inset-bottom));
    padding-left: 30rpx;
    border-top: 1rpx solid #e5e5e5;
    display: flex;
    gap: 20rpx;
    box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

    .vote-button {
        flex: 1;
        background-color: #07c160;
        color: #fff;
        border: none;
        border-radius: 10rpx;
        height: 92rpx;
        font-size: 32rpx;
        font-weight: 400;

        &[disabled] {
            background-color: #c8c9cc;
            color: #999;
        }

        &:not([disabled]):active {
            background-color: #05a652;
            transform: scale(0.98);
        }
    }

    .share-button {
        flex: 1;
        background-color: #fff;
        color: #333;
        border: 2rpx solid #e5e5e5;
        border-radius: 12rpx;
        height: 88rpx;
        font-size: 32rpx;

        &:active {
            background-color: #f8f9fa;
            transform: scale(0.98);
        }
    }
}

// 隐藏秒
:deep(.uni-countdown) {
    uni-text:nth-last-of-type(1) {
        display: none !important;
    }
    uni-text:nth-last-of-type(2) {
        display: none !important;
    }
}

.candidate-number_box {
    position: absolute;
    width: 88rpx;
    height: 40rpx;
    background: #11c685;
    border-radius: 10rpx;

    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    top: 204rpx;
    left: 120rpx;
}

.number-box {
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<template>
    <view class="vote-page">
        <!-- 选项卡切换 - 固定在顶部 -->
        <view class="tab-bar">
            <view class="tab-item" :class="{ active: currentTab === 'ongoing' }" @click="switchTab(1)">
                <text class="tab-text">进行中</text>
            </view>
            <view class="tab-item" :class="{ active: currentTab === 'finished' }" @click="switchTab(2)">
                <text class="tab-text">已结束</text>
            </view>
        </view>

        <scroll-view class="scroll-Y" :scrollTop="state.scrollTop" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom">
            <view class="vote-item" v-for="(item, index) in state.voteList" :key="index" @click="handleClick(item)">
                <!-- 投票活动图片 -->
                <view class="vote-image-container">
                    <image class="vote-image" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" mode="aspectFill" />
                </view>

                <!-- 投票活动信息 -->
                <view class="vote-info">
                    <!-- 状态标签 -->
                    <view class="status-container">
                        <!-- 状态标签 -->
                        <view class="status-tag" :class="{ finished: item.status === 2 }">
                            <text class="status-text">{{ item.status === 1 ? "进行中" : "已结束" }}</text>
                        </view>
                        <!--标题 -->
                        <view class="vote-title">{{ item.title }}</view>
                    </view>

                    <!-- 倒计时组件 - 只在进行中状态显示 -->
                    <view class="countdown-container" v-if="item.status === 1">
                        <view class="time-icon"></view>
                        <view class="countdown-wrapper">
                            <text class="countdown-label">距离结束还剩：</text>
                            <Countdown :end-time="item.endTime" :show-day="true" :show-hour="true" :show-minute="true" :show-second="false" number-color="#666" label-color="#666" @timeup="onTimeUp(item)" />
                        </view>
                    </view>
                    <!-- 已结束状态显示结束时间 -->
                    <view class="end-time-container" v-else>
                        <view class="time-icon finished"></view>
                        <text class="end-time-text">活动时间：{{ `${item.startTime} - ${item.endTime}` }}</text>
                    </view>
                </view>
            </view>

            <!-- <uni-load-more :showText="state.loadMore.showText" :contentText="state.loadMore.contentText" :status="state.loadMore.status" @clickLoadMore="state.scrollTop = 0" /> -->
        </scroll-view>

        <!-- 底部导航栏 - 固定在底部 -->
        <BottomTabBar></BottomTabBar>
    </view>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue"

import BottomTabBar from "../comp/bottomTabBar.vue"
import Countdown from "../comp/Countdown.vue"

// 响应式状态数据
const state = reactive({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    status: 1, // 1: 进行中, 2: 已结束
    voteList: [], // 投票列表数据
    loading: false, // 是否正在加载
    loadingMore: false, // 是否正在加载更多
    noMoreData: false, // 是否没有更多数据
    refresherTriggered: false // 下拉刷新状态
})

/**
 * 当前选中的选项卡
 * ongoing: 进行中
 * finished: 已结束
 */
const currentTab = ref("ongoing")

/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
    getVotePage()
})

/**
 * 获取投票列表数据
 * @param {boolean} isRefresh - 是否为刷新操作
 * @param {boolean} isLoadMore - 是否为加载更多操作
 */
const getVotePage = async (isRefresh = false, isLoadMore = false) => {
    try {
        // 设置加载状态
        if (isRefresh) {
            state.loading = true
        } else if (isLoadMore) {
            state.loadingMore = true
        } else {
            state.loading = true
        }

        const response = await http.post("/app/vote/page", {
            status: state.status,
            pageNo: state.pageNo,
            pageSize: state.pageSize
        })

        const { data } = response

        if (data && data.list) {
            // 处理时间戳转换
            const processedList = data.list.map((item) => ({
                ...item,
                endTimestamp: new Date(item.endTime).getTime()
            }))

            if (isLoadMore) {
                // 加载更多：追加数据
                state.voteList = [...state.voteList, ...processedList]
            } else {
                // 刷新或初始加载：替换数据
                state.voteList = processedList
            }

            // 更新总数和分页信息
            state.total = data.total || 0

            // 判断是否还有更多数据
            const hasMore = state.voteList.length < state.total
            state.noMoreData = !hasMore
        }
    } catch (error) {
        console.error("获取投票列表失败:", error)
        uni.showToast({
            title: "获取数据失败",
            icon: "none"
        })
    } finally {
        // 重置加载状态
        state.loading = false
        state.loadingMore = false
        state.refresherTriggered = false
    }
}

/**
 * 下拉刷新
 */
const onRefresh = () => {
    state.refresherTriggered = true
    state.pageNo = 1
    state.noMoreData = false
    getVotePage(true, false)
}

/**
 * 上拉加载更多
 */
const onLoadMore = () => {
    // 如果正在加载或没有更多数据，则不执行
    if (state.loadingMore || state.noMoreData) {
        return
    }

    state.pageNo += 1
    getVotePage(false, true)
}

/**
 * 滚动到底部处理函数
 */
const handlerScrollBottom = () => {
    onLoadMore()
}

/**
 * 切换选项卡
 * @param {number} status - 选项卡状态 (1: 进行中, 2: 已结束)
 */
const switchTab = (status) => {
    if (state.status === status) return

    state.status = status
    state.pageNo = 1
    state.noMoreData = false
    state.voteList = []

    // 更新当前选项卡显示
    currentTab.value = status === 1 ? "ongoing" : "finished"

    // 获取新数据
    getVotePage()
}

/**
 * 倒计时结束回调
 * @param {Object} item - 投票活动项
 */
const onTimeUp = (item) => {
    console.log("投票活动结束:", item.title)
    // 更新活动状态为已结束
    item.status = "finished"
    uni.showToast({
        title: "活动已结束",
        icon: "none"
    })
}

const handleClick = (item)=>{
    navigateTo({
        url: "/apps/vote/operation/index",
        query: {
            voteId: item.id
        }
    })
}
</script>

<style lang="scss" scoped>
.vote-page {
    min-height: 100vh;
    background-color: #f5f5f5;

    // 选项卡栏样式 - 固定在顶部
    .tab-bar {
        display: flex;
        background-color: #fff;

        .tab-item {
            flex: 1;
            height: 88rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .tab-text {
                font-size: 30rpx;
                color: #999999;
                transition: color 0.3s;
            }

            // 选中状态样式
            &.active {
                .tab-text {
                    color: #11c685;
                    font-weight: 500;
                }

                // 底部绿色指示线
                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 60rpx;
                    height: 4rpx;
                    background-color: #07c160;
                    border-radius: 2rpx;
                }
            }
        }
    }

    // 每个投票项的样式
    .vote-item {
        overflow: hidden;
        margin: 30rpx;
        background: #ffffff;
        box-shadow: 0rpx 14rpx 16rpx 0rpx rgba(227, 227, 227, 0.5);
        border-radius: 20rpx;

        // 投票图片容器
        .vote-image-container {
            position: relative;
            width: 100%;
            height: 320rpx;

            .vote-image {
                width: 100%;
                height: 100%;
            }
        }

        // 投票信息区域
        .vote-info {
            padding: 24rpx;

            // 投票标题
            .vote-title {
                font-size: 32rpx;
                color: #333;
                font-weight: 500;

                // 超出部分显示省略号
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            // 倒计时容器样式
            .countdown-container {
                display: flex;
                align-items: center;

                .time-icon {
                    width: 24rpx;
                    height: 24rpx;
                    background-color: #07c160;
                    border-radius: 50%;
                    margin-right: 12rpx;
                    flex-shrink: 0;
                }

                .countdown-wrapper {
                    display: flex;
                    align-items: center;
                    flex: 1;

                    .countdown-label {
                        font-size: 28rpx;
                        color: #666;
                        margin-right: 8rpx;
                    }
                }
            }

            // 已结束时间容器样式
            .end-time-container {
                display: flex;
                align-items: center;

                .time-icon {
                    width: 24rpx;
                    height: 24rpx;
                    background-color: #999;
                    border-radius: 50%;
                    margin-right: 12rpx;
                    flex-shrink: 0;

                    &.finished {
                        background-color: #999;
                    }
                }

                .end-time-text {
                    font-size: 28rpx;
                    color: #666;
                }
            }
        }
    }

    // 加载更多状态样式
    .load-more {
        padding: 40rpx 0;
        text-align: center;

        .loading,
        .no-more {
            .loading-text,
            .no-more-text {
                font-size: 28rpx;
                color: #999;
            }
        }
    }

    // 空状态样式
    .empty-state {
        padding: 120rpx 0;
        text-align: center;

        .empty-text {
            font-size: 32rpx;
            color: #999;
        }
    }
}

.scroll-Y {
    height: calc(100vh - 300rpx);
    overflow: hidden auto;
}

// 状态标签
.status-tag {
    background-color: #07c160;
    width: 80rpx;
    height: 36rpx;
    background: #11c685;
    border-radius: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .status-text {
        font-size: 24rpx;
        color: #fff;
        font-weight: 500;
    }

    // 已结束状态样式
    &.finished {
        background-color: #999;
    }
}

.status-container {
    display: flex;
    align-items: center;
}
</style>

<template>
    <view class="teacher_detail_container">
        <view class="top_box">
            <view class="left_text">{{ teaData.name.charAt(0) }}</view>
            <view class="right_text">
                <view>{{ teaData.name }}</view>
                <view class="btn_text">学籍号： {{ teaData.studentCode }}</view>
            </view>
        </view>
        <view class="main_box">
            <view class="top_text">关联的家长</view>
            <view class="msg_box" v-for="(item, index) in teaData.elterns" :key="index">
                <view class="left">{{ item.name }}</view>
                <view class="main">{{ relationsType[item.relations] }}</view>
                <view class="right">{{ item.phone }}</view>
            </view>
        </view>
    </view>
</template>
<script setup>
let teaData = reactive({})

onLoad((params) => {
    params.elterns = JSON.parse(params.elterns)
    Object.keys(params).forEach((i) => (teaData[i] = params[i]))
})

const relationsType = {
    1: "父亲",
    2: "母亲",
    3: "爷爷",
    4: "奶奶",
    5: "外公",
    6: "外婆",
    7: "其他"
}
</script>
<style lang="scss">
.teacher_detail_container {
    background-color: $uni-bg-color-grey;
    .top_box {
        background-color: $uni-bg-color;
        padding: 40rpx 28rpx;
        display: flex;
        .left_text {
            display: inline-block;
            width: 130rpx;
            height: 130rpx;
            text-align: center;
            line-height: 130rpx;
            color: $uni-text-color-inverse;
            font-size: 32rpx;
            border-radius: 50%;
            background-color: $uni-color-primary;
            font-weight: 600;
            margin-right: 24rpx;
        }
        .right_text {
            font-size: 32rpx;
            color: $uni-text-color;
            font-weight: 600;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .btn_text {
                font-size: 28rpx;
                font-weight: normal;
            }
        }
    }
    .main_box {
        margin-top: 20rpx;
        padding: 0 28rpx;
        background-color: $uni-bg-color;
        .top_text {
            color: $uni-text-color-grey;
            padding: 20rpx 0;
            border-bottom: 1rpx solid $uni-border-color;
        }
        .msg_box {
            display: flex;
            padding: 28rpx 0;
            border-bottom: 1rpx solid $uni-border-color;
            &:last-of-type {
                border-bottom: none;
            }
            .left {
                width: 220rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                margin-right: 45rpx;
            }
            .main {
                width: 190rpx;
            }
        }
    }
}
</style>

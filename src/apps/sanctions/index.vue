<template>
    <!-- 评价系统 -->
    <div>
        <!-- 家长和学生是一个页面 -->
        <eltern-home v-if="['eltern', 'student'].includes(identityType)" :identityType="identityType" />
        <!-- 老师一个页面 -->
        <teacher-home v-else :identityType="identityType" />
    </div>
</template>

<script setup>
import ElternHome from "./eltern.vue"
import TeacherHome from "./teacher.vue"
import useStore from "@/store"
const { user } = useStore()

const identityType = computed(() => {
    debugger
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
</script>

<style lang="scss" scoped></style>

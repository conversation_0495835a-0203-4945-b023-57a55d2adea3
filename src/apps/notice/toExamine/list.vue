<template>
    <view class="to-examine">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="审核列表"> </uni-nav-bar>
                <view class="handler">
                    <DropDownFiltering @emitChangeSelect="emitChangeSelect" :tabKey="state.activeTab" :tabType="state.form.type">
                        <template #leftSelect>
                            <uni-data-select v-model="state.form.approveStatus" :localdata="myTypeList" @change="changeSelect" :clear="false"></uni-data-select>
                        </template>
                    </DropDownFiltering>
                </view>
            </template>
            <view class="examine_list">
                <view class="examine-item" v-for="(item, index) in state.dataList" :key="index" @click="handlerApprove(item)">
                    <view class="cell-item-content">
                        <view class="title">
                            {{ item.title }}
                        </view>
                        <view class="type"> 类型：{{ item.messTypeName }} </view>
                        <view class="cloth-in">
                            <view>
                                {{ item.identityUserName }}发布于<text> {{ item.timerDate }}</text>
                            </view>
                            <view :style="{ color: statusObj[item.approveStatus]?.color }">{{ statusObj[item.approveStatus].text }} </view>
                        </view>
                    </view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import { reactive, ref } from "vue"
import DropDownFiltering from "../components/dropDownFiltering.vue"
const myTypeList = [
    { text: "全部", value: "" },
    { text: "拒绝", value: "0" },
    { text: "审核中", value: "1" },
    { text: "已通过", value: "2" }
]
const statusObj = {
    0: { text: "拒绝", color: "#fd4f45" },
    1: { text: "审核中", color: "#f0ad4e" },
    2: { text: "已审核", color: "#00b781" },
    4: { text: "已撤销", color: "#00b781" }
}
const state = reactive({
    navBarTitle: "通知公告",
    activeTab: 0,
    myType: "",
    dataList: [],
    form: {
        approveStatus: "",
        startTime: "",
        endTime: ""
    }
})
const paging = ref(null)
const emitChangeSelect = (item) => {
    const { startTime, endTime } = item
    state.form.startTime = startTime
    state.form.endTime = endTime
    paging.value?.reload()
}
const changeSelect = () => {
    paging.value?.reload()
}
const initPage = (pageNo, pageSize) => {
    const params = {
        ...state.form,
        pageNo,
        pageSize
    }
    http.post("/app/mobile/mess/publish/page/all", params).then(({ data }) => {
        paging.value.complete(data.list)
    })
}
// 去审批
const handlerApprove = (item) => {
    const { id, identifier, approveStatus, contentType, messType, receiveUsers } = item
    navigateTo({
        url: `/apps/notice/${identifier}/details`,
        query: { id, approveStatus, contentType, messType, receiveUsers, status: approveStatus === 1 }
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

onShow(() => {
    paging.value?.reload()
})
</script>

<style lang="scss" scoped>
$color6: #666666;

.to-examine {
    background: $uni-bg-color-grey;
    min-height: 100vh;

    .examine_list {
        padding: 10rpx;

        .examine-item {
            background: $uni-bg-color;
            padding: 20rpx;
            border-radius: 10rpx;
            margin: 10rpx;

            .title {
                font-size: 28rpx;
                font-weight: 600;
            }

            .cloth-in,
            .type {
                font-size: 24rpx;
                margin: 20rpx 0;
                color: $color6;
            }

            .cloth-in {
                display: flex;
                justify-content: space-between;
            }
        }
    }
}
</style>

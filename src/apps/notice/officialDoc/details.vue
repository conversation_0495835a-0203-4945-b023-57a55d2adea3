<!-- 公文传阅 -->
<template>
    <view class="container">
        <NavBar :title="state.details.title" :id="state.details.id" />

        <view class="handler">
            <view class="handler-text">
                {{ state.details.identityUserName }}发布于
                {{ state.details.timerDate }}
            </view>
            <view class="handler-text" v-if="state.propsForm.type === 'publish'"> <uni-icons type="eye" size="14"></uni-icons> 浏览 {{ state.details.viewUsers }} </view>
        </view>
        <view class="constnt" :class="{ acitve: state.propsForm.type === 'publish' }">
            <view class="constnt-box">
                <view class="content-html" v-if="state.details.contentImg">
                    <image class="received-left" fit="cover" :src="state.details.contentImg" @click="onConfirm(cellItem)" />
                </view>
                <view class="content-html" v-if="state.urls.length"> </view>
                <view class="content-html" v-else>
                    <uv-parse :content="state.details?.content"></uv-parse>
                </view>
            </view>
            <view class="files" v-for="(item, index) in state.details.attachments" :key="index">
                <view class="files-image">
                    <image style="width: 25px; height: 25px" src="@nginx/workbench/notice/filew.png"></image>
                    <text class="files-text">{{ item.name }}</text>
                </view>
                <view class="files-btn" @click="previewFileByUrl(item.attachment)">查看</view>
            </view>
            <uni-list v-if="state.propsForm.type === 'publish'">
                <uni-list-item title="传阅人员" showArrow :rightText="state.details.notifyUsersInfo" link @click="handerNotifyScopes" />
            </uni-list>
        </view>
        <CreateFooter>
            <template #footer>
                <view v-if="!state.propsForm.status">
                    <view class="footer" v-if="state.propsForm.type === 'publish'">
                        <button class="isRetract-btn" :class="{ active: state.details.isRetract }" type="primary" plain :disabled="state.details.isRetract" @click="handerWithdraw">
                            {{ state.details.isRetract ? "已撤回" : "撤回" }}
                        </button>
                        <button class="istop-btn" :class="{ active: state.details.isRetract }" type="primary" @click="handerIsTop" :disabled="state.details.isRetract">
                            {{ state.details.isTop ? "取消置顶" : "置顶" }}
                        </button>
                    </view>
                </view>
            </template>
        </CreateFooter>
    </view>
</template>

<script setup>
import { previewFileByUrl } from "@/utils/index"
import NavBar from "../components/navBar.vue"
import CreateFooter from "../components/createFooter.vue"

const showPopover = shallowRef(false)
const state = reactive({
    urls: [],
    propsForm: {
        id: "",
        type: "",
        receiveUsers: 0
    },
    details: {
        id: "",
        identityUserName: "",
        timerDate: "",
        viewUsers: 0,
        contentImg: "",
        content: "",
        notifyUsersInfo: "",
        viewUsers: 0,
        status: 0,
        isDominateScreen: false,
        datetimerange: []
    }
})
const isDisabled = shallowRef(false)
// 通知公告详情
const getDetails = () => {
    const { id, type, contentType, messType } = state.propsForm
    const params = { id }
    // 收到
    let url = "/app/mobile/mess/receive/info"
    // 发布
    if (type === "publish") {
        url = "/app/mobile/mess/publish/getInfo"
        params.contentType = contentType
        params.messType = messType
    }
    http.post(url, params).then(({ data }) => {
        state.details = data
        // 如果是我收到的，且需要确认，按钮可点击
        isDisabled.value = type === "receive" && !data.isNeedConfirm
        state.details.datetimerange = [data.dominateStartTime, data.dominateEndTime]
    })
}
// 撤销
const handerWithdraw = () => {
    uni.showModal({
        title: "",
        content: "您确定要撤回该条消息吗？",
        confirmColor: "#00b781",
        success(res) {
            if (res.confirm) {
                const { id } = state.details
                http.post("/cloud/mobile/mess/publish/updateRetract", { id }).then(({ message }) => {
                    uni.showToast({ title: message, icon: "none" })
                    getDetails()
                })
            }
        }
    })
}

// 置顶切换
const handerIsTop = () => {
    const { id } = state.details
    http.post("/cloud/mobile/mess/publish/updateTop", { id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}
// 通知范围
const handerNotifyScopes = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/notificationScope",
        query: {
            id,
            title: "传阅人员"
        }
    })
}

// 确认
const getReceivedConfirm = () => {
    const { receiveId } = state.details
    http.post("/cloud/mobile/mess/receive/updateIncrementconfirms", { receiveId }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}

onLoad((options) => {
    state.propsForm = options
    if (options.status) {
        // status 是从审批列表过来的
        state.propsForm.type = "publish"
    }
    getDetails()
})
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    background: $uni-bg-color-grey;
    // padding: 0 30rpx;

    .handler {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .handler-text {
            font-size: 24rpx;
            color: #666666;
            padding: 10rpx 0;
        }
    }

    .constnt {
        overflow: hidden auto;

        &.acitve {
            height: calc(100vh - 280rpx);
        }

        .constnt-box {
            background-color: #dcf5ee80;
            margin: 20rpx 0 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            padding: 20rpx;

            .content-html {
                font-size: 26rpx;
                margin-bottom: 30rpx;
                line-height: 40rpx;
                padding: 4rpx 0;
                word-wrap: break-word;

                .received-left {
                    width: 100% !important;
                    height: 1228rpx;
                }
            }
        }

        .files {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: $uni-text-color-inverse;
            margin: 20rpx 0;
            padding: 20rpx;

            .files-image {
                flex: 1;
                display: flex;
                // justify-content: space-between;
                align-items: center;

                .files-text {
                    font-size: 26rpx;
                    // 一行超出隐藏显示
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                }
            }

            .files-btn {
                width: 60rpx;
                text-align: center;
                font-size: 26rpx;
                color: $uni-color-primary;
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $uni-text-color-inverse;

        uni-button {
            width: 45%;
            font-size: 30rpx;

            &.isRetract-btn {
                border-color: $uni-color-primary;
                color: $uni-color-primary;

                &.active {
                    border-color: #d5cdcd;
                    color: #d5cdcd;
                }
            }

            &.istop-btn {
                background-color: $uni-color-primary;

                &.active {
                    background: $uni-text-color-grey;
                    color: #d5cdcd;
                }
            }
        }
    }
}
</style>

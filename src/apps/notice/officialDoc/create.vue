<template>
    <view class="container">
        <NavBar title="公文传阅" />
        <IsTemplage />
        <view class="create">
            <input class="uni-input" v-model.trim="state.form.title" maxlength="50" placeholder="请输入标题（0/50）" />
            <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <image :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg"></image>
            </view>
            <Editor v-else v-model:valueHtml="state.form.content" />
            <uni-list-item title="附件">
                <template v-slot:footer>
                    <uni-file-picker v-show="state.form.attachments.length < 3" class="file-picker files-pickers" ref="files" :limit="3" file-mediatype="all" :auto-upload="false" @select="fileSelect" @delete="fileDelete" v-model="state.form.attachments">
                        <image style="width: 20px; height: 20px" src="@nginx/workbench/notice/fileIcon.png"></image>
                    </uni-file-picker>
                </template>
            </uni-list-item>

            <uni-list-item v-for="(item, index) in state.form.attachments" :key="index" :title="item.name">
                <template v-slot:footer>
                    <uni-icons type="trash" size="20" style="color: red" @click="fileDelete(item)"></uni-icons>
                </template>
            </uni-list-item>
            <uni-list-item title="传阅人" showArrow :rightText="notifyUsers" link @click="handerNotifyScopes" />

            <view class="uni-list-cell">
                <view class="uni-list-cell-db">是否置顶</view>
                <switch color="#00b781" @change="switchTopChange" :checked="state.form.isTop" style="transform: scale(0.8)" />
            </view>
            <uni-list-item title="定时发布">
                <template v-slot:footer>
                    <uni-datetime-picker class="file-picker datetime-picker" :border="false" type="datetime" v-model="state.form.timerDate" />
                </template>
            </uni-list-item>
        </view>
        <CreateFooter :forms="state.form" />
        <yd-selector ref="selectorRef" @confirm="confirmFn" />

        <!-- <yd-tinymce /> -->
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import IsTemplage from "../components/isTemplage.vue"
import CreateFooter from "../components/createFooter.vue"
import Editor from "../components/editor.vue"

const selectorRef = ref(null)
const state = reactive({
    form: {
        title: "",
        content: "",
        identifier: "officialDoc",
        contentType: 0,
        attachments: [],
        notifyUsersInfo: "",
        officicalDocTeachers: [],
        isTop: false, // 是否置顶
        timerDate: "", // 定时发布
        officicalDocElterns: []
    }
})

const notifyUsers = computed(() => {
    if (state.form.officicalDocTeachers.length || state.form.officicalDocElterns.length) {
        return `教职工${state.form.officicalDocTeachers.length}人、家长${state.form.officicalDocElterns.length}人`
    }
    return ""
})

// 是否置顶
const switchTopChange = (e) => {
    state.form.isTop = e.detail.value
}

// 通知范围
const handerNotifyScopes = () => {
    const typeList = [
        {
            type: "people_dept",
            name: "老师",
            selectLevel: "people_dept"
        },
        {
            type: "parent",
            name: "家长",
            selectLevel: "parent"
        }
    ]
    selectorRef.value.open(typeList, true)
}
// 上传附件
const fileSelect = (file) => {
    if (file.tempFiles && file.tempFiles.length) {
        file.tempFiles.forEach((i) => {
            const { path, name, uuid, size } = i
            http.uploadFile("/file/common/upload", path, { folderType: "app" }).then((url) => {
                const params = {
                    attachment: url,
                    name,
                    uuid: uuid + size
                }
                state.form.attachments.push(params)
            })
        })
    }
}

watch(
    () => state.form.timerDate,
    (val) => {
        state.form.isTimer = !!val.length
    }
)

// 删除附件
const fileDelete = (file) => {
    const uuid = file.uuid || file.tempFile.uuid
    state.form.attachments = state.form.attachments.filter((item) => item.uuid !== uuid)
}

function confirmFn(ids, selected) {
    state.form.notifyUsersInfo = selected.map((i) => i.name)?.join("、")
    state.form.officicalDocElterns = [] // 班级
    state.form.officicalDocTeachers = [] // 部门
    selected.forEach((item) => {
        if (item.typeValue === "people_dept") {
            state.form.officicalDocTeachers.push({
                id: item.id,
                userId: item.id,
                name: item.name,
                pid: item.pid,
                deptVOList: [
                    {
                        deptId: item.pid || "",
                        name: item.pName || ""
                    }
                ]
            })
        } else {
            const obj = { ...item, student: [] }
            delete obj["student"]
            item.student.elterns = [obj]
            state.form.officicalDocElterns.push(item.student)
        }
    })
    console.log(ids, selected, state.form, "选择的id和id对象")
}

onLoad((options) => {
    const { title = "", coverImg = "", contentImg = "", id = "" } = options
    // 这是通过模版进入的
    state.form.details = []
    if (coverImg || contentImg) {
        state.form.contentType = 1
        state.form.title = title
        state.form.details = [
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 1334,
                parseJson: "",
                width: 750
            },
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 576,
                parseJson: "",
                width: 1024
            }
        ]
    }
})
</script>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 130rpx);
    background-color: #f9faf9;

    .create {
        margin: 10rpx 0;
        padding-bottom: 174rpx;

        .uni-list-cell,
        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
            color: #3b4144;
        }

        .uni-list-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1rpx solid #eee;
            padding-left: 15rpx;

            .uni-list-cell-db {
                text-indent: 14rpx;
                font-size: 28rpx;
            }
        }

        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 1rpx 0;
            text-indent: 20rpx;
        }

        // 附件
        .file-picker {
            text-align: right;

            :deep(.uni-icons) {
                display: none;
            }
        }

        .files-pickers {
            :deep(.uni-file-picker__lists) {
                display: none;
            }
        }
    }

    :deep(.uni-list-item__extra) {
        width: 251rpx;
    }
}
</style>

<template>
    <view class="container">
        <NavBar title="文章鉴赏" />
        <IsTemplage />
        <view class="create">
            <input class="uni-input" v-model.trim="state.form.title" maxlength="50" placeholder="请输入标题（0/50）" />
            <input class="uni-input" v-model.trim="state.form.author" maxlength="50" placeholder="请填写作者（0/50）" />
            <input class="uni-input" v-model.trim="state.form.source" maxlength="50" placeholder="请填写出处（0/50）" />
            <view v-if="state.form.details.length" :style="{ height: state.form.details[0].height + 'rpx', padding: '0 20rpx' }">
                <image :style="{ width: '100%', height: '100%' }" :src="state.form.details[0].contentImg"></image>
            </view>
            <Editor v-else v-model:valueHtml="state.form.content" />
            <uni-list-item title="附件">
                <template v-slot:footer>
                    <uni-file-picker v-show="state.form.attachments.length < 3" class="file-picker files-pickers" ref="files" :limit="3" file-mediatype="all" :auto-upload="false" @select="fileSelect" @delete="fileDelete" v-model="state.form.attachments">
                        <image style="width: 20px; height: 20px" src="@nginx/workbench/notice/fileIcon.png"></image>
                    </uni-file-picker>
                </template>
            </uni-list-item>

            <uni-list-item v-for="(item, index) in state.form.attachments" :key="index" :title="item.name">
                <template v-slot:header>
                    <slot :item="item">
                        <text> <image style="width: 20px; height: 20px" src="@nginx/workbench/notice/filew.png"></image> </text>
                    </slot>
                </template>
                <template v-slot:footer>
                    <uni-icons type="trash" size="20" style="color: red" @click="fileDelete(item)"></uni-icons>
                </template>
            </uni-list-item>

            <uni-list-item title="接收人员" showArrow :rightText="state.form.notifyUsersInfo" link @click="handerNotifyScopes" />
            <uni-list-item title="定时发布">
                <template v-slot:footer>
                    <uni-datetime-picker class="file-picker datetime-picker" :border="false" type="datetime" v-model="state.form.timerDate" />
                </template>
            </uni-list-item>

            <uni-list-item title="接收设备" showArrow :rightText="state.form.receiver" link @click="onClickDevice" />

            <uni-list-item title="选择班牌" v-if="state.form.receiver" showArrow :rightText="state.form.notifyDevicesInfo" link @click="handerBanPai" :ellipsis="1">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.form.notifyDevicesInfo }}
                    </text>
                </template>
            </uni-list-item>

            <uni-list-item title="选择借还机" showArrow :rightText="state.deviceInfo" link @click="handerJeHaiji" :ellipsis="1">
                <template v-slot:footer>
                    <text class="item_right_text ellipsis">
                        {{ state.deviceInfo }}
                    </text>
                </template>
            </uni-list-item>

            <view class="uni-list-cell" v-if="state.form.notifyDevicesInfo">
                <view class="uni-list-cell-db">班牌霸屏显示</view>
                <switch color="#00b781" @change="switchScreenChange" :checked="state.form.isDominateScreen" style="transform: scale(0.8)" />
            </view>

            <uni-datetime-picker v-if="state.form.isDominateScreen" v-model="state.datetimerange" type="datetimerange" rangeSeparator="至" @change="changeTime" />
        </view>
        <CreateFooter :forms="state.form" />
        <!-- <yd-tinymce /> -->
    </view>
</template>

<script setup>
import NavBar from "../components/navBar.vue"
import IsTemplage from "../components/isTemplage.vue"
import CreateFooter from "../components/createFooter.vue"
import Editor from "../components/editor.vue"

const state = reactive({
    selectedRecipient: [],
    form: {
        title: "",
        content: "",
        source: "",
        author: "",
        identifier: "article", //文章鉴赏
        contentType: 0,
        depts: [], // 部门id
        notifyUsersInfo: "", // 通知范围name
        isDominateScreen: false, // 是否霸屏
        receiver: "",
        notifyDevicesInfo: "", // 班牌name
        brandIds: [], // 班牌id
        dominateEndTime: "", // 结束时间
        dominateStartTime: "", // 开始时间
        deviceList: [], // 设备id
        attachments: [], // 附件
        deviceTypes: [], // 设备类型
        timerDate: "" // 定时发布
    },
    // 霸屏时间
    datetimerange: [],
    propsForm: { id: "", contentType: "", messType: "" }
})

// 接收设备
const onClickDevice = () => {
    navigateTo({
        url: "/apps/notice/components/optionDevices",
        events: {
            selectMember: (data) => {
                state.form.receiver = data.name
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}
// 递归函数 获取最后一级后返回
const getLast = (arr, last) => {
    arr.forEach((item) => {
        if (item.children.length) {
            getLast(item.children, last)
        } else {
            last.push(item)
        }
    })
}
// 选择班牌
const handerBanPai = () => {
    navigateTo({
        url: "/apps/notice/components/selectMember/banPai",
        query: {
            treeType: 2
        },
        events: {
            selectMember: (data) => {
                state.form.notifyDevicesInfo = data.treeSubmitListName
                let brandIds = []
                getLast(data.treeSubmitList, brandIds)
                state.form.brandIds = brandIds.map((item) => item.id)
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}
// 选择借还机
const handerJeHaiji = () => {
    navigateTo({
        url: "/apps/notice/components/selectMember/banPai",
        query: {
            treeType: 6
        },
        events: {
            selectMember: (data) => {
                state.deviceInfo = data.treeSubmitListName
                let deviceList = []
                getLast(data.treeSubmitList, deviceList)
                state.form.deviceList = deviceList.map((item) => {
                    return {
                        brandId: item.id,
                        deviceType: 6
                    }
                })
            }
        },
        success: (res) => {
            res.eventChannel.emit("feedbackSelected", { selectedData: [] })
        }
    })
}
// 通知范围
const handerNotifyScopes = () => {
    navigateTo({
        url: "/apps/notice/components/receivingpersonnel",
        query: { selectedRecipient: state.selectedRecipient },
        events: {
            selectMember: (data) => {
                state.form.notifyUsersInfo = ""
                state.selectedRecipient = []
                if (data.length) {
                    const names = []
                    data.forEach((v) => {
                        if (v.key == "1") {
                            state.form.isCheckedEltern = v.checked
                        }
                        if (v.key == "2") {
                            state.form.isCheckedEmployee = v.checked
                        }
                        if (v.checked) {
                            names.push(v.name)
                            state.selectedRecipient.push(v.key)
                        }
                    })
                    state.form.notifyUsersInfo = names.join("、")
                }
            }
        }
    })
}

// 上传附件
const fileSelect = (file) => {
    if (file.tempFiles && file.tempFiles.length) {
        file.tempFiles.forEach((i) => {
            const { path, name, uuid, size } = i
            http.uploadFile("/file/common/upload", path, { folderType: "app" }).then((url) => {
                const params = {
                    attachment: url,
                    name,
                    uuid: uuid + size
                }
                state.form.attachments.push(params)
            })
        })
    }
}
// 删除附件
const fileDelete = (file) => {
    const uuid = file.uuid || file.tempFile.uuid
    state.form.attachments = state.form.attachments.filter((item) => item.uuid !== uuid)
}

watch(
    () => state.form.timerDate,
    (val) => {
        state.form.isTimer = !!val.length
    }
)

const getDetails = () => {
    http.post("/app/mobile/mess/publish/getInfo", state.propsForm).then(({ data }) => {
        const { coverImg, dominateStartTime, dominateEndTime, notifyDevicesInfo, notifyUsersInfo, deviceList } = data
        state.form = data
        state.form.coverImg = coverImg
        state.form.receiver = notifyDevicesInfo ? "班牌" : ""
        state.form.notifyDevicesInfo = notifyDevicesInfo
        state.form.deviceList = deviceList.map((v) => v.brandId)
        state.datetimerange = [dominateStartTime, dominateEndTime]
        state.form.notifyUsersInfo = notifyUsersInfo
    })
}
// 是否霸屏
const switchScreenChange = (e) => {
    state.form.isDominateScreen = e.detail.value
}

// 选择班牌时间
const changeTime = (e) => {
    state.form.dominateStartTime = e[0] || ""
    state.form.dominateEndTime = e[1] || ""
}
onLoad((options) => {
    state.propsForm = options
    const { title = "", coverImg = "", contentImg = "", id = "" } = options
    id && getDetails()
    // 这是通过模版进入的
    state.form.details = []
    if (coverImg || contentImg) {
        state.form.contentType = 1
        state.form.title = title
        state.form.details = [
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 1334,
                parseJson: "",
                width: 750
            },
            {
                contentImg: coverImg || contentImg,
                coverImg: coverImg || contentImg,
                height: 576,
                parseJson: "",
                width: 1024
            }
        ]
    }
})
</script>

<style lang="scss" scoped>
.container {
    height: calc(100vh - 130rpx);
    background-color: #f9faf9;
    .create {
        margin: 10rpx 0;
        padding-bottom: 174rpx;
        .uni-list-cell,
        .uni-input {
            padding: 15rpx 0;
            background-color: $uni-text-color-inverse;
            margin: 2rpx 0;
            text-indent: 20rpx;
            color: #3b4144;
        }

        .uni-list-cell {
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-top: 1rpx solid #eee;
            padding-left: 15rpx;

            .uni-list-cell-db {
                text-indent: 14rpx;
                font-size: 28rpx;
            }
        }
        // 附件
        .file-picker {
            text-align: right;

            :deep(.uni-icons) {
                display: none;
            }
        }

        .datetime-picker {
            :deep(.uni-date__x-input) {
                height: 48rpx;
                line-height: 48rpx;
            }
        }

        .files-pickers {
            :deep(.uni-file-picker__lists) {
                display: none;
            }
        }
    }
}
:deep(.uni-list-item__content) {
    flex: none;
}
:deep(.uni-list-item__extra) {
    flex: 1;
}
.item_right_text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    text-align: right;
    flex: 1;
}
</style>

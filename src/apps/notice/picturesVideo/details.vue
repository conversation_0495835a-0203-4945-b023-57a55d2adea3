<template>
    <view class="container">
        <NavBar :title="state.details.title" :id="state.details.id" />
        <view class="handler">
            <view class="handler-text">
                {{ state.details.identityUserName }}发布于
                {{ state.details.timerDate }}
            </view>
            <view class="handler-text" v-if="state.propsForm.type === 'publish'"> </view>
        </view>
        <view class="constnt" :class="{ acitve: state.propsForm.type === 'publish' }">
            <view class="constnt-box">
                <uni-swiper-dot class="uni-swiper-dot-box" :info="state.details.aioMachineContent.urls" :mode="'indexes'" field="content" :current="state.swiperDotIndex" @clickItem="clickItem">
                    <swiper class="swiper-box" :current="state.swiperDotIndex" @change="clickItemChange" v-if="state.details.aioMachineContent.urls.length">
                        <swiper-item v-for="(src, index) in state.details.aioMachineContent.urls" :key="index">
                            <view class="video-content" v-if="isVideoImag(src)">
                                <video class="reset-image" :src="src" />
                                <!-- <video webkit-playsinline="true" playsinline="true" x-webkit-airplay="allow" x5-video-player-type="h5" x5-video-player-fullscreen="true" x5-video-orientation="portraint" ref="root" class="nut-video-player" :muted="options.muted" :autoplay="options.autoplay" :loop="options.loop" :poster="options.poster" :controls="options.controls" :preload="options.preload">
                                    <source :src="src" type="video/mp4" />
                                </video> -->
                            </view>
                            <image v-else class="reset-image" :src="src" />
                            <!-- </view> -->
                        </swiper-item>
                    </swiper>
                </uni-swiper-dot>
            </view>
            <uni-list v-if="state.propsForm.type === 'publish'">
                <uni-list-item title="显示设置" showArrow :rightText="showSetList[state.details.aioMachineContent.showType]" />
                <uni-list-item title="图片轮播时间" showArrow :rightText="`${state.details.aioMachineContent.showSecond ? state.details.aioMachineContent.showSecond + '秒' : ''} `" />

                <uni-list-item title="发布方式" showArrow :rightText="releaseMode[state.details.aioMachineContent.mode]" />
                <uni-list-item title="选择画屏" showArrow :rightText="state.details.notifyDevicesInfo" />
                <uni-list-item title="选择一体机" showArrow :rightText="state.details.notifyDevicesInfo" />
            </uni-list>
        </view>
        <CreateFooter>
            <template #footer>
                <view v-if="!state.propsForm.status">
                    <view class="footer" v-if="state.propsForm.type === 'publish'">
                        <button class="isRetract-btn" :class="{ active: state.details.isRetract }" type="primary" plain @click="handerWithdraw">
                            {{ state.details.isRetract ? "已撤回" : "撤回" }}
                        </button>
                        <button plain class="istop-btn" @click="copyAnnouncement">复制</button>
                    </view>
                </view>
            </template>
        </CreateFooter>
    </view>
</template>

<script setup>
import CreateFooter from "../components/createFooter.vue"
import NavBar from "../components/navBar.vue"

const showSetList = { 1: "适应边框", 2: "占满屏幕（拉伸比例）", 3: "占满屏幕（裁剪）" }

const releaseMode = { 1: "插入发布", 2: "覆盖发布", 3: "临时发布" }
const options = {
    autoplay: false, //是否自动播放
    volume: 0.5,
    poster: "",
    loop: false,
    controls: true,
    muted: false, //是否静音
    disabled: false, //禁止操作
    playsinline: false, //行内展示
    touchPlay: false,
    preload: ""
}
// const showPopover = shallowRef(false);
const state = reactive({
    propsForm: {
        id: "",
        contentType: 0,
        messType: 8
    },
    details: {
        id: "",
        title: "",
        aioMachineContent: {
            showType: null,
            showSecond: null,
            mode: null
        },
        notifyDevicesInfo: ""
    }
})
const isVideoImag = computed(() => {
    return (url) => {
        const videos = [".mp4", ".webm", ".ogg", ".mpeg", ".plain", ".flv", ".x-ms-wmv", ".avi"]
        return videos.some((v) => url.lastIndexOf(v) != -1)
    }
})
const clickItem = (index) => {
    state.swiperDotIndex = index
}
const clickItemChange = (e) => {
    state.swiperDotIndex = e.detail.current
}
// 通知公告详情
const getDetails = () => {
    const { id, type, contentType, messType } = state.propsForm
    const params = { id }
    // 收到
    let url = "/app/mobile/mess/receive/info"
    // 发布
    if (type === "publish") {
        url = "/app/mobile/mess/publish/getInfo"
        params.contentType = contentType
        params.messType = messType
    }
    http.post(url, params).then(({ data }) => {
        state.details = data
    })
}
// 撤销
const handerWithdraw = () => {
    uni.showModal({
        title: "",
        content: "您确定要撤回该条消息吗？",
        confirmColor: "#00b781",

        success(res) {
            if (res.confirm) {
                const { id } = state.details
                http.post("/cloud/mobile/mess/publish/updateRetract", { id }).then(({ message }) => {
                    uni.showToast({ title: message, icon: "none" })
                    getDetails()
                })
            }
        }
    })
}

// 复制
const copyAnnouncement = () => {
    const { id, contentType, messType } = state.details
    navigateTo({
        url: `/apps/notice/picturesVideo/create`,
        query: { id, contentType, messType }
    })
}
// 通知范围
const handerNotifyScopes = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/notificationScope",
        query: {
            id
        }
    })
}
// 班牌
const onClickDevice = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/deviceList",
        query: {
            id
        }
    })
}

onLoad((options) => {
    state.propsForm = options
    if (options.status) {
        // status 是从审批列表过来的
        state.propsForm.type = "publish"
    }
    getDetails()
})
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background: $uni-bg-color-grey;

    .handler {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .handler-text {
            font-size: 24rpx;
            color: #666666;
            padding: 10rpx 0;

            .baping {
                color: $uni-color-primary;
            }
        }
    }

    .constnt {
        overflow: hidden auto;

        &.acitve {
            height: calc(100vh - 280rpx);
        }

        .constnt-box {
            background-color: #dcf5ee80;
            margin: 20rpx 0 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            padding: 20rpx;

            .content-html {
                font-size: 26rpx;
                margin-bottom: 30rpx;
                line-height: 40rpx;
                padding: 4rpx 0;

                :deep(img) {
                    width: 100% !important;
                    height: auto;
                }
            }

            .swiper-box {
                .nut-video-player,
                .video-content {
                    width: 100%;
                    height: 100%;
                }

                .reset-image {
                    width: 100%;
                    height: 100%;
                }

                // overflow: hidden;
            }
        }
    }

    .video-content {
        width: 100%;
        margin-right: 10px;
        display: flex;
        position: relative;
        margin-bottom: 20px;

        .campusVideo {
            width: 100%;
            height: auto;
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $uni-text-color-inverse;

        uni-button {
            width: 45%;
            font-size: 30rpx;

            &.isRetract-btn {
                border-color: $uni-color-primary;
                color: $uni-color-primary;

                &.active {
                    border-color: #d5cdcd;
                    color: #d5cdcd;
                }
            }

            &.istop-btn {
                background-color: $uni-color-primary;
                color: $uni-bg-color;
                border-color: $uni-bg-color;

                &.active {
                    background: $uni-text-color-grey;
                    color: #d5cdcd;
                }
            }
        }
    }
}
</style>

<!-- 图片/视频 -->
<template>
    <view class="news">
        <view class="news-item" v-for="(item, index) in dataList" :key="index" @click="onConfirm(item)">
            <!-- 我发布的 -->
            <view class="news-item-content">
                <template v-if="item.aioMachineContent?.urls.length">
                    <div class="video-content" v-if="isVideoImag(item.aioMachineContent.urls)">
                        <!-- <image class="img" src="@nginx/workbench/notice/play.png" alt /> -->
                        <video class="campusVideo" ref="campusVideo" :src="item.aioMachineContent.urls[0]"></video>
                    </div>
                    <image v-else class="img" fit="cover" :src="item.aioMachineContent.urls[0]" />
                </template>
                <view class="cell-item" :class="{ active: item.aioMachineContent?.urls.length }">
                    <view class="cell-item-hander">
                        {{ item.title }}
                    </view>
                    <view class="cell-item-content">
                        <view class="list-source">
                            {{ item.identityUserName }}发布于<text>
                                {{ item.timerDate }}
                            </text>
                        </view>
                    </view>
                    <view class="cell-item-footer" v-if="paramsForm.type == 'publish'">
                        <i />

                        <uni-list-item v-if="item.isRetract" :class="{ resetListItem: item.isRetract }" rightText="已撤回" />
                        <uni-list-item v-else :class="{ resetConfimItemActive: !item.status }" :rightText="item.status ? '已发布' : '待发布'" />
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["complete", "reload"])
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    dataList: {
        type: Array,
        default: () => []
    }
})

const dataList = computed(() => props.dataList)

const isVideoImag = computed(() => {
    return (url) => {
        const videos = [".mp4", ".webm", ".ogg", ".mpeg", ".plain", ".flv", ".x-ms-wmv", ".avi"]
        return videos.some((v) => url[0].lastIndexOf(v) != -1)
    }
})
const initPage = (pageNo, pageSize, isAll) => {
    const { type, startTime, endTime } = props.paramsForm
    const params = {
        identifier: "aio_machine",
        keyWords: "",
        startTime,
        endTime,
        pageNo,
        pageSize
    }
    // 我收到的
    let url = "/cloud/mobile/mess/receive/page"
    // 我发布的
    if (type == "publish") {
        url = "/cloud/mobile/mess/publish/page"
    }
    // 所有我发布的
    if (isAll) {
        url = "/app/mobile/mess/publish/page/all"
    }
    http.post(url, params).then(({ data }) => {
        emit("complete", data.list)
    })
}
// 去确认、详情
const onConfirm = (item) => {
    if (!item.isView) {
        emit("reload")
    }
    navigateTo({
        url: "/apps/notice/picturesVideo/details",
        query: {
            id: item.id,
            type: props.paramsForm.type,
            receiveUsers: item.receiveUsers,
            contentType: item.contentType,
            messType: item.messType
        }
    })
}

watch(
    () => props.paramsForm,
    () => {
        emit("reload")
    },
    {
        deep: true
    }
)
onShow(() => {
    emit("reload")
})
onLoad(() => {
    emit("reload")
})

defineExpose({ initPage })
</script>

<style lang="scss" scoped>
$color6: #666666;
$warning: #faad14;
$red: red;

.news {
    padding: 0rpx 30rpx 30rpx 30rpx;

    .news-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 20rpx 0;
        padding: 30rpx;

        .news-item-content {
            display: flex;
            align-items: center;

            .campusVideo,
            .img {
                width: 240rpx;
                height: 180rpx;
                border-radius: 10rpx;
                margin-right: 20rpx;
            }

            .cell-item {
                flex: 1;

                &.active {
                    height: 190rpx;
                }

                .cell-item-hander {
                    font-size: 30rpx;
                    font-weight: 600;
                    // 两行超出隐藏
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    line-height: 1.5;
                }
            }
        }

        .cell-item-content {
            font-size: 24rpx;
            margin: 20rpx 0;
            color: $color6;

            .list-source {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;

                &:not(:last-child) {
                    margin: 10rpx 0;
                }
            }

            .active {
                color: $warning;
            }
        }

        .cell-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            :deep(.uni-list-item) {
                .uni-list--border {
                    display: none;
                }

                &.resetConfimItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $warning;
                    }
                }

                &.resetListItem {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $uni-text-color-grey !important;
                    }
                }

                .uni-list-item__extra-text span,
                .uni-icon-wrapper {
                    padding: 0;
                    font-size: 24rpx;
                    color: $uni-color-primary;
                }

                .uni-list-item__container {
                    padding: 0;
                }
            }
        }
    }
}
</style>

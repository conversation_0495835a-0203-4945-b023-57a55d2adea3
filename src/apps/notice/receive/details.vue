<template>
    <view class="container">
        <NavBar :title="state.details.title" :id="state.details.id" />
        <view class="handler">
            <view class="handler-text">
                {{ state.details.identityUserName }}发布于
                {{ state.details.timerDate }}
            </view>
            <view class="handler-text" v-if="state.propsForm.type === 'publish'"> <uni-icons type="eye" size="14"></uni-icons> 浏览 {{ state.details.viewUsers }} </view>
        </view>
        <view class="constnt" :class="{ acitve: state.propsForm.type === 'publish' }">
            <view class="constnt-contact" v-for="item in contactlist" :key="item.key">
                <view class="constnt-title">
                    {{ item.title }}
                </view>
                <view class="constnt-name">
                    {{ state.details[item.key] }}
                </view>
            </view>
            <view class="constnt-box">
                <view class="content-html" v-if="state.details.contentImg">
                    <image class="reset-image" :class="{ active: state.details.contentImg }" fit="cover" :src="state.details.contentImg" @click="onConfirm(cellItem)" />
                </view>
                <view class="content-html" v-if="state.urls.length">
                    <template v-for="it in state.urls">
                        <image class="reset-image" :src="it" />
                    </template>
                </view>
                <view class="content-html" v-else v-html="state.details.content"></view>
            </view>
            <uni-list v-if="state.propsForm.type === 'publish'">
                <uni-list-item title="接收人员" :rightText="state.details.notifyUsersInfo" />
                <!--  @click="handerNotifyScopes"  -->
                <uni-list-item title="接收设备" :rightText="'班牌'" />
                <uni-list-item title="选择设备" :rightText="state.details.notifyDevicesInfo || '无'" @click="onClickDevice" link />
                <uni-list-item title="班牌霸屏显示" :rightText="state.details.isDominateScreen ? '开启' : '关闭'" />
                <uni-datetime-picker v-if="!state.details.id" disabled v-model="state.details.datetimerange" type="datetimerange" rangeSeparator="至" />
            </uni-list>
        </view>
        <CreateFooter>
            <template #footer>
                <view v-if="!state.propsForm.status">
                    <view class="footer" v-if="state.propsForm.type === 'publish'">
                        <!-- <button plain type="primary" :disabled="state.details.isRetract" @click="handerWithdraw">
                            {{ state.details.isRetract ? "已撤回" : "撤回" }}
                        </button> -->
                        <button plain type="primary" :disabled="state.details.isRetract" @click="editNew">编辑</button>
                    </view>
                </view>
            </template>
        </CreateFooter>
    </view>
</template>

<script setup>
import CreateFooter from "../components/createFooter.vue"
import NavBar from "../components/navBar.vue"

const contactlist = [
    { title: "招领物品", key: "lossItems" },
    { title: "丢失/拾取时间", key: "lossTime" },
    { title: "联系人", key: "contactPerson" },
    { title: "联系电话", key: "contactPhone" },
    { title: "丢失/拾取地点", key: "lossLocation" }
]
// const showPopover = shallowRef(false);
const state = reactive({
    urls: [],
    propsForm: {
        id: "",
        type: "",
        receiveUsers: 0
    },
    details: {
        id: "",
        identityUserName: "",
        timerDate: "",
        viewUsers: 0,
        contentImg: "",
        content: "",
        notifyUsersInfo: "",
        viewUsers: 0,
        status: 0,
        isDominateScreen: false,
        datetimerange: [],
        lossItems: ""
    }
})
// const isDisabled = shallowRef(false);
// 通知公告详情
const getDetails = () => {
    const { id, type, contentType, messType } = state.propsForm
    const params = { id }
    // 收到
    let url = "/cloud/mobile/mess/receive/info"
    // 发布
    if (type === "publish") {
        url = "/cloud/mobile/mess/publish/getInfo"
        params.contentType = contentType
        params.messType = messType
    }
    http.post(url, params).then(({ data }) => {
        state.details = data
        state.details.datetimerange = [data.dominateStartTime, data.dominateEndTime]
    })
}
// 撤销
const handerWithdraw = () => {
    const { id } = state.details
    http.post("/cloud/mobile/mess/publish/updateRetract", { id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}

// 通知范围
const handerNotifyScopes = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/notificationScope",
        query: {
            id
        }
    })
}
// 班牌
const onClickDevice = () => {
    navigateTo({
        url: "/apps/notice/components/deviceList",
        query: {
            id: state.details.id
        }
    })
}

// 确认
const getReceivedConfirm = () => {
    const { receiveId } = state.details
    http.post("/cloud/mobile/mess/receive/updateIncrementconfirms", { receiveId }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}

// 编辑新闻
const editNew = () => {
    const { id, contentType, messType } = state.details
    navigateTo({
        url: `/apps/notice/recruit/create`,
        query: { id, contentType, messType }
    })
}
onLoad((options) => {
    state.propsForm = options
    if (options.status) {
        // status 是从审批列表过来的
        state.propsForm.type = "publish"
    }
    getDetails()
})
</script>

<style lang="scss" scoped>
.container {
    height: 100vh;
    background: $uni-bg-color-grey;

    .bar_right {
        color: $uni-primary;
    }

    .handler {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .handler-text {
            font-size: 24rpx;
            color: #666666;
            padding: 10rpx 0;
        }
    }

    .constnt {
        overflow: hidden auto;
        background: $uni-bg-color;

        &.acitve {
            height: calc(100vh - 280rpx);
        }

        .constnt-contact {
            padding: 10rpx 30rpx;

            .constnt-title {
                font-size: 24rpx;
                color: #666666ff;
                margin-bottom: 8rpx;
            }

            .constnt-name {
                font-size: 28rpx;
                color: #333333ff;
            }
        }

        .constnt-box {
            background-color: #dcf5ee80;
            margin: 20rpx 0 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            padding: 20rpx;

            .content-html {
                font-size: 26rpx;
                margin-bottom: 30rpx;
                line-height: 40rpx;
                padding: 4rpx 0;

                :deep(img),
                .reset-image {
                    width: 100% !important;
                    height: auto;

                    &.active {
                        height: 1334rpx;
                    }
                }
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: $uni-text-color-inverse;

        uni-button {
            width: 100%;
            font-size: 30rpx;

            &[type="primary"] {
                background: $uni-color-primary;
                color: $uni-text-color-inverse;
                border-color: $uni-color-primary;
            }

            &[disabled][type="primary"] {
                background: #00b78185;
                border-color: #d5cdcd;
                color: #d5cdcd;
            }
        }

        .butn-primary {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            border-color: $uni-color-primary;
        }
    }
}
</style>

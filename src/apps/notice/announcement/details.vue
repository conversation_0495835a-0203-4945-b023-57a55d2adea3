<template>
    <view class="container">
        <NavBar :title="state.details.title" :id="state.details.id" />
        <view class="handler">
            <view class="handler-text">
                {{ state.details.identityUserName }}发布于
                {{ state.details.timerDate }}
            </view>
            <view class="handler-text" v-if="state.propsForm.type === 'publish'"> <uni-icons type="eye" size="14"></uni-icons> 浏览 {{ state.details.viewUsers }} </view>
        </view>
        <view class="constnt" :class="{ acitve: state.propsForm.type === 'publish' }">
            <view class="constnt-box">
                <view class="content-html" v-if="state.details.contentImg">
                    <image class="reset-image" :class="{ active: state.details.contentImg }" fit="cover" :src="state.details.contentImg" />
                </view>
                <view class="content-html" v-if="state.urls.length"> </view>
                <view v-else class="content-html">
                    <uv-parse :content="state.details?.content"></uv-parse>
                </view>
            </view>
            <view class="files" v-for="(item, index) in state.details.attachments" :key="index">
                <view class="files-image">
                    <image style="width: 25px; height: 25px" src="@nginx/workbench/notice/filew.png"></image>
                    <text class="files-text">{{ item.name }}</text>
                </view>
                <view class="files-btn" @click="previewFileByUrl(item.attachment)">查看</view>
            </view>
            <uni-list v-if="state.propsForm.type === 'publish'">
                <uni-list-item title="通知范围" showArrow :rightText="state.details.notifyUsersInfo" link @click="handerNotifyScopes" />
                <uni-list-item v-if="state.details.isNeedConfirm" title="确认人员" showArrow :rightText="`${state.details.confirmUsers}/${state.propsForm.receiveUsers}`" link @click="handerBrowsingPersonnel('isConfirm')" />
                <uni-list-item title="接收设备" :rightText="state.details.notifyDevicesInfo" />

                <uni-list-item title="选择设备" showArrow :rightText="state.details.notifyDevicesInfo" link @click="onClickDevice" />
                <uni-list-item title="班牌霸屏显示" :rightText="state.details.isDominateScreen ? '开启' : '关闭'" />
                <uni-datetime-picker v-if="!state.details.id" disabled v-model="state.details.datetimerange" type="datetimerange" rangeSeparator="至" />
            </uni-list>
        </view>
        <CreateFooter>
            <template #footer>
                <view class="footer" v-if="!state.propsForm.status">
                    <template v-if="state.propsForm.type === 'receive'">
                        <button v-if="!state.details.isConfirm && state.details.isNeedConfirm" style="width: 100%" :disabled="state.details.isConfirm" type="primary" @click="getReceivedConfirm">确认</button>
                    </template>

                    <template v-else>
                        <button class="butnDefaultActive" plain @click="copyAnnouncement">复制</button>
                        <button class="butn-default" :class="{ butnDefaultActive: !state.details.isRetract }" plain @click="handerWithdraw">
                            {{ state.details.isRetract ? "已撤回" : "撤回" }}
                        </button>
                        <button type="primary" @click="handerIsTop">
                            {{ state.details.isTop ? "取消置顶" : "置顶" }}
                        </button>
                    </template>
                </view>
            </template>
        </CreateFooter>

        <uni-popup ref="dominateScreenDialog" type="dialog">
            <uni-popup-dialog class="dialog_box" type="center" title="该消息已撤回" cancelText="取消" confirmText="确认" message="首页" content="是否需要撤回设备上的霸屏任务？" @confirm="dialogDominateScreenConfirm" @close="dominateScreenDialog.close()"> </uni-popup-dialog>
        </uni-popup>
    </view>
</template>

<script setup>
import { previewFileByUrl } from "@/utils/index"
import NavBar from "../components/navBar.vue"
import CreateFooter from "../components/createFooter.vue"
const dominateScreenDialog = ref(false)
const state = reactive({
    urls: [],
    propsForm: {
        id: "",
        type: "",
        receiveUsers: 0
    },
    details: {
        title: "通知公告详情",
        id: "",
        identityUserName: "",
        timerDate: "",
        viewUsers: 0,
        contentImg: "",
        content: "",
        notifyUsersInfo: "",
        viewUsers: 0,
        status: 0,
        isDominateScreen: false,
        datetimerange: []
    }
})

// 通知公告详情
const getDetails = () => {
    const { id, type, contentType, messType, status } = state.propsForm
    const params = { id }
    // 收到
    let url = "/app/mobile/mess/receive/info"
    // 发布
    if (type === "publish") {
        url = "/app/mobile/mess/publish/getInfo"
        params.contentType = contentType
        params.messType = messType
    }
    http.post(url, params).then(({ data }) => {
        state.details = data
        // 如果是我收到的，且需要确认，按钮可点击
        state.details.datetimerange = [data.dominateStartTime, data.dominateEndTime]
    })
}
// 复制公告
const copyAnnouncement = () => {
    const { title, content, contentImg } = state.details
    navigateTo({
        url: `/apps/notice/announcement/create`,
        query: { title, content, contentImg }
    })
}
//  弹窗确认
const handerWithdraw = () => {
    if (state.details.isRetract) return
    uni.showModal({
        title: "",
        content: "您确定要撤回该条消息吗？",
        confirmColor: "#00b781",
        success(res) {
            if (res.confirm) {
                const { id, isDominateScreen } = state.details
                http.post("/cloud/mobile/mess/publish/updateRetract", { id }).then(({ message }) => {
                    if (isDominateScreen) {
                        dominateScreenDialog.value.open("center")
                    } else {
                        uni.showToast({ title: message, icon: "none" })
                        getDetails()
                    }
                })
            }
        }
    })
}

// 撤回置顶
const dialogDominateScreenConfirm = () => {
    const { id } = state.details
    http.post("/app/mobile/mess/publish/updateStopDominateScreen", { ids: [id] }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}
// 置顶切换
const handerIsTop = () => {
    const { id } = state.details
    http.post("/cloud/mobile/mess/publish/updateTop", { id }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}
// 通知范围
const handerNotifyScopes = () => {
    const { id } = state.details
    navigateTo({
        url: "/apps/notice/components/notificationScope",
        query: {
            id
        }
    })
}
// 浏览人员
const handerBrowsingPersonnel = (confirmView) => {
    const { id, confirmUsers } = state.details
    navigateTo({
        url: "/apps/notice/components/browsingPersonnel",
        query: {
            id,
            confirmView,
            confirmUsers,
            receiveUsers: state.propsForm.receiveUsers
        }
    })
}
// 班牌
const onClickDevice = () => {
    navigateTo({
        url: "/apps/notice/components/deviceList",
        query: {
            id: state.details.id
        }
    })
}

// 确认
const getReceivedConfirm = () => {
    const { receiveId } = state.details
    http.post("/cloud/mobile/mess/receive/updateIncrementconfirms", { receiveId }).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        getDetails()
    })
}

onLoad((options) => {
    state.propsForm = options
    if (options.status) {
        // status 是从审批列表过来的
        state.propsForm.type = "publish"
    }
    getDetails()
})
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    padding-bottom: 116rpx;
    background: $uni-bg-color-grey;

    .handler {
        display: flex;
        justify-content: space-between;
        padding: 0 30rpx;

        .handler-text {
            font-size: 24rpx;
            color: #666666;
            padding: 10rpx 0;
        }
    }

    .constnt {
        overflow: hidden auto;

        &.acitve {
            height: calc(100vh - 280rpx);
        }

        .constnt-box {
            background-color: #dcf5ee80;
            margin: 20rpx 0 30rpx;
            box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
            border-radius: 20rpx;
            padding: 20rpx;

            .content-html {
                font-size: 26rpx;
                margin-bottom: 30rpx;
                line-height: 40rpx;
                padding: 4rpx 0;

                :deep(img),
                .reset-image {
                    width: 100% !important;
                    height: auto;

                    &.active {
                        height: 1334rpx;
                    }
                }
            }
        }

        .files {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: $uni-text-color-inverse;
            margin: 20rpx 0;
            padding: 20rpx;

            .files-image {
                flex: 1;
                display: flex;
                // justify-content: space-between;
                align-items: center;

                .files-text {
                    font-size: 26rpx;
                    // 一行超出隐藏显示
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                }
            }

            .files-btn {
                width: 60rpx;
                text-align: center;
                font-size: 26rpx;
                color: $uni-color-primary;
            }
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        background-color: $uni-text-color-inverse;

        uni-button {
            width: 30%;
            font-size: 30rpx;

            &[type="primary"] {
                background: $uni-color-primary;
            }

            &[disabled][type="primary"] {
                background: #00b78185;
            }
        }

        .butn-primary {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            border-color: $uni-color-primary;
        }

        .butn-default {
            border-color: $uni-text-color-disable;
            color: $uni-text-color-disable;
        }

        .butnDefaultActive {
            border-color: $uni-color-primary;
            color: $uni-color-primary;
        }

        .disabledAcitve {
            border-color: #d5cdcd;
            color: #d5cdcd;
        }
    }
}

:deep(.uni-modal__bd) {
    color: $uni-text-color;
}
</style>

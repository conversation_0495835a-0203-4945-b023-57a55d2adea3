<!-- 通知公告 -->
<template>
    <view class="announcement">
        <view class="announcement-item" v-for="(item, index) in dataList" :key="index" @click="onConfirm(item)">
            <template v-if="paramsForm.type == 'publish'">
                <view class="cell-item-hander">
                    {{ item.title }}
                </view>
                <view class="cell-item-content">
                    {{ item.identityUserName }}发布于<text :class="{ active: !item.status && !item.isRetract }"> {{ item.timerDate }}</text>
                    <template v-if="item.status">
                        <text> 浏览{{ item.viewUsers }}次 </text>
                        <text v-if="item.isNeedConfirm"> 确认{{ item.confirmUsers }}/{{ item.receiveUsers }} </text>
                    </template>
                </view>
                <view class="cell-item-footer">
                    <view class="icon">
                        <image class="isTop" v-if="item.isTop" src="@nginx/workbench/notice/isTop.png" />
                        <text class="screenDomination" v-if="item.isDominateScreen">霸屏</text>
                    </view>
                    <uni-list-item :class="{ resetListItemActive: !item.status, resetListItem: item.isRetract }" :rightText="item.isRetract ? '已撤回' : item.statusRemark" />
                </view>
            </template>
            <template v-else>
                <view class="cell-item-hander">
                    <view class="list-title-icon" v-if="!item.isView">未 读</view>
                    {{ item.title }}
                </view>
                <view class="cell-item-content"> {{ item.identityUserName }} 发布于 {{ item.timerDate }} </view>
                <view class="cell-item-footer" v-if="item.isNeedConfirm">
                    <i />
                    <uni-list-item :class="{ resetConfimItemActive: item.isConfirm }" :rightText="!item.isConfirm ? '去确认' : '已确认'" />
                </view>
            </template>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["complete", "reload"])
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    dataList: {
        type: Array,
        default: () => []
    }
})

const dataList = computed(() => props.dataList)

const initPage = (pageNo, pageSize, isAll) => {
    const { type, startTime, endTime } = props.paramsForm
    const params = {
        identifier: "announcement",
        keyWords: "",
        startTime,
        endTime,
        pageNo,
        pageSize
    }
    // 我收到的
    let url = "/app/mobile/mess/receive/page"
    // 我发布的
    if (type == "publish") {
        url = "/app/mobile/mess/publish/page"
    }
    // 所有我发布的
    if (isAll) {
        url = "/app/mobile/mess/publish/page/all"
    }
    http.post(url, params).then(({ data }) => {
        emit("complete", data.list)
    })
}
// 去确认、详情
const onConfirm = (item) => {
    if (!item.isView) {
        emit("reload")
    }
    navigateTo({
        url: "/apps/notice/announcement/details",
        query: {
            id: item.id,
            type: props.paramsForm.type,
            receiveUsers: item.receiveUsers,
            contentType: item.contentType,
            messType: item.messType
        }
    })
}

watch(
    () => props.paramsForm,
    () => {
        emit("reload")
    },
    {
        deep: true
    }
)

onShow(() => {
    emit("reload")
})

defineExpose({ initPage })
</script>

<style lang="scss" scoped>
$color6: #666666;
$warning: #faad14;
$red: red;

.announcement {
    padding: 0rpx 30rpx 30rpx 30rpx;

    .announcement-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 20rpx 0;
        padding: 30rpx;

        .cell-item-hander {
            font-size: 30rpx;
            font-weight: 600;
            // 两行超出隐藏
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;

            .list-title-icon {
                display: inline-block;
                font-size: 20rpx;
                color: $red;
                border-radius: 8rpx;
                width: 65rpx;
                height: 25rpx;
                text-align: center;
                border: 0.01rpx solid $red;
                // transform: scale(0.68);
            }
        }

        .cell-item-content {
            font-size: 24rpx;
            margin: 20rpx 0;
            color: $color6;

            .active {
                color: $warning;
            }
        }

        .cell-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .icon {
                display: flex;
                align-items: center;

                .isTop {
                    width: 32rpx;
                    height: 32rpx;
                    margin-right: 10rpx;
                }

                .screenDomination {
                    color: $uni-color-primary;
                    font-size: 20rpx;
                    background-color: #00b7811a;
                    border-radius: 10rpx;
                    padding: 6rpx;
                }
            }

            :deep(.uni-list-item) {
                .uni-list--border {
                    display: none;
                }

                &.resetConfimItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $uni-text-color-grey;
                    }
                }

                &.resetListItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $warning;
                    }
                }

                &.resetListItem {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $uni-text-color-grey;
                    }
                }

                .uni-list-item__container {
                    padding: 0;
                }

                .uni-list-item__extra span,
                .uni-icon-wrapper {
                    color: $uni-color-primary;
                    padding: 0;
                    font-size: 28rpx;
                }
            }
        }
    }
}
</style>

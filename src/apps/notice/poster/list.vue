<!-- 海报 -->
<template>
    <view class="news">
        <view :class="{ active: paramsForm.type == 'receive' }">
            <view class="news-item" v-for="(item, index) in dataList" :key="index" @click="onConfirm(item)">
                <!-- 我发布的 -->
                <view class="news-item-content" v-if="paramsForm.type == 'publish'">
                    <view class="img-box" v-if="item.details.length">
                        <image class="img" :src="item.details[1]?.contentImg" />
                    </view>
                    <image v-if="item.contentImg" class="img-box" :src="item.contentImg" />
                    <view class="cell-item" :class="{ active: item.contentImg }">
                        <view class="cell-item-hander">
                            {{ item.title }}
                        </view>
                        <view class="cell-item-content">
                            <view class="list-source">
                                {{ item.identityUserName }} 发布于
                                <text :class="{ listSourceActive: !item.status }">
                                    {{ item.timerDate }}
                                </text>
                            </view>
                        </view>

                        <view class="cell-item-footer">
                            <text class="screenDomination" v-if="item.isDominateScreen">霸屏</text>
                            <i v-else />
                            <uni-list-item :class="{ resetConfimItemActive: !item.status }" :rightText="item.status ? '已发布' : '待发布'" />
                        </view>
                    </view>
                </view>
                <view class="news-item-content" v-else>
                    <view class="img-box-receive">
                        <view class="img-box">
                            <image class="img" v-if="item.details.length" :src="item.details[1]?.contentImg" />
                            <image v-if="item.contentImg" class="img-box" :src="item.contentImg" />
                        </view>
                        <view class="list-source">
                            {{ item.timerDate }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
const emit = defineEmits(["complete", "reload"])
const props = defineProps({
    paramsForm: {
        type: Object,
        default: () => {}
    },
    dataList: {
        type: Array,
        default: () => []
    }
})

const dataList = computed(() => props.dataList)
const initPage = (pageNo, pageSize, isAll) => {
    const { type, startTime, endTime } = props.paramsForm
    const params = {
        identifier: "poster",
        keyWords: "",
        startTime,
        endTime,
        pageNo,
        pageSize
    }
    // 我收到的
    let url = "/cloud/mobile/mess/receive/page"
    // 我发布的
    if (type == "publish") {
        url = "/cloud/mobile/mess/publish/page"
    }
    // 所有我发布的
    if (isAll) {
        url = "/app/mobile/mess/publish/page/all"
    }
    http.post(url, params).then(({ data }) => {
        emit("complete", data.list)
    })
}
// 去确认、详情
const onConfirm = (item) => {
    if (!item.isView) {
        emit("reload")
    }
    navigateTo({
        url: "/apps/notice/poster/details",
        query: {
            id: item.id,
            type: props.paramsForm.type,
            receiveUsers: item.receiveUsers,
            contentType: item.contentType,
            messType: item.messType
        }
    })
}

watch(
    () => props.paramsForm,
    () => {
        emit("reload")
    },
    {
        deep: true
    }
)
onShow(() => {
    emit("reload")
})
defineExpose({ initPage })
</script>

<style lang="scss" scoped>
$color6: #666666;
$warning: #faad14;
$red: red;

.news {
    padding: 0rpx 30rpx 30rpx 30rpx;
    .active {
        border-radius: 20rpx;
        overflow: hidden;
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        background-color: $uni-text-color-inverse;

        .news-item {
            border-radius: 0;
            margin: 0 6px;

            .img-box-receive {
                .img-box {
                    width: 240rpx;
                    height: 180rpx;
                    margin: 0 auto;

                    .img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .list-source {
                    margin-top: 20rpx;
                    font-size: 26rpx;
                    color: $color6;
                }
            }
        }
    }

    .news-item {
        background: $uni-bg-color;
        border-radius: 20rpx;
        margin: 20rpx 0;
        padding: 30rpx;

        .news-item-content {
            display: flex;
            align-items: center;

            .img-box {
                width: 240rpx;
                height: 180rpx;
                margin-right: 20rpx;

                .img {
                    width: 100%;
                    height: 100%;
                }
            }

            .cell-item {
                flex: 1;

                &.active {
                    height: 190rpx;
                }

                .cell-item-hander {
                    font-size: 30rpx;
                    font-weight: 600;
                    // 两行超出隐藏
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    line-height: 1.5;
                }

                .listSourceActive {
                    color: $warning;
                }
            }
        }

        .cell-item-content {
            font-size: 24rpx;
            margin: 20rpx 0 0;
            color: $color6;

            .list-source {
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;

                &:not(:last-child) {
                    margin: 10rpx 0;
                }
            }

            .active {
                color: $warning;
            }
        }

        .cell-item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex: 1;

            .screenDomination {
                color: $uni-color-primary;
                font-size: 20rpx;
                background-color: #00b7811a;
                border-radius: 10rpx;
                padding: 6rpx;
            }

            :deep(.uni-list-item) {
                .uni-list--border {
                    display: none;
                }

                .uni-list-item__container {
                    padding: 0;

                    .uni-list-item__content {
                        display: none;
                    }
                }

                &.resetConfimItemActive {
                    .uni-list-item__extra span,
                    .uni-icon-wrapper {
                        color: $warning !important;
                    }
                }

                .uni-list-item__extra-text span,
                .uni-icon-wrapper {
                    padding: 0;
                    font-size: 24rpx;
                    color: $uni-color-primary;
                }

                .uni-list-item__container {
                    padding: 0;
                }
            }
        }
    }
}
</style>

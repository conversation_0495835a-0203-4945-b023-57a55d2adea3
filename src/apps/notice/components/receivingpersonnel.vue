<template>
    <view class="notification-scope">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="接收人员">
            <template v-slot:right>
                <!-- #ifdef H5 || H5-WEIXIN-->
                <div class="bar_right" @click="handlerSubimt">确定</div>
                <!-- #endif -->
            </template>
        </uni-nav-bar>
        <view class="reset-list">
            <checkbox-group @change="checkboxChange">
                <label class="uni-list-cell" v-for="item in state.propsForm.deviceList" :key="item.key">
                    <checkbox :value="item.key" activeBackgroundColor="#00b781" activeBorderColor="#00b781" color="#fff" :checked="item.checked" />
                    <view class="name">{{ item.name }}</view>
                </label>
            </checkbox-group>
            <yd-empty class="yd-empty" v-if="!state.propsForm.deviceList.length" text="暂无数据" />
        </view>
    </view>
</template>

<script setup>
const eventChannel = getCurrentInstance().proxy.getOpenerEventChannel()
const state = reactive({
    propsForm: {
        deviceList: [
            {
                name: "全部家长",
                key: "1",
                checked: false
            },
            {
                name: "全部教职工",
                key: "2",
                checked: false
            }
        ]
    }
})
const setMap = (devices) => {
    state.propsForm.deviceList.forEach((item, i) => {
        if (devices.includes(item.key)) {
            state.propsForm.deviceList[i].checked = true
        } else {
            state.propsForm.deviceList[i].checked = false
        }
    })
}

const clickLeft = () => {
    uni.navigateBack()
}

const checkboxChange = (item) => {
    setMap(item.detail.value)
}
const handlerSubimt = () => {
    eventChannel.emit("selectMember", state.propsForm.deviceList)
    uni.navigateBack()
}
onLoad((items) => {
    setMap(items.selectedRecipient)
})
</script>

<style lang="scss" scoped>
.yd-empty {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.notification-scope {
    height: 100vh;

    .bar_right {
        color: $uni-primary;
    }

    .reset-list {
        margin: 10rpx 0;
        overflow: hidden auto;
        height: calc(100vh - 100rpx);

        .uni-list-cell {
            display: flex;
            text-align: center;
            justify-content: flex-start;
            border-bottom: 1px solid $uni-border-color;
            padding: 20rpx;

            .name {
                font-size: 28rpx;
            }
        }
    }
}
</style>

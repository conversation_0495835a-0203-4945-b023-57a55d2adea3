<template>
    <view class="create-footer">
        <slot name="footer">
            <!-- 审批 -->
            <view class="btns" v-if="state.status === 'true'">
                <button class="plain" plain @click="handerGgreeRefuse(0)">拒绝</button>
                <button type="primary" @click="handerGgreeRefuse(2)">同意</button>
            </view>
            <button v-if="!state.status" type="primary" @click="handerSubmit">发布</button>
        </slot>
    </view>
</template>

<script setup>
const props = defineProps({
    forms: { type: Object, default: () => ({}) }
})

const state = reactive({
    isTemplate: false,
    status: "",
    form: {
        id: "",
        approveStatus: ""
    }
})

const handerSubmit = () => {
    let URL = "/app/mobile/mess/publish/create"
    if (props.forms.id) {
        URL = "/app/mobile/mess/publish/update"
    }
    http.post(URL, props.forms).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        uni.redirectTo({
            url: "/apps/notice/index"
        })
    })
}
// 同意 拒绝
const handerGgreeRefuse = (status) => {
    state.form.approveStatus = status
    http.post("/app/mobile/mess/publish/updateApproveStatus", state.form).then(({ message }) => {
        uni.showToast({ title: message, icon: "none" })
        uni.navigateBack()
    })
}

onLoad((options) => {
    // 模板进来的
    if (options.coverImg || options.contentImg) {
        state.isTemplate = true // 是否是模版
    }
    state.status = options.status || ""
    state.form.id = options.id || ""
})
</script>

<style lang="scss" scoped>
.create-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: $uni-text-color-inverse;
    padding: 20rpx;

    uni-button {
        &[type="primary"] {
            background: $uni-color-primary;
        }
    }

    .btns {
        display: flex;
        justify-content: space-between;

        uni-button {
            flex: 1;
            margin: 0 10rpx;
        }

        .plain {
            border-color: $uni-color-primary;
            color: $uni-color-primary;
        }
    }
}
</style>

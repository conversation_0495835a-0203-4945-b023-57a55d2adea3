<template>
    <view class="nav-bar">
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="back" :title="props.id ? props.title : `发布${props.title}`">
            <template #right>
                <!-- #ifdef H5 || H5-WEIXIN-->
                <view v-if="state.status == 'true'" :style="{ color: statusObj[state.approveStatus]?.color }">{{ statusObj[state.approveStatus].text }} </view>
                <view class="bar_right" v-if="state.status === 'false'" @click="handlerTemplate">模版</view>
                <div class="bar_right" v-if="state.status === 'all'" @click="handlerAllReleases">所有发布</div>
                <uni-icons v-if="state.status === 'publish'" type="trash" size="18" @click="handlerDelete"></uni-icons>
                <!-- #endif -->
            </template>
            <template #left> <slot name="left"> </slot></template>
        </uni-nav-bar>
    </view>
</template>

<script setup>
const props = defineProps({
    // 返回事件
    clickLeft: Function,
    title: {
        type: String,
        default: ""
    },
    id: {
        type: String,
        default: ""
    },
    status: {
        type: String,
        default: ""
    }
})
const statusObj = {
    0: { text: "拒绝", color: "#fd4f45" },
    1: { text: "审核中", color: "#f0ad4e" },
    2: { text: "已审核", color: "#00b781" },
    4: { text: "已撤销", color: "#00b781" }
}
const state = reactive({
    id: "",
    title: "",
    status: "false",
    approveStatus: null
})

const back = () => {
    if (props.clickLeft) {
        return props.clickLeft()
    }
    uni.navigateBack({
        delta: 1
    })
}
// 模版
const handlerTemplate = () => {
    navigateTo({
        url: "/apps/notice/components/libraryTemplate",
        success: (res) => {
            res.eventChannel.emit("libraryTemplate", { title: props.title })
        }
    })
}
// 删除我发布的
const handlerDelete = () => {
    uni.showModal({
        title: "提示",
        content: "您确定要删除该条信息吗？",
        confirmColor: "#00b781",
        success(res) {
            if (res.confirm) {
                const { id } = state
                http.post("/app/mobile/mess/publish/delete", { id }).then(({ message }) => {
                    uni.showToast({ title: message, icon: "none" })
                    uni.navigateBack()
                })
            }
        }
    })
}
// 所有发布
const handlerAllReleases = () => {
    navigateTo({
        url: "/apps/notice/allReleases/index",
        query: { dataSelectType: "allReleases" }
    })
}
watch(
    () => props.status,
    (v) => {
        state.status = v
    }
)
onLoad((options) => {
    state.id = options.id || props.id
    state.status = options.status || options.type || props.status || "false"
    state.approveStatus = options.approveStatus || null
})
</script>

<style lang="scss" scoped>
.nav-bar {
    background-color: $uni-bg-color;
    .bar_right {
        color: $uni-primary;
    }
}
</style>

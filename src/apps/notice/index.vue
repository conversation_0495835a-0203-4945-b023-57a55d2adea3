<template>
    <view class="notic">
        <z-paging ref="paging" v-model="state.dataList" @query="initPage" :auto="false">
            <template #top>
                <NavBar title="信息发布" :status="state.isAllPublish ? 'all' : '-'" :clickLeft="clickLeft" />
                <uv-tabs :list="tabsList" :current="state.activeTab" @click="clickTabs" :activeStyle="{ color: pattern.buttonColor }" :inactiveStyle="{ color: pattern.inactive }" lineWidth="20" :customStyle="{ background: pattern.custom }" :lineColor="pattern.buttonColor"></uv-tabs>

                <view class="content">
                    <!-- publish：发布， receive：收到 ， all:收到、发布 -->
                    <DropDownFiltering @emitChangeSelect="emitChangeSelect" :tabKey="state.activeTab" :tabType="user.identityInfo.identity == 2 ? 'receive' : 'all'" />
                </view>
            </template>
            <Announcement ref="comRef" v-if="tabsList[state.activeTab].key === 'announcement'" :dataList="state.dataList" :paramsForm="state.paramsForm" @complete="completeFn" @reload="reloadFn" />
            <News ref="comRef" v-else-if="tabsList[state.activeTab].key === 'news'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="news" @complete="completeFn" @reload="reloadFn" />
            <CampusStyle ref="comRef" v-else-if="tabsList[state.activeTab].key === 'campusStyle'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="campusStyle" @complete="completeFn" @reload="reloadFn" />
            <Article ref="comRef" v-else-if="tabsList[state.activeTab].key === 'article'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="article" @complete="completeFn" @reload="reloadFn" />
            <Receive ref="comRef" v-else-if="tabsList[state.activeTab].key === 'receive'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="receive" @complete="completeFn" @reload="reloadFn" />
            <Poster ref="comRef" v-else-if="tabsList[state.activeTab].key === 'poster'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="poster" @complete="completeFn" @reload="reloadFn" />
            <OfficialDoc ref="comRef" v-else-if="tabsList[state.activeTab].key === 'officialDoc'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="officialDoc" @complete="completeFn" @reload="reloadFn" />
            <Aio_machine ref="comRef" v-else-if="tabsList[state.activeTab].key === 'aio_machine'" :dataList="state.dataList" :paramsForm="state.paramsForm" key="aio_machine" @complete="completeFn" @reload="reloadFn" />
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <uni-fab v-if="state.addBtnTabList.length" ref="fab" :pattern="pattern" vertical="bottom" direction="right" horizontal="right" @fabClick="fabClick" />
        <uni-popup ref="popupRef" type="share" safeArea>
            <view class="popup_container" v-if="state.addBtnTabList.length">
                <view class="uni-title"> <uni-icons type="closeempty" size="20" @click="popupChangeClose"></uni-icons> </view>
                <uni-grid :column="4" :show-border="false" :square="false">
                    <uni-grid-item v-for="(item, idx) in state.addBtnTabList" :index="idx" :key="item.id" @click="clickTabsCreate(item, idx)">
                        <view class="grid-item-box">
                            <image class="image" mode="aspectFit" :src="btnTabIcon[item.perms].icon"></image>
                            <view class="text">{{ item.name }}</view>
                        </view>
                    </uni-grid-item>
                </uni-grid>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import { sendAppEvent, checkPlatform } from "@/utils/sendAppEvent.js"
import useStore from "@/store"
import NavBar from "./components/navBar.vue"
import DropDownFiltering from "./components/dropDownFiltering.vue"
import Announcement from "./announcement/list.vue"
import News from "./news/list.vue"
import CampusStyle from "./campusStyle/list.vue"
import Article from "./article/list.vue"
import Receive from "./receive/list.vue"
import Poster from "./poster/list.vue"
import OfficialDoc from "./officialDoc/list.vue"
import Aio_machine from "./aio_machine/list.vue"
import ToExamine from "./toExamine/list.vue"

const { user } = useStore()
const comRef = ref(null)
const paging = ref(null)

const btnTabIcon = {
    announcement: { name: "通知公告", icon: "@nginx/workbench/notice/notice_icon.png" },
    news: { name: "新闻资讯", icon: "@nginx/workbench/notice/info_icon.png" },
    campusStyle: { name: "校园风采", icon: "@nginx/workbench/notice/info_icon.png" },
    article: { name: "文章鉴赏", icon: "@nginx/workbench/notice/appreciate_icon.png" },
    receive: { name: "失物招领", icon: "@nginx/workbench/notice/recruit_icon.png" },
    poster: { name: "海报", icon: "@nginx/workbench/notice/poster_icon.png" },
    officialDoc: { name: "公文传阅", icon: "@nginx/workbench/notice/poster_icon.png" },
    aio_machine: { name: "图片/视频", icon: "@nginx/workbench/notice/template_icon.png" },
    template: { name: "资源模版库", icon: "@nginx/workbench/notice/template_icon.png" },
    toExamine: { name: "审核", icon: "@nginx/workbench/notice/template_icon.png" },
    captions_notice: { name: "字幕通知", icon: "@nginx/workbench/notice/template_icon.png" },
    toExamine: { name: "审核", icon: "@nginx/workbench/notice/template_icon.png" }
}
// 经问原生开发，无需再做逻辑判断，直接写死 tab
const tabsList = [
    {
        name: "通知公告",
        icon: "@nginx/workbench/notice/notice_icon.png",
        key: "announcement"
    },
    {
        name: "新闻资讯",
        icon: "@nginx/workbench/notice/info_icon.png",
        key: "news"
    },
    {
        name: "校园风采",
        icon: "@nginx/workbench/notice/campus_icon.png",
        key: "campusStyle"
    },
    {
        name: "文章鉴赏",
        icon: "@nginx/workbench/notice/appreciate_icon.png",
        key: "article"
    },
    {
        name: "失物招领",
        icon: "@nginx/workbench/notice/recruit_icon.png",
        key: "receive"
    },
    {
        name: "海报",
        icon: "@nginx/workbench/notice/poster_icon.png",
        key: "poster"
    },
    {
        name: "公文传阅",
        icon: "@nginx/workbench/notice/poster_icon.png",
        key: "officialDoc"
    },
    {
        name: "图片/视频",
        icon: "@nginx/workbench/notice/template_icon.png",
        key: "aio_machine"
    }
    // {
    //     name: "资源模版库",
    // icon: "@nginx/workbench/notice/template_icon.png",
    //     key: 'template',
    // },
]

const pattern = {
    inactive: "#606266",
    custom: "#fff",
    buttonColor: "#00b781"
}

const state = reactive({
    isAllPublish: false,
    activeTab: 0,
    dataList: [],
    paramsForm: {
        type: "receive",
        startTime: "",
        endTime: ""
    },
    addBtnTabList: []
})
const popupRef = shallowRef(null)
const fabClick = () => {
    popupRef.value.open("bottom")
}
const popupChangeClose = () => {
    popupRef.value.close()
}
// tables
const clickTabs = (item) => {
    const index = item.index
    state.activeTab = index
}

function clickLeft() {
    // #ifdef H5
    const roleArr = ["yide-ios-app", "yide-android-app", "yide-Harmony-app"]
    if (roleArr?.includes(checkPlatform())) {
        sendAppEvent("backApp", {}) ||
            uni.switchTab({
                url: "/pages/workbench/index"
            })
        return
    }
    // #endif
    uni.switchTab({
        url: "/pages/workbench/index"
    })
}
// 创建
const clickTabsCreate = (item, idx) => {
    state.activeTab = idx
    const { perms } = item
    let url = `${perms}/create`
    // // 如果是审核列表则进入审核列表
    if (perms === "toExamine") {
        url = `${perms}/list`
    }
    navigateTo({ url })
    popupChangeClose()
}
const emitChangeSelect = (item) => {
    state.paramsForm = item
}

function initPage(pageNo, pageSize) {
    comRef.value.initPage(pageNo, pageSize)
}

// 确认
const getAppRouters = async () => {
    await http.post("/app/appCenter/getAppRouters", { code: "notice" }).then(({ data }) => {
        if (data.length) {
            state.addBtnTabList = []
            // 新增
            data[0].children.forEach((item) => {
                if (item.component === "addRelease") {
                    item.btnList?.forEach((v) => {
                        // 如果是字幕通知这隐藏 因为app 原生没做
                        if (v.perms !== "captions_notice") {
                            state.addBtnTabList.push(v)
                        }
                    })
                }
                // 判断是否有所有发布权限
                if (item.component === "allPublish") {
                    state.isAllPublish = true
                }
                if (item.name === "信发审核") {
                    state.addBtnTabList.push({
                        name: "审核",
                        icon: "@nginx/workbench/notice/template_icon.png",
                        componentList: ToExamine,
                        perms: "toExamine"
                    })
                }
            })
        }
    })
}

function reloadFn() {
    paging.value?.reload()
}

function completeFn(list) {
    paging.value?.complete(list || false)
}
onMounted(async () => {
    await getAppRouters()
    nextTick(() => {
        paging.value.reload()
    })
})
</script>

<style lang="scss" scoped>
.notic {
    min-height: 100vh;
    overflow: hidden;
    max-height: 100vh;
    background: $uni-bg-color-grey;

    .popup_container {
        background: $uni-bg-color;
        width: 100vw;
        min-height: 200rpx;
        box-sizing: border-box;
        padding: 22rpx 38rpx 34rpx 38rpx;
        border-radius: 16rpx;

        .uni-title {
            height: 60rpx;
            text-align: right;
        }

        .grid-item-box {
            text-align: center;

            .image {
                width: 80rpx;
                height: 80rpx;
            }

            .text {
                font-size: 12px;
                margin: 10rpx auto 20rpx;
            }
        }
    }
}
</style>

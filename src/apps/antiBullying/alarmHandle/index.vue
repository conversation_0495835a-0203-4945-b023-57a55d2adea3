<template>
    <view class="container_box">
        <z-paging ref="paging">
            <view class="title">基础信息：</view>
            <view class="base_box">
                <view class="item"
                    ><text class="l_box">设备名称：</text><text class="r_box">{{ state.info.deviceName }}</text></view
                >
                <view class="item"
                    ><text class="l_box">设备类型：</text><text class="r_box">{{ state.info.equipmentTypeName }}</text></view
                >
                <view class="item"
                    ><text class="l_box">设备场地：</text><text class="r_box">{{ state.info.siteName }}</text></view
                >
            </view>
            <view class="title">告警信息：</view>
            <view class="base_box" style="height: 264rpx">
                <view class="item"
                    ><text class="l_box">告警类型：</text><text class="r_box">{{ state.info.alarmTypeName }}</text></view
                >
                <view class="item"
                    ><text class="l_box">告警内容：</text><text class="r_box">{{ state.info.content }}</text></view
                >
                <view class="item"
                    ><text class="l_box">告警开始时间：</text><text class="r_box">{{ state.info.eventTime }}</text></view
                >
                <view class="item">
                    <text class="l_box">告警录音：</text>
                    <view class="r_box">
                        <text v-if="!state.info.hasAlarmAudio" style="color: #00b781">暂无录音</text>
                        <text v-else class="audio_box">
                            <text v-for="item in state.info.alarmAudioUrls" :key="item.id">
                                <text style="color: #ff3145" v-if="item.isExpired">录音已过期</text>
                                <cui-audiobar v-else :src="item.fileUrl" :durationText="formatDuration(item.duration)"></cui-audiobar>
                            </text>
                        </text>
                    </view>
                </view>
            </view>
            <view class="title">处理信息：</view>
            <view class="handle_box">
                <view class="top_box">
                    <view class="top_l">处理结果：</view>
                    <view class="top_r">
                        <view :class="['r_item', state.resultId === item.value ? 'active' : '']" v-for="item in state.resultList" :key="item.value" @click="handleClick(item)">
                            {{ item.label }}
                        </view>
                    </view>
                </view>
                <view class="btm_box">
                    <view class="btm_l">备注：</view>
                    <view class="btm_r">
                        <uni-easyinput class="textarea_box" type="textarea" v-model="state.remark" :maxlength="200" placeholder="请输入内容"></uni-easyinput>
                        <view class="num_box">{{ state.remark.length }}/200</view>
                        <button :class="['btn_class', state.resultId !== null ? 'submit_btn' : '']" :disabled="state.resultId === null" @click="confirm">确认处理结果</button>
                    </view>
                </view>
            </view>
        </z-paging>
    </view>
</template>
<script setup>
import { formatDuration } from "@/utils"
import cuiAudiobar from "../components/cui-audiobar/cui-audiobar.vue"
const state = reactive({
    resultList: [],
    remark: "",
    resultId: null,
    id: null,
    info: {
        alarmAudioUrls: [],
        callAudioUrls: []
    }
})

// 获取告警详情
const getDetail = (id) => {
    http.post("/app/anti-bullying/mgmt/alarm/get", { id }).then((res) => {
        console.log("res:", res)
        if (!res.data) return
        state.info = res.data
    })
}

onLoad((options) => {
    state.id = options.id
    getDetail(options.id)
})

const handleClick = (item) => {
    state.resultId = item.value
}

const confirm = () => {
    const params = {
        id: state.id,
        resultId: state.resultId,
        remark: state.remark
    }

    http.post("/app/anti-bullying/mgmt/alarm/handle", params).then((res) => {
        uni.showToast({
            icon: "none",
            title: res.message
        })
        navigateTo({
            url: "/apps/antiBullying/alarm/index"
        })
    })
}

onMounted(() => {
    // 获取处理结果选项列表
    http.post("/app/anti-bullying/mgmt/alarm-result-option/list").then((res) => {
        state.resultList = res.data.map((i) => ({
            label: i.text,
            value: i.id
        }))
    })
})
</script>
<style lang="scss" scoped>
.container_box {
    padding: 20rpx 22rpx;
    background: #f7f7f7;
    min-height: calc(100vh - 130rpx);
    .title {
        margin: 24rpx 18rpx;
        font-size: 24rpx;
        color: #4d4d4d;
        font-weight: 600;
    }
    .item {
        display: flex;
        overflow: hidden;
        font-size: 24rpx;
        color: #828282;
        align-items: center;
        .l_box {
            width: 170rpx;
            color: #040404;
        }
        .r_box {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .audio_box {
            display: inline-block;
            width: 336rpx;
        }
    }
    .base_box {
        height: 208rpx;
        box-sizing: border-box;
        border-radius: 16rpx;
        background: #fff;
        padding: 28rpx 18rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .handle_box {
        padding: 30rpx 46rpx 66rpx 18rpx;
        background-color: $uni-text-color-inverse;
        border-radius: 16rpx;
        .top_box {
            display: flex;
            .top_l {
                width: 132rpx;
                color: #040404;
                font-size: 24rpx;
                flex-shrink: 0;
            }
            .top_r {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                .r_item {
                    font-size: 20rpx;
                    color: #898989;
                    width: 240rpx;
                    height: 48rpx;
                    border: 2rpx solid #ededed;
                    line-height: 48rpx;
                    text-align: center;
                    margin-bottom: 20rpx;
                }
                .active {
                    color: $uni-text-color-inverse;
                    background: $uni-color-primary;
                }
            }
        }
        .btm_box {
            margin-top: 20rpx;
            display: flex;
            .btm_l {
                flex-shrink: 0;
                width: 94rpx;
                font-size: 24rpx;
                color: #040404;
            }
            .btm_r {
                flex: 1;
                position: relative;
                .textarea_box {
                    :deep(.is-focused) {
                        border-color: $uni-color-primary !important;
                    }
                    :deep(.input-padding) {
                        padding-bottom: 30rpx !important;
                    }
                }
                .num_box {
                    position: absolute;
                    bottom: 80rpx;
                    right: 15rpx;
                    font-size: 20rpx;
                    color: #898989;
                }
                .btn_class {
                    margin-top: 20rpx;
                    margin-left: 0;
                    width: 240rpx;
                    height: 48rpx;
                    font-size: 28rpx;
                    text-align: center;
                    line-height: 48rpx;
                    color: $uni-text-color-inverse;
                    background: #d8d8d8;
                }
                .submit_btn {
                    color: $uni-text-color-inverse;
                    background: $uni-color-primary;
                }
            }
        }
    }
}
</style>

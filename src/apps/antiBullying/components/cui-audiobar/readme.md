#  播放音频组件
> 小程序播放音频组件，全局播放，自动暂停正在播放的音频，重新打开页面时恢复状态
1. 新音频播放时，自动暂停页面中其他正在播放的音频
2. 通过背景音频播放器，后退时继续播放，重新进入此页面时，继续显示播放状态
3. 自动处理音频路径后面带有时长的情况，如链接： https://xxx.cn/testaudio.durationTime=2232.mp3
4. 兼容企业微信，当企业微信中打开时，使用内部音频播放器，否则使用背景音频播放器

## 平台兼容

| H5  | 微信小程序 | 支付宝小程序 | 百度小程序 | 头条小程序 | QQ 小程序 | App |
| --- | ---------- | ------------ | ---------- | ---------- | --------- | --- |
| 未测   | √          | 未测         | 未测       | 未测          | 未测      | 未测    |


## 代码演示

### 基本用法
```html
<view>
	<!-- 已知音频时长可以直接填上，单位是秒 -->
	<cui-audiobar src="http://music.163.com/song/media/outer/url?id=447925558.mp3"
		:initDuration="230"></cui-audiobar>
	
	<!-- 如果不填，则是是播放后才会出现音频时长 -->
	<cui-audiobar src="http://music.163.com/song/media/outer/url?id=447925558.mp3"></cui-audiobar>
	
	<!-- 如果音频路径后面已经带有时长，会自动处理(注意，下面这个链接格式的示例，不能播放) -->
	<cui-audiobar src="https://xxx.cn/testaudio.durationTime=2232.mp3"></cui-audiobar>
</view>
```

```js
export default {
	data() {
		return {
		}
	},
	methods: { 
	}
}

```

### 插件标签
- 默认 cui-audiobar 为 component
## API
### Props

| 参数			| 说明					| 类型				| 默认值		|
| --------------| ------------			| ----------------	| ------------	|
| src			| 音频url				| <em>String</em>	| 无			|
| initDuration	| 音频时长，单位秒，选填| <em>number</em>	|无				|


### 示例小程序
![](https://web-1257970417.cos.ap-guangzhou.myqcloud.com/common/qrcode-xbrys.jpg)
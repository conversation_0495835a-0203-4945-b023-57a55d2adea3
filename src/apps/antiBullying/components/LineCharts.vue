<template>
    <qiun-data-charts type="line" :opts="state.opts" :chartData="props.chartData" />
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"

const props = defineProps({
    chartData: {
        type: Object,
        default: () => {}
    }
})

const state = reactive({
    opts: {
        color: ["#00b781"],
        tooltipShow: false,
        tapLegend: false,
        dataLabel: false,
        padding: [15, 10, 0, 15],
        enableScroll: false,
        legend: {
            show: false
        },
        xAxis: {
            disableGrid: true
        },
        yAxis: {
            gridType: "solid",
            dashLength: 2,
            showTitle: true,
            data: [
                {
                    title: "单位/次"
                }
            ]
        },
        extra: {
            tooltip: {
                showBox: false
            },
            line: {
                type: "curve",
                width: 2,
                activeType: "hollow"
            }
        }
    }
})
</script>

<style lang="scss" scoped></style>

<script>
export default {
    componentPlaceholder: {
        "qiun-data-charts": "view"
    }
}
</script>

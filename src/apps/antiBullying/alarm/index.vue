<template>
    <view class="container_box">
        <z-paging ref="paging" v-model="state.dataList" @query="queryList" @onRefresh="onRefresh">
            <template #top>
                <view class="top_box">
                    <view class="query_box">
                        <uni-easyinput @change="searchInputChange" primaryColor="#00b781" :styles="{ borderColor: 'transparent' }" class="search_input" v-model="state.deviceName" placeholder="请输入设备名称" confirmType="search" :inputBorder="false" prefixIcon="search" placeholderStyle="#B1B0B2"></uni-easyinput>
                        <view class="search_box" @click="handleSelect">筛选</view>
                    </view>
                    <view class="tab_box">
                        <uni-segmented-control :current="state.current" :values="state.items" styleType="text" inActiveColor="red" active-color="#00b781" @clickItem="onClickItem" />
                    </view>
                </view>
            </template>
            <view class="main_box">
                <itemBox @handleClick="handleClick(item)" class="item" v-for="(item, index) in state.dataList" :key="index" :itemInfo="item" />
            </view>

            <template #bottom>
                <view class="footer">
                    <view class="item" @click="tabClick">
                        <view>
                            <uni-icons fontFamily="antiBullying" :size="18">{{ "&#xe615;" }}</uni-icons>
                        </view>
                        <view class="view">首页</view>
                    </view>
                    <view class="item active">
                        <view>
                            <uni-icons fontFamily="antiBullying" :size="20" color="#00b781">{{ "&#xe618;" }}</uni-icons>
                        </view>
                        <view class="view">告警</view>
                    </view>
                </view>
            </template>
        </z-paging>
        <yd-select-popup ref="selectPopupRef" :list="state.list" title="按时间" @closePopup="closePopup" />
    </view>
</template>
<script setup>
import dayjs from "dayjs"
import { debounce } from "@/utils"
import itemBox from "./components/itemBox.vue"

const today = dayjs().format("YYYY-MM-DD")
const state = reactive({
    dataList: [],
    items: ["全部", "已处理", "未处理"],
    current: 0,
    deviceName: "",
    eventStartTime: null,
    eventEndTime: null,
    list: [
        { label: "全部", value: [null, null] },
        { label: "今日", value: [today, today] },
        { label: "近一周", value: [dayjs().subtract(6, "day").format("YYYY-MM-DD"), today] },
        { label: "近一个月", value: [dayjs().subtract(1, "month").format("YYYY-MM-DD"), today] },
        { label: "近三个月", value: [dayjs().subtract(3, "month").format("YYYY-MM-DD"), today] },
        { label: "近半年", value: [dayjs().subtract(6, "month").format("YYYY-MM-DD"), today] },
        { label: "今年", value: [dayjs().subtract(1, "year").format("YYYY-MM-DD"), today] }
    ]
})

onLoad((option) => {
    state.current = parseInt(option.type) || 0
    state.time = option.time
    if (option.time == "1") {
        state.eventStartTime = today
        state.eventEndTime = today
        state.list[1].isCheck = true
    } else {
        state.list[0].isCheck = true
    }
})

const statusObj = {
    0: null,
    1: 2,
    2: 0
}

const paging = ref(null)
const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        status: statusObj[state.current],
        deviceName: state.deviceName,
        eventStartTime: state.eventStartTime,
        eventEndTime: state.eventEndTime
    }
    http.post("/app/anti-bullying/mgmt/alarm/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const onRefresh = () => {
    state.eventStartTime = null
    state.eventEndTime = null
    state.list[0].isCheck = true
}

// 搜索框输入事件
const searchInputChange = debounce(() => {
    paging.value.reload()
}, 800)

const closePopup = (data) => {
    if (!data) return
    state.eventStartTime = data.value[0]
    state.eventEndTime = data.value[1]
    paging.value.reload()
}

// tab切换
const onClickItem = (e) => {
    state.current = e.currentIndex
    paging.value.reload()
}

const selectPopupRef = ref(null)
const handleSelect = () => {
    selectPopupRef.value.open()
}

const handleClick = (item) => {
    console.log("item:", item)
    // 未处理跳转告警处理
    if (item.status === 0) {
        navigateTo({
            url: "/apps/antiBullying/alarmHandle/index",
            query: {
                id: item.id
            }
        })
    } else {
        navigateTo({
            url: "/apps/antiBullying/alarmDetail/index",
            query: {
                id: item.id
            }
        })
    }
}

const tabClick = () => {
    navigateTo({
        url: "/apps/antiBullying/index"
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    min-height: calc(100vh - 160rpx);
    background: #f7f7f7;
    padding: 24rpx 30rpx;
    .top_box {
        background: $uni-bg-color;
        padding: 24rpx;
        .query_box {
            display: flex;
            align-items: center;
            margin-bottom: 12rpx;
            .search_input {
                background: #f8f8f8;
                border-radius: 12rpx;
            }
            .search_box {
                color: $uni-color-primary;
                margin-left: 14rpx;
            }
        }
        .tab_box {
            :deep(.segmented-control__text) {
                color: #b1b0b2;
            }
        }
    }
    .main_box {
        padding: 16rpx 24rpx;
    }
    .footer {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        height: 120rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 4rpx 12rpx 4rpx rgba(197, 197, 197, 0.5);
        font-size: 20rpx;
        .item {
            view-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100rpx;
            text-align: center;
            .view {
                margin-top: 8rpx;
            }
        }
        .active {
            color: $uni-color-primary;
        }
    }
}
</style>

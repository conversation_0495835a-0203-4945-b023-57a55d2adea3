<template>
    <div class="class_table">
        <view v-show="isLoading" class="loading_box">
            <uv-loading-icon color="#00b781" textColor="#00b781" :vertical="true" :show="isLoading" text="课表加载中..." textSize="30rpx"></uv-loading-icon>
        </view>
        <view v-show="!isLoading">
            <!-- 选择天 -->
            <view class="sift_box">
                <view class="select_day" @click="selectDayRef.open()">
                    <text>{{ nowDayName || "请选择" }}</text>
                    <text class="triangle_down"></text>
                </view>
                <uni-data-picker v-model="classesId" placeholder="请选择" popup-title="请选择班级" :localdata="classList" v-slot:default="{ data, error }" :map="{ text: 'showName', value: 'id', children: 'children' }" parent-field="area" @change="selectPickerData">
                    <view v-if="error" class="picker_list">
                        <text>{{ error }}</text>
                    </view>
                    <view v-else-if="data.length" class="picker_list">
                        <view v-for="(item, index) in data" :key="index">
                            <text v-if="index != 0">/</text>
                            {{ item.text }}
                        </view>
                        <text class="triangle_down"></text>
                    </view>

                    <view v-else class="picker_list"> 选择班级 <text class="triangle_down"></text></view>
                </uni-data-picker>
            </view>
            <view class="table_box">
                <view class="table_content border_top_class" v-if="isNoData(sectionObj)">
                    <view v-for="obj in objList" :key="obj.code">
                        <view class="table_item" v-if="sectionObj[obj.code] && sectionObj[obj.code].length">
                            <view class="period_time border_bottom_class" :class="obj.class">{{ obj.value }}</view>
                            <view class="right_box">
                                <view class="festival border_bottom_class" v-for="item in sectionObj[obj.code]" :key="item.id">
                                    <view class="festival_item">
                                        {{ item.sequence }}
                                    </view>
                                    <view class="course" :class="expire(item.course) ? 'course_grey' : ''">
                                        {{ item.course?.subjectName || "/" }}
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <yd-empty text="本周暂无课表数据" v-else :isMargin="true" />
            </view>
            <div class="back_today" v-if="isShowBackToday || thisWeek != selectWeek" @click="backToday">
                <image mode="widthFix" class="back_icon" src="@nginx/workbench/schoolTable/back_today.png" alt="" />
            </div>
        </view>
        <yd-select-popup ref="selectDayRef" title="请选择" :list="weekDayList" @closePopup="closeSelectDay" :selectId="[nowDayId]"></yd-select-popup>
    </div>
</template>

<script setup>
import { defaultArrId, setArrCourse, expire, objList, isNoData } from "../data"
import dayjs from "dayjs"

const { system } = store()
const emit = defineEmits(["backWeek"])
const props = defineProps({
    thisWeek: {
        type: Number,
        default: null
    }
})
const selectDayRef = ref(null)
const weekDayList = ref([])
const nowDayId = ref(null)
const nowDayName = ref("")

const classList = computed(() => system.apps.schoolTable.tableClassList)
const classesId = ref("")

const isShowBackToday = ref(false)
const sectionObj = ref({})
const getData = ref([])
const isLoading = ref(false)

const thisWeek = computed(() => props.thisWeek)
const selectWeek = computed(() => system.apps.schoolTable.tableSelectWeek)

// 回显数据
async function setData() {
    const { sectionList, timetableList } = getData.value
    const courseList = []
    timetableList.forEach((i) => {
        if (i.week == nowDayId.value) {
            courseList.push(i)
        }
    })
    await setArrCourse(courseList, sectionList)
    sectionObj.value = sectionList
}

async function setPageData(data) {
    getData.value = data
    const { weekMap } = data
    weekDayList.value = weekMap.map((i, index) => {
        return {
            value: index + 1,
            label: i
        }
    })
    nowDayId.value = dayjs().day() // 当前周
    nowDayName.value = weekMap[nowDayId.value - 1]
    isShowBackToday.value = nowDayId.value != dayjs().day()
    await setData()
}

async function getList() {
    classesId.value = await defaultArrId(classList.value)
    const storeData = system.apps.schoolTable
    if (storeData && storeData.classesTable && storeData.classesTable.thisWeek == storeData.tableSelectWeek) {
        setPageData(storeData.classesTable)
    } else {
        getClassTable()
    }
}

// 获取课表
function getClassTable() {
    const param = {
        type: 1, // 类型:1班级 2场地,
        typeId: classesId.value, // 班级/场地ID
        weekTime: selectWeek.value
    }
    isLoading.value = true
    http.post("/app/timetable/getWeekTime", param)
        .then(async (res) => {
            setPageData(res.data)
            system.setAppData({ sys: "schoolTable", data: { classesTable: res.data } })
        })
        .finally(() => {
            isLoading.value = false
        })
}

// 选择天
async function closeSelectDay(obj) {
    if (!obj) return
    nowDayId.value = obj.value
    nowDayName.value = obj.label
    isShowBackToday.value = nowDayId.value != dayjs().day()
    await setData()
}

// 选择班级
function selectPickerData() {
    getClassTable()
}

// 回到今天
function backToday() {
    const storeData = system.apps.schoolTable
    if (thisWeek.value == selectWeek.value) {
        const value = dayjs().day()
        const label = storeData.classesTable?.weekMap[value - 1]
        closeSelectDay({ value, label })
    } else {
        emit("backWeek")
    }
}

defineExpose({ getList, getClassTable })
</script>

<style lang="scss" scoped>
@import "../schoolTable.scss";
</style>

<template>
    <view class="week_container_box">
        <view class="top_box">
            <view class="up_box">{{ title }}</view>
            <SelectBtn :list="list" v-model:value="value" @handleClick="selectClick"></SelectBtn>
            <view class="mian_box">
                <view class="top">
                    <text class="left text" @click="attendSchool">{{ state.typeData.name }}</text>
                    <text class="right text" v-if="state.checkList.length && state.typeData.value !== 2" @click="attendName">{{ state.checkData.name }}</text>
                </view>
            </view>
            <view class="down_box">
                <view class="total_box" v-if="state.typeData.value === 2">
                    <view class="top">{{ state.totalNum }}</view>
                    <view>总节次/节</view>
                </view>
                <stat-box v-if="state.typeData.value === 2" :list="state.statListO" @handleClick="statClick"></stat-box>
                <stat-box v-else :list="state.statListT" @handleClick="statClick"></stat-box>
            </view>
        </view>
        <view class="msg_box">
            <ListMsgBox :list="dataList" :show="state.typeData.value !== 2 ? true : false" @handleDetailClick="handleDetailClick"></ListMsgBox>
            <uni-load-more iconType="auto" :status="status" />
        </view>

        <select-popup ref="selPopO" title="选择考勤类型" :list="state.typeList" @closePopup="(val) => closePopup(val, 0)" />

        <select-popup ref="selPopT" title="选择考勤 " :list="state.checkList" @closePopup="(val) => closePopup(val, 1)" />
    </view>
</template>

<script setup>
import SelectPopup from "../../components/selectPopup.vue"
import StatBox from "../../components/statBox.vue"
import ListMsgBox from "../../components/listMsgBox.vue"
import SelectBtn from "../../components/selectBtn.vue"
import useQueryList from "../../hook/useQueryList.js"
import { onReachBottom } from "@dcloudio/uni-app"

import { getMonthWeekList, getWeekOfMonth } from "@/utils/getDate"
const { getList, dataList, pageNo, status } = useQueryList()

const { weekOfMonth, startOfWeek, endOfWeek } = getWeekOfMonth()
const timeGroup = capitalize(startOfWeek) + " - " + capitalize(endOfWeek).slice(5)
const list = ref(getMonthWeekList())
const value = ref(weekOfMonth)
let title = ref(timeGroup)

const state = reactive({
    typeList: [
        { name: "出入校考勤", value: 0, isCheck: true },
        { name: "事件考勤", value: 1 },
        { name: "课程考勤", value: 2 }
    ],
    statListO: [
        { name: "迟到/人", type: "beLateNum", value: "0", flag: 1 },
        { name: "请假/人", type: "leaveNum", value: "0", flag: 3 },
        { name: "缺勤/人", type: "absenteeismNum", value: "0", flag: 4 }
    ],
    statListT: [
        { name: "迟到/人", type: "beLateNum", value: "0", flag: 1 },
        { name: "早退/人", type: "leaveEarlyNum", value: "0", flag: 2 },
        { name: "请假/人", type: "leaveNum", value: "0", flag: 3 },
        { name: "缺勤/人", type: "absenteeismNum", value: "0", flag: 4 }
    ],
    checkList: [],
    typeData: { name: "出入校考勤", value: 0 },
    checkData: {},
    totalNum: 0,
    statData: {},
    classesId: ""
})

function getPageList(params) {
    return http.post("/app/master/attendance/dateStatisticsPage", params)
}
// 获取考勤列表
const getEventList = async () => {
    const time = list.value[value.value - 1]
    const params = {
        classesId: state.classesId,
        startDate: time.startDate,
        endDate: time.endDate,
        type: state.typeData.value
    }

    const { data } = await http.post("/app/master/attendance/getEventList", params)
    state.checkList = data.map((i) => ({ ...i, value: i.id }))

    if (state.checkList.length) {
        state.checkData = state.checkList[0]
        state.checkData.isCheck = true
    }
    getTimeTableDate()
}

// 获取周月统计头部
async function getTimeTableDate() {
    const time = list.value[value.value - 1]
    const params = {
        classesId: state.classesId,
        startDate: time.startDate,
        endDate: time.endDate,
        type: state.typeData.value,
        attendanceId: state.checkData.value === 2 ? state.checkData.value : ""
    }

    const res = await http.post("/app/master/attendance/dateStatistics", params)
    if (res.data) {
        if (state.typeData.value === 2) {
            state.statListO.forEach((i) => (i.value = res.data[i.type]))
        } else {
            state.statListT.forEach((i) => (i.value = res.data[i.type]))
        }
        state.totalNum = res.data.totalNum
    }
    getList(getPageList, params)
}

const handleDetailClick = (val) => {
    const params = {
        navTitle: val.studentName,
        title: `${list.value[value.value].name} | ${title.value}`,
        params: JSON.stringify({
            attendanceId: val.attendanceId,
            classesId: val.classesId,
            endDate: val.endDate,
            startDate: val.startDate,
            type: val.type,
            userId: val.userId
        })
    }
    navigateTo({
        url: "/apps/attendance/classTeacher/abnormalDetail/index",
        query: params
    })
}

const statClick = (val) => {
    const params = {
        navTitle: val.name.substr(0, 2) + "详情",
        title: title.value,
        type: 0,
        value: value.value,
        checkData: state.checkData.value === 2 ? state.checkData.value : "",
        typeData: state.typeData.value
    }
    navigateTo({
        url: "/apps/attendance/classTeacher/information/index",
        query: params
    })
}

const selectClick = (val) => {
    title.value = capitalize(val.startDate) + " - " + capitalize(val.endDate).slice(5)
    dataList.value = []
    pageNo.value = 1
    getEventList()
}

function capitalize(date) {
    return date.replace("-", "年").replace("-", "月") + "日"
}

const closePopup = (val, flag) => {
    dataList.value = []
    pageNo.value = 1
    if (flag === 0) {
        state.typeData = val
        getEventList()
    } else {
        state.checkData = val
        getTimeTableDate()
    }
}

// 考勤事件
const selPopO = ref(null)
const attendSchool = () => {
    selPopO.value.open()
}

// 对应考勤名称
const selPopT = ref(null)
const attendName = () => {
    selPopT.value.open()
}

onLoad((options) => {
    state.classesId = options.classesId
    getEventList()
})

onReachBottom(() => {
    const time = list.value[value.value - 1]
    const params = {
        classesId: state.classesId,
        startDate: time.startDate,
        endDate: time.endDate,
        type: state.typeData.value,
        attendanceId: state.checkData.value === 2 ? state.checkData.value : ""
    }
    getList(getPageList, params)
})
</script>

<style lang="scss">
.week_container_box {
    background-color: #f0f2f5;
    .top_box {
        .up_box {
            height: 100rpx;
            line-height: 100rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
            background-color: #f3fcf9;
            border-bottom: 1rpx solid #d9d9d9;
        }
        .mian_box {
            padding: 0 30rpx;
            background: #fff;
            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 100rpx;
                border-bottom: 1rpx solid #d9d9d9;
                .left {
                    text-align: left;
                    width: 170rpx;
                }
                .right {
                    text-align: right;
                    width: 250rpx;
                }
                .text {
                    position: relative;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: #00b781;
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
        }
        .down_box {
            padding: 30rpx;
            background-color: #fff;
            display: flex;
            .total_box {
                width: 160rpx;
                text-align: center;
                font-size: 28rpx;
                position: relative;
                border-radius: 20rpx;
                padding: 26rpx 0rpx;
                background-color: #f0f2f5;
                margin-right: 15rpx;
                .top {
                    font-size: 40rpx;
                    margin: 10rpx 0 16rpx 0;
                    text-align: center;
                }
            }
        }
    }
    .msg_box {
        background-color: #fff;
        margin-bottom: 20rpx;
    }
}
</style>

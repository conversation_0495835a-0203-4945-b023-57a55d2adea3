<template>
  <view
    class="day_list_box"
    v-for="(item, index) in list"
    :key="index"
    @click="handleDetailClick(item)"
  >
    <view class="left_box">
      <text class="head">{{ item.studentName.charAt(0) }}</text>
      {{ item.studentName }}
    </view>
    <view class="right_box">
      <view class="item">正常 {{ item.normalNum }} 次</view>
      <view class="item">迟到 {{ item.beLateNum }} 次</view>
      <view class="item" v-if="show">早退 {{ item.leaveEarlyNum }} 次</view>
      <view class="item">请假 {{ item.leaveNum }} 次</view>
      <view class="item danger">缺勤 {{ item.absenteeismNum }} 次</view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue'

defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  show: {
    type: <PERSON>olean,
    default: true,
  },
})

const emit = defineEmits(['handleDetailClick'])

const handleDetailClick = val => {
  emit('handleDetailClick', val)
}
</script>

<style lang="scss">
.day_list_box {
  width: 690rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 20rpx;
  border: 1rpx solid #d9d9d9;
  margin: 0 auto;
  margin-bottom: 20rpx;
  padding: 20rpx 30rpx;
  display: flex;
  font-size: 28rpx;
  .left_box {
    display: flex;
    width: 200rpx;
    align-items: center;
    font-weight: 600;
    margin-right: 20rpx;
    .head {
      display: inline-block;
      color: #ffffff;
      background-color: #00b781;
      width: 60rpx;
      height: 60rpx;
      text-align: center;
      line-height: 60rpx;
      margin-right: 16rpx;
      border-radius: 50%;
    }
  }
  .right_box {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    .item {
      width: 200rpx;
      margin: 10rpx 0rpx;
      text-align: center;
    }
    .danger {
      color: #f5222d;
    }
  }
}
</style>

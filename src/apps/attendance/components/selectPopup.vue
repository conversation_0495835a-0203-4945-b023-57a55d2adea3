<template>
    <view>
        <uni-popup ref="selectPopup" @click.stop type="bottom" :is-mask-click="false" :safe-area="false">
            <div class="week_popup">
                <div class="title">
                    <span class="text">{{ title }}</span>
                    <img class="image" @click="closeFn" src="https://alicdn.1d1j.cn/announcement/20230706/193b257d495e428e9cbe74ff02432f93.png" alt="" />
                </div>
                <uni-list :border="false">
                    <uni-list-item @click="changeWeek(item)" clickable v-for="(item, index) in list" :key="index">
                        <template v-slot:header>
                            <slot :item="item">
                                <text>{{ item.name }}</text>
                            </slot>
                        </template>
                        <template v-slot:footer>
                            <image v-if="item.isCheck" class="select" src="https://alicdn.1d1j.cn/announcement/20230706/b145ff2645b345d09410448a346d115d.png"></image>
                        </template>
                    </uni-list-item>
                </uni-list>
            </div>
        </uni-popup>
    </view>
</template>

<script setup>
import { ref, watch, computed } from "vue"

const selectPopup = ref(null)
const $emit = defineEmits(["closePopup"])
const $props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    title: {
        type: String,
        default: ""
    },
    multiple: {
        type: Boolean,
        default: false
    }
})

function changeWeek(data) {
    if (!$props.multiple) {
        $props.list.forEach((i) => (i.isCheck = false))
        data.isCheck = !data.isCheck
    } else {
        data.isCheck = !data.isCheck
    }
}

const open = () => {
    selectPopup.value.open()
}

defineExpose({ open })

let value = null
function closeFn() {
    if (!$props.multiple) {
        value = $props.list.find((i) => i.isCheck)
    } else {
        value = $props.list.filter((i) => i.isCheck)
    }
    selectPopup.value.close()
    $emit("closePopup", value)
}
</script>

<style lang="scss" scoped>
// 选择周
.week_popup {
    min-height: 300rpx;
    max-height: 750rpx;
    overflow-y: auto;
    background: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 120rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-family:
                PingFangSC-Medium,
                PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
        }

        .image {
            height: 44rpx;
            width: 44rpx;
            margin-right: 18rpx;
            flex-shrink: 0;
        }
    }

    .select {
        height: 40rpx;
        width: 40rpx;
    }

    .text {
        font-size: 28rpx;
        font-family:
            PingFangSC-Regular,
            PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;

        .time {
            color: #999999;
        }
    }
}
</style>

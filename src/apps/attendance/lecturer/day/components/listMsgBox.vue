<template>
  <view class="day_list_box" v-for="(item, index) in list" :key="index">
    <view class="left_box">
      <text class="head">{{ item.studentName.charAt(0) }}</text>
      {{ item.studentName }}
    </view>
    <view class="right_box">
      <view class="box">
        <view>{{ item.ruleStatus === 0 ? '签到' : '签退' }}：{{ item.requiredTime }}</view>
        <view :style="{ color: change(item.status, 'color') }">{{
          change(item.status, 'status')
        }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>


defineProps({
  list: {
    type: Array,
    default: () => [],
  },
})

const colorType = {
  0: '#00B781',
  1: '#F5222D',
  2: '#FC941F',
  3: '#1EC1C3',
  5: '#333333',
  6: '#8C8C8C',
}

const statusType = {
  0: '正常',
  1: '缺勤',
  2: '迟到',
  3: '早退',
  5: '请假',
  6: '未签到',
}

const checkType = {
  color: colorType,
  status: statusType,
}

const change = (val, type) => {
  return checkType[type][val] || ''
}
</script>

<style lang="scss">
.day_list_box {
  width: 690rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 20rpx;
  border: 1rpx solid #d9d9d9;
  margin: 0 auto;
  margin-bottom: 20rpx;
  padding: 20rpx 30rpx;
  display: flex;
  font-size: 28rpx;
  .left_box {
    display: flex;
    width: 200rpx;
    align-items: center;
    font-weight: 600;
    .head {
      color: #ffffff;
      background-color: #00b781;
      padding: 10rpx 16rpx;
      margin-right: 16rpx;
      border-radius: 50%;
    }
  }
  .right_box {
    flex: 1;
    margin: auto 0;
    .box {
      display: flex;
      flex: 1;
      justify-content: space-around;
      line-height: 40rpx;
    }
  }
}
</style>

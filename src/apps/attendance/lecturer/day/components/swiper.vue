<template>
    <view class="swiper_wrap_box">
        <view>
            <swiper class="swiper" :current="current" circular :indicator-dots="indicatorDots" @change="change">
                <swiper-item v-for="(item, index) in list" :key="index">
                    <view class="swiper-item">
                        <view class="main">
                            <view class="top_msg">{{ item.sequence }} {{ item.classesName }} {{ item.requiredTime }} </view>
                            <l-circular-progress class="yd_circularProgress" :canvasId="'canvas' + index" :bgCanvasId="'bgCanvas' + index" :fontShow="false" :percent="parseInt((item.actualNum / item.totalNum) * 100)" type="halfCircular" :lineWidth="14" progressColor="#00B781" bgColor="#F0F2F5" :boxWidth="240" :boxHeight="150">
                                <view class="yd_circular_box">
                                    <view
                                        ><text class="left">实签 {{ item.actualNum }}</text
                                        >/<text>应签 {{ item.totalNum }}</text></view
                                    >
                                </view>
                            </l-circular-progress>
                        </view>
                        <view class="footer">
                            <view class="footer_box">迟到 {{ item.beLateNum }}</view>
                            <view class="footer_box">请假 {{ item.leaveNum }}</view>
                            <view class="footer_box danger" :style="{ color: dayjs().isBefore(dayjs(item.endTime)) ? '#f5222d' : '#8C8C8C' }">{{ dayjs().isBefore(dayjs(item.endTime)) ? "缺勤" : "未签到" }} {{ item.absenteeismNum }}</view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </view>
    </view>
</template>

<script setup>
import LCircularProgress from "@/subModules/components/l-circular-progress/components/l-circular-progress/l-circular-progress.vue"
import dayjs from "dayjs"

defineProps({
    list: {
        type: Array,
        default: () => []
    },
    current: {
        type: Number,
        default: 0
    }
})

const indicatorDots = ref(true)

const emit = defineEmits(["change", "update:current"])

const change = (e) => {
    emit("update:current", e.detail.current)
    emit("change", e.detail.current)
}
</script>

<style lang="scss">
.swiper_wrap_box {
    padding: 30rpx;
    padding-bottom: 0rpx;
    .swiper {
        height: 450rpx;
        .swiper-item {
            .top {
                display: flex;
                justify-content: space-between;
                .right_text {
                    position: relative;
                    text-align: right;
                    width: 250rpx;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: #00b781;
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
            .main {
                .top_msg {
                    text-align: center;
                    margin-bottom: 10rpx;
                }
                .yd_circularProgress {
                    margin: 0 auto;
                    position: relative;
                    .yd_circular_box {
                        z-index: 999;
                        width: 480rpx;
                        height: 150rpx;
                        position: absolute;
                        bottom: 0rpx;
                        left: 0;
                        display: flex;
                        align-items: center;
                        justify-content: space-evenly;
                        flex-direction: column;
                        .top_text {
                            position: relative;
                            text-align: right;
                            width: 250rpx;
                            padding-right: 30rpx;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            -o-text-overflow: ellipsis;
                            margin-bottom: 20rpx;
                            &::after {
                                content: "";
                                width: 0;
                                height: 0;
                                display: inline-block;
                                border: 14rpx solid transparent;
                                border-top-color: #00b781;
                                position: absolute;
                                right: 0rpx;
                                top: 15rpx;
                            }
                        }
                        .left {
                            color: #00b781;
                        }
                    }
                }
            }
            .footer {
                display: flex;
                .danger {
                    color: #f5222d;
                }
                .footer_box {
                    width: 230rpx;
                    text-align: center;
                }
                .center_box {
                    border-left: 1px solid #d9d9d9;
                    border-right: 1px solid #d9d9d9;
                }
            }
        }
    }
}
</style>

<template>
  <view class="swiper_wrap_box">
    <view>
      <swiper
        class="swiper"
        :current="current"
        circular
        :indicator-dots="indicatorDots"
        @change="change"
      >
        <swiper-item v-for="(item, index) in list" :key="index">
          <view class="mian_box">
            <view class="top">
              <text class="left text">课程考勤</text>
              <text class="right text">{{ item.classesName }}</text>
            </view>
          </view>
          <view class="down_box">
            <view class="total_box">
              <view class="top">{{ item.totalNum }}</view>
              <view>总节次/节</view>
            </view>
            <view class="stat_container_box">
              <view class="box">
                <view class="item">
                  <view class="top">{{ item.beLateNum }}</view>
                  <view>迟到/人</view>
                </view>
                <view class="item">
                  <view class="top">{{ item.leaveNum }}</view>
                  <view>请假/人</view>
                </view>
                <view class="item danger">
                  <view class="top">{{ item.absenteeismNum }}</view>
                  <view>缺勤/人</view>
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  current: {
    type: Number,
    default: 0,
  },
})

const indicatorDots = ref(true)

const statClick = val => {
  const params = {
    navTitle: val.name.substr(0, 2) + '详情',
    title: title.value,
    type: 0,
    value: value.value,
    checkData: state.checkData.value,
    typeData: state.typeData.value,
  }
  navigate('/attendance/classTeacher/information/index', params)
}

const emit = defineEmits(['change', 'update:current'])

const change = e => {
  emit('update:current', e.detail.current)
  emit('change', e.detail.current)
}
</script>

<style lang="scss">
.swiper_wrap_box {
  padding-bottom: 0rpx;
  .swiper {
    height: 370rpx;
    background-color: #fff;
    .mian_box {
      padding: 0 30rpx;
      background: #fff;
      .top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 100rpx;
        border-bottom: 1rpx solid #d9d9d9;
        .left {
          text-align: left;
          width: 170rpx;
        }
        .right {
          text-align: right;
          width: 250rpx;
        }
        .text {
          position: relative;
          padding-right: 30rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
        }
      }
    }
    .down_box {
      padding: 30rpx;
      background-color: #fff;
      display: flex;
      .total_box {
        width: 160rpx;
        text-align: center;
        font-size: 28rpx;
        position: relative;
        border-radius: 20rpx;
        padding: 26rpx 0rpx;
        background-color: #f0f2f5;
        margin-right: 15rpx;
        .top {
          font-size: 40rpx;
          margin: 10rpx 0 16rpx 0;
          text-align: center;
        }
      }
      .stat_container_box {
        .box {
          background-color: #f0f2f5;
          border-radius: 20rpx;
          overflow: hidden;
          display: flex;
          padding: 26rpx 0rpx;
          .item {
            text-align: center;
            font-size: 28rpx;
            position: relative;
            border-right: 1rpx solid #d9d9d9;
            width: 170rpx;
            &:last-of-type {
              border: none;
            }
            .top {
              font-size: 40rpx;
              margin: 10rpx 0 16rpx 0;
            }
          }
          .danger {
            color: #f5222d;
          }
        }
      }
    }
    .swiper-item {
      .top {
        display: flex;
        justify-content: space-between;
        .right_text {
          position: relative;
          text-align: right;
          width: 250rpx;
          padding-right: 30rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
        }
      }
      .main {
        .top_msg {
          text-align: center;
          margin-bottom: 10rpx;
        }
        .yd_circularProgress {
          margin: 0 auto;
          position: relative;
          .yd_circular_box {
            z-index: 999;
            width: 480rpx;
            height: 150rpx;
            position: absolute;
            bottom: 0rpx;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            flex-direction: column;
            .top_text {
              position: relative;
              text-align: right;
              width: 250rpx;
              padding-right: 30rpx;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              -o-text-overflow: ellipsis;
              margin-bottom: 20rpx;
            }
            .left {
              color: #00b781;
            }
          }
        }
      }
      .footer {
        display: flex;
        .danger {
          color: #f5222d;
        }
        .footer_box {
          width: 230rpx;
          text-align: center;
        }
        .center_box {
          border-left: 1px solid #d9d9d9;
          border-right: 1px solid #d9d9d9;
        }
      }
    }
  }
}
</style>

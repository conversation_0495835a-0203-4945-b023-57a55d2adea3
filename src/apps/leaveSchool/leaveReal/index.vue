<template>
    <z-paging ref="paging" class="container_box" v-model="state.dataList" @query="queryList">
        <template #top>
            <view class="search_box">
                <uni-search-bar radius="100" placeholder="搜索班级" bgColor="#F0F2F5" v-model="state.classesName" @confirm="confirm" clearButton="none" cancelButton="none" />
            </view>
            <view class="tab_box">
                <uv-tabs lineWidth="20" lineColor="#00B781" :current="state.current" :scrollable="false" :activeStyle="{ color: '#00B781' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" :list="state.tabsList" @click="tabsClick"></uv-tabs>
            </view>
        </template>
        <view class="main_box">
            <view class="item_box" v-for="item in state.dataList" :key="item.classesId">{{ item.classesName }}</view>
        </view>
        <template #empty>
            <view class="empty_box">
                <img class="img" src="@nginx/components/empty.png" />
                <text>暂无数据</text>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
const paging = ref()
const state = reactive({
    current: 0,
    tabsList: [
        { name: "未放学", type: 0 },
        { name: "放学中", type: 1 },
        { name: "已放学", type: 2 }
    ],
    dataList: []
})

const queryList = async (pageNo, pageSize) => {
    try {
        const params = {
            pageNo,
            pageSize,
            status: state.current,
            classesName: state.classesName
        }
        const { data } = await http.post("/app/after/school/liveBoard", params)
        paging.value.complete(data)
    } catch (err) {
        paging.value.complete(false)
    }
}

// tab切换
function tabsClick(item) {
    state.current = item.type
    state.classesName = ""
    paging.value.reload()
}

const confirm = () => {
    paging.value.reload()
}
</script>
<style lang="scss" scoped>
.container_box {
    .search_box {
        background: #fff;
        :deep(.uni-searchbar__box) {
            justify-content: flex-start;
            padding-left: 10rpx;
        }
    }
    .tab_box {
        margin-top: 20rpx;
    }
    .empty_box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #ccc;
        .img {
            width: 540rpx;
            height: 400rpx;
        }
    }
    .main_box {
        padding: 40rpx 30rpx;
        background: #fff;
        .item_box {
            border: 2rpx solid #d9d9d9;
            border-radius: 28rpx;
            height: 100rpx;
            padding: 0 22rpx;
            font-size: 30rpx;
            color: #333;
            display: flex;
            align-items: center;
            margin-bottom: 30rpx;
        }
    }
}
</style>

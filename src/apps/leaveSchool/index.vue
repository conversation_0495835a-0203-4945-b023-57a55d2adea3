<template>
    <z-paging ref="paging" v-model="state.dataList" @query="queryList">
        <view class="header_box">
            <view class="bg_container">
                <img class="bg" src="@nginx/workbench/leaveSchool/bg.png" />
            </view>
            <view class="content">
                <view class="top_title">{{ state.date }} 星期{{ week[state.week] }}</view>
                <view class="mian_title">
                    <view class="box" style="background: #d2fedc" @click="navigateTo({ url: '/apps/leaveSchool/leaveReal/index' })"> 放学实况<img class="icon" src="@nginx/workbench/leaveSchool/right.png" /></view>
                    <view class="box" style="background: #fef5d2" @click="navigateTo({ url: '/apps/leaveSchool/record/index' })"> 放学记录<img class="icon" src="@nginx/workbench/leaveSchool/right.png" /></view>
                </view>
                <view class="input_box">
                    <view class="input">
                        <uni-search-bar radius="100" @confirm="search" placeholder="搜索班级" v-model="state.classesName" bgColor="#fff" clearButton="none" cancelButton="none" />
                    </view>
                    <view class="search_icon" @click="search"><uni-icons type="search" size="28" color="#fff"></uni-icons></view>
                </view>
            </view>
            <view class="modal"></view>
        </view>
        <view class="main_box" v-if="state.dataList.length != 0">
            <view class="item_box" v-for="item in state.dataList" :key="item.afterSchoolLogId">
                <view class="left_box" @click="handleClick(item)">
                    <view v-if="item.status == 0">
                        <uni-icons type="checkbox-filled" size="26" color="#00B781" v-if="item.checked"></uni-icons>
                        <view class="empty_circle" v-else></view>
                    </view>
                </view>
                <view class="right_box">
                    <view style="display: flex; align-items: center; flex: 1; overflow: hidden">
                        <view class="status_box" :style="{ background: status[item.status].bg, color: status[item.status].clo }">{{ status[item.status].title }}</view>
                        <view class="class_box">{{ item.classesName }}</view>
                    </view>
                    <view class="btn" :style="{ background: item.status == 0 ? '#00B781' : '#C7C200' }" @click="handleItem(item)">{{ item.status == 0 ? "立即放学" : "暂不放学" }}</view>
                </view>
            </view>
        </view>
        <template #empty>
            <view class="empty_box">
                <img class="img" src="@nginx/components/empty.png" />
                <text>暂无数据</text>
            </view>
        </template>
        <template #bottom v-if="hidden">
            <view class="bottom_box">
                <button class="btm_btn" @click="submit()" :loading="state.loading" :disabled="state.loading">一键放学</button>
            </view>
        </template>
    </z-paging>

    <yd-popup ref="confirmRef" :titleflag="false" cancelColor="#00B781" @confirm="dialogConfirm">
        <view
            :style="{
                padding: '33px 0px 10px 0px'
            }"
            >{{ state.confirmTitle }}</view
        >
    </yd-popup>
</template>
<script setup>
import dayjs from "dayjs"

const paging = ref(null)
const confirmRef = ref(null)
const week = ["日", "一", "二", "三", "四", "五", "六"]
const status = {
    0: {
        title: "未放学",
        clo: "#00B781",
        bg: "#EBFAF5"
    },
    1: {
        title: "放学中",
        clo: "#DEA300",
        bg: "#FAF6EB"
    },
    2: {
        title: "已放学",
        clo: "#009CDE",
        bg: "#EBF6FA"
    }
}
const state = reactive({
    date: dayjs().format("YYYY.MM.DD"),
    week: dayjs().day(),
    classesName: "",
    dataList: [],
    loading: false,
    classID: "",
    confirmTitle: "",
    fnStatus: ""
})

const queryList = async () => {
    try {
        const params = {
            classesName: state.classesName
        }
        const { data } = await http.post("/app/after/school/userBoard", params)
        const arr = [...(data.notAfterSchoolList || []), ...(data.underwayAfterSchoolList || []), ...(data.afterSchoolList || [])]
        paging.value.setLocalPaging(arr)
    } catch (err) {
        paging.value.complete(false)
    }
}

const classesIds = computed(() => state.dataList.filter((item) => item.checked).map((item) => item.classesId))
const hidden = computed(() => {
    const notAfterSchoolList = state.dataList.filter((item) => item.status == 0)
    return notAfterSchoolList.length != 0
})

const handleClick = (item) => {
    item.checked = !item.checked
}

const submit = async () => {
    if (classesIds.value.length == 0) {
        return uni.showToast({
            title: "请先选择班级",
            icon: "none"
        })
    }
    state.confirmTitle = "确认一键放学已选班级吗？"
    state.fnStatus = "after"
    confirmRef.value.open()
}

const after = async () => {
    state.loading = true
    try {
        const { message } = await http.post("/app/after/school/afterSchool", { classesIds: classesIds.value })
        uni.showToast({
            title: message,
            icon: "none"
        })
        state.loading = false
        paging.value.reload()
    } catch (error) {
        state.loading = false
    }
}

const immediately = async (id) => {
    const { message } = await http.post("/app/after/school/afterSchool", { classesIds: [id] })
    uni.showToast({
        title: message,
        icon: "none"
    })
    paging.value.reload()
}

const update = async (id) => {
    const { message } = await http.post("/app/after/school/update", { id })
    uni.showToast({
        title: message,
        icon: "none"
    })
    paging.value.reload()
}

const Fn = {
    after: after,
    immediately: immediately,
    update: update
}

const handleItem = (item) => {
    state.classID = item.status == 0 ? item.classesId : item.afterSchoolLogId
    state.confirmTitle = item.status == 0 ? "确认立即放学吗？" : "确认将已选班级归到未放学列表吗？"
    state.fnStatus = item.status == 0 ? "immediately" : "update"
    confirmRef.value.open()
}

const dialogConfirm = () => {
    state.fnStatus == "after" ? Fn[state.fnStatus]() : Fn[state.fnStatus](state.classID)
}

const search = () => {
    paging.value.reload()
}
</script>
<style lang="scss" scoped>
.header_box {
    min-height: 355rpx;
    background: linear-gradient(130deg, #fafbd9 0%, #d0f5d4 100%);
    box-sizing: border-box;
    padding: 34rpx 30rpx;
    position: relative;
    overflow: hidden;
    .bg_container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        .bg {
            position: absolute;
            width: 310rpx;
            height: 195rpx;
            top: 34rpx;
            right: 56rpx;
        }
    }
    .content {
        position: relative;
        .top_title {
            font-size: 36rpx;
            color: #000;
            font-weight: 600;
        }
        .mian_title {
            display: flex;
            margin-top: 22rpx;
            margin-bottom: 34rpx;
            .box {
                padding-left: 20rpx;
                font-size: 26rpx;
                color: #333;
                width: 172rpx;
                height: 48rpx;
                line-height: 48rpx;
                border-radius: 24rpx;
                box-sizing: border-box;
                margin-right: 16rpx;
                .icon {
                    width: 32rpx;
                    height: 32rpx;
                    vertical-align: text-top;
                }
            }
        }
        .input_box {
            display: flex;
            align-items: center;
            background: #fff;
            border-radius: 40rpx;
            box-shadow: 0rpx 0rpx 12rpx 0rpx #ceeecd;
            padding: 5rpx;
            .input {
                flex: 1;
                :deep(.uni-searchbar) {
                    padding: 0;
                }
                :deep(.uni-searchbar__box) {
                    justify-content: flex-start;
                }
            }
            .search_icon {
                width: 120rpx;
                height: 72rpx;
                border-radius: 40rpx;
                background: #11c685;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
    .modal {
        height: 60rpx;
        width: 100%;
        background: #fff;
        border-radius: 40rpx 40rpx 0rpx 0rpx;
        position: absolute;
        bottom: 0;
        left: 0;
    }
}
.main_box {
    background: #fff;
    padding: 0 30rpx 30rpx 30rpx;
    .item_box {
        display: flex;
        align-items: center;
        overflow: hidden;
        margin-bottom: 30rpx;
        .left_box {
            width: 52rpx;
            margin-right: 10rpx;
            .empty_circle {
                width: 36rpx;
                height: 36rpx;
                border: 2rpx solid #cfcfcf;
                border-radius: 50%;
                margin: 0 auto;
            }
        }
        .right_box {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            border: 2rpx solid #d9d9d9;
            border-radius: 28rpx;
            padding: 26rpx 24rpx;
            overflow: hidden;
            .status_box {
                width: 76rpx;
                height: 36rpx;
                text-align: center;
                line-height: 36rpx;
                font-size: 20rpx;
                border-radius: 8rpx;
                margin-right: 16rpx;
            }
            .class_box {
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            .btn {
                width: 132rpx;
                height: 48rpx;
                color: #fff;
                font-size: 24rpx;
                border-radius: 26rpx;
                text-align: center;
                line-height: 48rpx;
            }
        }
    }
}
.bottom_box {
    padding: 30rpx;
    padding-bottom: 40rpx;
    .btm_btn {
        font-size: 32rpx;
        color: #fff;
        background: #11c685;
        &:after {
            border: none;
        }
    }
}
.empty_box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #ccc;
    background: #fff;
    width: 100%;
    .img {
        width: 540rpx;
        height: 400rpx;
    }
}
</style>

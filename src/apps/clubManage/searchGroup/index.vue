<template>
    <z-paging ref="paging" class="container_box" v-model="dataList" @query="queryList" :auto="false">
        <template #top>
            <view class="header">
                <uni-nav-bar :border="false" statusBar fixed>
                    <template #left>
                        <view class="left">
                            <uni-icons type="left" size="22" color="#333333" @click="back"></uni-icons>
                        </view>
                    </template>
                    <view class="search_box">
                        <uni-easyinput v-model="query.name" trim="all" focus prefixIcon="search" placeholder="搜索社团" primaryColor="#c0c4cc" @clear="handleClearSearch" @confirm="handleSearch"></uni-easyinput>
                    </view>
                    <template #right><view class="right" @click="handleSearch">搜索</view></template>
                </uni-nav-bar>
            </view>
        </template>
        <view class="main">
            <view class="item_box" v-for="(item, idx) in dataList" @click="handleClick(item)" :key="idx">
                <img class="img" :src="item.iconUrl" />
                <view class="flag" :style="{ backgroundColor: item.level === 'department' ? '#00B781' : '#FF992B' }">
                    {{ item.level === "department" ? "院" : "校" }}
                </view>
                <view class="r_box">
                    <view class="t">{{ item.name }}</view>
                    <view class="btm">{{ item.memberCount }}人</view>
                </view>
            </view>
        </view>
        <template #empty>
            <view class="empty_box">
                <img class="empty_img" src="@nginx/workbench/groupManage/empty.png" alt="" />
                <view>暂无数据</view>
            </view>
        </template>
    </z-paging>
</template>
<script setup>
import { debounce } from "@/utils"
const query = reactive({
    name: ""
})
const dataList = ref([])
const paging = ref(null)

const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        name: query.name
    }
    http.post("/app/club/club/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

const verifySearchStr = (str) => {
    const v = ["", undefined].includes(str)
    if (v) return false
    return str.replace(/^\s+|\s+$/g, "") != ""
}

const handleSearch = debounce(() => {
    if (!verifySearchStr(query.name)) {
        return uni.showToast({
            icon: "none",
            title: "请输入社团名称"
        })
    }
    uni.showLoading({
        title: "Loading..."
    })
    paging.value.reload()
    uni.hideLoading()
}, 400)

const handleClick = (item) => {
    navigateTo({
        url: "/apps/clubManage/groupDetail/index",
        query: {
            id: item.id
        }
    })
}
const back = () => {
    uni.navigateBack()
}

const handleClearSearch = () => {
    paging.value.reload()
}
</script>
<style lang="scss" scoped>
.container_box {
    .header {
        background-color: $uni-text-color-inverse;
        margin-bottom: 20rpx;
        .search_box {
            display: flex;
            align-items: center;
            :deep(.is-input-border) {
                border: none;
                background-color: #f0f2f5 !important;
                border-radius: 20px;
            }
        }
        .right {
            color: $uni-color-primary;
        }
    }
    .main {
        padding: 20rpx 30rpx;
        background-color: $uni-text-color-inverse;
        .item_box {
            width: 100%;
            display: flex;
            padding: 20rpx 0;
            .img {
                width: 88rpx;
                height: 88rpx;
                border-radius: 12rpx;
                overflow: hidden;
                margin-right: 16rpx;
            }
            .flag {
                font-size: 16rpx;
                color: $uni-text-color-inverse;
                position: absolute;
                width: 24rpx;
                height: 24rpx;
                text-align: center;
                border-radius: 0.375rem 0 0.375rem 0;
            }
            .r_box {
                display: flex;
                flex-direction: column;
                justify-content: space-around;
                flex: 1;
                overflow: hidden;
                .t {
                    font-size: 32rpx;
                    color: #262626;
                    flex: 1;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    font-weight: 600;
                }
                .btm {
                    font-size: 24rpx;
                    color: #999999;
                }
            }
        }
        .empty_box {
            width: 360rpx;
            height: 400rpx;
            color: #8c8c8c;
            font-size: 28rpx;
            text-align: center;
            margin: auto;
            padding-top: 40%;
            .empty_img {
                width: 360rpx;
            }
        }
    }
}
</style>

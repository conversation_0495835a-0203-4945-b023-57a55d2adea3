<template>
    <z-paging ref="paging" class="container_box">
        <template #top>
            <uni-nav-bar :border="false" statusBar fixed backgroundColor="transparent" left-icon="left" :title="state.isEdit ? '我的社团' : '创建社团'" @clickLeft="back" />
        </template>
        <view class="main_box">
            <uni-forms ref="valiForm" :rules="rules" :modelValue="state.formData" label-width="180rpx">
                <view class="textarea_box">
                    <uni-forms-item label="社团名称" required name="name">
                        <uni-easyinput v-model="state.formData.name" placeholder="请输入" :inputBorder="false" type="textarea" maxlength="40" />
                        <view class="textarea_count">{{ state.formData.name.length }}/40</view>
                    </uni-forms-item>
                </view>
                <uni-forms-item label="社团封面" required name="iconUrl">
                    <view class="right_box" @click="handleUpload">
                        <text class="text">{{ state.formData.iconUrl ? "" : "请上传" }}</text>
                        <img class="img" v-if="state.formData.iconUrl" :src="state.formData.iconUrl" />
                        <img class="img" v-else src="@nginx/workbench/groupManage/uploadBg.png" />
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="社团分类" required name="categoryId">
                    <view class="right_box" @click="handleSelect('categoryId')">
                        <text class="text">{{ state.info.categoryId || "请选择" }}</text>
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="社团等级" required name="level">
                    <view class="right_box" @click="handleSelect('level')">
                        <text class="text">{{ state.info.level || "请选择" }}</text>
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="社团规模" required name="size" :style="{ 'pointer-events': state.isEdit ? 'none' : 'all' }">
                    <view class="right_box" @click="handleSelect('size')">
                        <text class="text">{{ state.info.size || "请选择" }}</text>
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="社团负责人" required name="personLiable">
                    <view class="right_box" style="background: #f6f6f6">
                        <text class="text">{{ state.info.personLiable || "请选择" }}</text>
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <uni-forms-item label="指导老师" required name="advisorSelection" :style="{ 'pointer-events': state.isEdit ? 'none' : 'all' }">
                    <view class="right_box" @click="handleTeacher">
                        <text class="text">{{ state.info.advisorSelection || "请选择" }}</text>
                        <uni-icons type="right" size="20" color="#333"></uni-icons>
                    </view>
                </uni-forms-item>
                <view class="textarea_box">
                    <uni-forms-item label="社团口号" required name="slogan">
                        <uni-easyinput v-model="state.formData.slogan" :inputBorder="false" placeholder="请输入" type="textarea" maxlength="100" />
                        <view class="textarea_count">{{ state.formData.slogan.length }}/100</view>
                    </uni-forms-item>
                </view>
                <view class="textarea_box">
                    <uni-forms-item label="社团简介" required name="description">
                        <uni-easyinput v-model="state.formData.description" :inputBorder="false" placeholder="请输入" type="textarea" maxlength="5000" />
                        <view class="textarea_count">{{ state.formData.description.length }}/5000</view>
                    </uni-forms-item>
                </view>
            </uni-forms>
        </view>
        <template #bottom>
            <view class="footer">
                <button class="btn" v-if="state.isEdit" @click="handleEdit">完成</button>
                <button class="btn" v-else @click="handleEdit">提交申请</button>
            </view>
        </template>

        <uv-picker ref="selPopClassify" :columns="state.classifyList" keyName="name" cancelColor="#00b781" confirmColor="#00b781" @confirm="(val) => closePopup(val, 'categoryId')" :closeOnClickOverlay="false"></uv-picker>
        <uv-picker ref="selPopGrade" :columns="state.gradeList" keyName="name" cancelColor="#00b781" confirmColor="#00b781" @confirm="(val) => closePopup(val, 'level')" :closeOnClickOverlay="false"></uv-picker>
        <uv-picker ref="selPopScale" :columns="state.scaleList" keyName="name" cancelColor="#00b781" confirmColor="#00b781" @confirm="(val) => closePopup(val, 'size')" :closeOnClickOverlay="false"></uv-picker>
    </z-paging>
</template>
<script setup>
import { debounce } from "@/utils"
const state = reactive({
    isAdd: true,
    formData: {
        name: "",
        slogan: "",
        description: ""
    },
    info: {},
    classifyList: [],
    gradeList: [],
    scaleList: [],
    isEdit: false
})

onLoad((options) => {
    options.isEdit = Boolean(options.isEdit)
    Object.assign(state.formData, options)
    if (options.isEdit) {
        state.isEdit = true
        getDetail(options.oldId)
    }
})

// 获取社团详情
function getDetail(id) {
    http.post("/app/club/club/get", { id }).then((res) => {
        Object.assign(state.formData, res.data)
        // 数据反显
        state.info.size = res.data.sizeName
        state.info.level = res.data.levelName
        state.info.categoryId = res.data.categoryName
        state.info.advisorSelection = JSON.parse(res.data.advisorSelection)
            .map((i) => i.name)
            .join("、")
    })
}

const handleTeacher = () => {
    navigateTo({
        url: "/apps/clubManage/selectMember/index",
        events: {
            selectMember: (data) => {
                console.log("data:", data)
                state.info.advisorSelection = data.treeSubmitListName
                state.formData.advisorSelection = JSON.stringify(data.treeSubmitList)
            }
        }
    })
}

const handleUpload = debounce(() => {
    uni.chooseImage({
        count: 1,
        success: (res) => {
            http.uploadFile("/file/common/upload", res.tempFiles[0].path, { folderType: "app" }).then((res) => {
                state.formData.iconUrl = res
            })
        }
    })
}, 1000)

const selPopClassify = ref(null)
const selPopGrade = ref(null)
const selPopScale = ref(null)

const sel = {
    categoryId: selPopClassify,
    level: selPopGrade,
    size: selPopScale
}
const handleSelect = (val) => {
    sel[val].value.open()
}

const closePopup = (val, flag) => {
    state.info[flag] = val.value[0].name
    state.formData[flag] = val.value[0].id
}

const valiForm = ref(null)
const handleEdit = () => {
    valiForm.value.validate().then((res) => {
        const params = {
            ...state.formData
        }

        const url = state.isEdit ? "/app/club/club/update" : "/app/club/club-application/create"
        http.post(url, params).then((res) => {
            uni.showToast({
                title: state.isEdit ? "编辑成功" : "申请成功",
                icon: "none"
            })
            if (state.isEdit) {
                navigateTo({
                    query: {
                        id: state.formData.oldId
                    },
                    url: "/apps/clubManage/groupDetail/index"
                })
            } else {
                navigateTo({
                    url: "/apps/clubManage/myGroup/index"
                })
            }
        })
    })
}

const back = () => {
    uni.navigateBack()
}

const rules = ref({
    name: {
        rules: [
            {
                required: true,
                errorMessage: "请输入社团名称"
            }
        ]
    },
    iconUrl: {
        rules: [
            {
                required: true,
                errorMessage: "请上传社团封面"
            }
        ]
    },
    categoryId: {
        rules: [
            {
                required: true,
                errorMessage: "请选择社团分类"
            }
        ]
    },
    level: {
        rules: [
            {
                required: true,
                errorMessage: "请选择社团等级"
            }
        ]
    },
    size: {
        rules: [
            {
                required: true,
                errorMessage: "请选择社团规模"
            }
        ]
    },
    personLiable: {
        rules: [
            {
                required: true,
                errorMessage: "请选择社团负责人"
            }
        ]
    },
    advisorSelection: {
        rules: [
            {
                required: true,
                errorMessage: "请选择社团老师"
            }
        ]
    },
    slogan: {
        rules: [
            {
                required: true,
                errorMessage: "请输入社团口号"
            }
        ]
    },
    description: {
        rules: [
            {
                required: true,
                errorMessage: "请输入社团简介"
            }
        ]
    }
})

onMounted(() => {
    // 社团规模列表
    http.post("/app/club/club-size/list").then((res) => {
        state.scaleList = [
            res.data.map((i) => ({
                name: i.name,
                id: i.code
            }))
        ]
    })
    // 社团等级列表
    http.post("/app/club/club-level/list").then((res) => {
        state.gradeList = [
            res.data.map((i) => ({
                name: i.name,
                id: i.code
            }))
        ]
    })
    // 社团分类列表
    http.post("/app/club/club-category/list").then((res) => {
        state.classifyList = [
            res.data.map((i) => ({
                name: i.name,
                id: i.id
            }))
        ]
    })

    // 获取学生登录信息   目前通过接口获取 负责人不可修改
    http.post("/app/profile/student/me").then((res) => {
        state.info.personLiable = res.data.name
        state.formData.personLiable = res.data.userId
    })
})
</script>

<style lang="scss" scoped>
.container_box {
    .header {
        :deep(.uni-navbar__content) {
            border: none;
            .uni-navbar__header {
                background: transparent !important;
                .uni-nav-bar-text {
                    font-weight: 600;
                }
            }
        }
    }
    .main_box {
        margin: 20rpx 0;
        padding: 30rpx;
        background-color: $uni-text-color-inverse;
        overflow-x: auto;
        .pointer_none {
            pointer-events: none !important;
        }
        .right_box {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            color: #999;
            .text {
                margin-right: 30rpx;
                font-size: 24rpx;
            }
        }
        :deep(.uni-forms-item) {
            min-height: 120rpx;
            align-items: center;
            margin-bottom: 0;
            border-bottom: 2rpx solid #d8d8d8;
            .uni-forms-item__error {
                padding-top: 0;
                top: 90%;
            }
            .uni-easyinput__content {
                text-align: right;
            }
        }
        .textarea_box {
            display: block;
            position: relative;
            &:last-of-type {
                border: none;
            }
            :deep(.uni-forms-item) {
                padding-bottom: 44rpx;
                padding-top: 44rpx;
            }
            :deep(.uni-easyinput__content) {
                padding: 20rpx 0 40rpx 20rpx;
                background-color: #f6f6f6 !important;
                border-radius: 10rpx;
                text-align: left;
            }
            :deep(.uni-forms-item__error) {
                padding-top: 8rpx;
                top: 100%;
            }
            .textarea_count {
                position: absolute;
                display: inline-block;
                color: #999;
                font-size: 28rpx;
                right: 20rpx;
                bottom: 10rpx;
            }
        }
        .img {
            width: 72rpx;
            height: 72rpx;
            background: #d3d3d3;
            border-radius: 4px;
        }
    }
    .footer {
        width: 100%;
        padding: 0 30rpx;
        background: #fff;
        font-size: 32rpx;
        display: flex;
        justify-content: center;
        box-sizing: border-box;
        .btn {
            color: $uni-text-color-inverse;
            height: 92rpx;
            width: 100%;
            line-height: 92rpx;
            border-radius: 10rpx;
            background: $uni-color-primary;
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            font-size: 32rpx;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .btnR {
            color: $uni-text-color-inverse;
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            flex: 1;
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            margin-left: 30rpx;
            font-size: 32rpx;
            &:after {
                border: none;
            }
        }
    }
    .pop_box {
        position: relative;
        .close {
            position: absolute;
            top: 30rpx;
            right: 30rpx;
        }
        .header_box {
            text-align: center;
            font-size: 32rpx;
            color: #262626;
            padding: 32rpx 0;
        }
        .pop_main {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40rpx 0 88rpx 0;
            .popup_content {
                width: 88rpx;
                height: 88rpx;
                background-color: #f75251;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                .img {
                    width: 48rpx;
                    height: 48rpx;
                }
            }
        }
    }
}
</style>

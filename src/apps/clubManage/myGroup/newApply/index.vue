<template>
    <z-paging ref="paging" v-model="dataList" @query="queryList">
        <view class="container_box">
            <view class="item_box" v-for="item in dataList">
                <view class="role">{{ item.userRealName.slice(0, 1) }}</view>
                <view class="right">
                    <view class="item_top_box">
                        <view class="l">
                            <view class="text_top"> {{ item.userRealName }} </view>
                            <view class="text_btm">申请社团: {{ item.clubName }}</view>
                        </view>
                        <view class="r">
                            <button class="btn_cls" v-if="item.status === 'pending'" @click="handleApply(item)">入团审核</button>
                            <text v-else :style="{ color: statusObj[item.status]?.color, fontSize: '28rpx' }">{{ statusObj[item.status]?.text }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <template #empty>
            <view class="empty_box">
                <img class="empty_img" src="@nginx/workbench/groupManage/empty.png" alt="" />
                <view>暂无数据</view>
            </view>
        </template>
        <uni-popup ref="alertDialog" type="dialog">
            <uni-popup-dialog class="dialog_box" type="info" cancelText="拒绝" confirmText="同意" content="是否同意该同学加入社团?" @confirm="dialogConfirm" @close="dialogClose"> </uni-popup-dialog>
        </uni-popup>
    </z-paging>
</template>
<script setup>
const statusObj = {
    approved: {
        color: "#00B781",
        text: "已同意"
    },
    2: {
        color: "#999999",
        text: "已过期"
    },
    rejected: {
        color: "#F5222D",
        text: "已拒绝"
    }
}
const dataList = ref([])

let stash = {}
const alertDialog = ref(null)
const handleApply = (item) => {
    stash = item
    alertDialog.value.open()
}
const paging = ref(null)
const queryList = (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize
    }
    http.post("/app/club/join-request/page", params)
        .then((res) => {
            paging.value.complete(res.data.list)
        })
        .catch((err) => {
            paging.value.complete(false)
        })
}

// 弹窗确认
const dialogConfirm = () => {
    http.post("/app/club/join-request/approve", { id: stash.id }).then((res) => {
        uni.showToast({
            title: "同意成功",
            icon: "none"
        })
        paging.value.reload()
        alertDialog.value.close()
    })
}

// 拒绝
const dialogClose = () => {
    http.post("/app/club/join-request/reject", { id: stash.id }).then((res) => {
        uni.showToast({
            title: "拒绝成功",
            icon: "none"
        })
        paging.value.reload()
        alertDialog.value.close()
    })
}
</script>
<style lang="scss" scoped>
.container_box {
    margin: 20rpx 0;
    padding: 0 30rpx;
    background-color: $uni-text-color-inverse;
    .item_box {
        display: flex;
        padding: 30rpx 0;
        border-bottom: 2rpx solid #d9d9d9;
        .role {
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
            margin-right: 24rpx;
            background-color: $uni-color-primary;
            font-size: 34rpx;
            color: $uni-text-color-inverse;
            line-height: 96rpx;
            text-align: center;
        }
        .right {
            flex: 1;
            overflow: hidden;
            .item_top_box {
                display: flex;
                align-items: center;
                height: 100rpx;
                .l {
                    flex: 1;
                    overflow: hidden;
                    .text_top {
                        font-size: 32rpx;
                        color: #262626;
                        font-weight: 600;
                        margin-bottom: 8rpx;
                        overflow: hidden;
                    }
                    .text_btm {
                        font-size: 24rpx;
                        color: #999;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                    }
                }
                .r {
                    .btn_cls {
                        font-size: 28rpx;
                        width: 132rpx;
                        height: 60rpx;
                        line-height: 60rpx;
                        padding: 0;
                        text-align: center;
                        background-color: $uni-color-primary;
                        color: $uni-text-color-inverse;
                        &:after {
                            border-color: $uni-color-primary;
                        }
                    }
                }
            }
        }
    }
    .dialog_box {
        :deep(.uni-dialog-title) {
            display: none;
        }
        :deep(.uni-dialog-content) {
            padding-top: 88rpx;
            padding-bottom: 88rpx;
        }
        :deep(.uni-dialog-button-text) {
            color: #f5222d !important;
        }
        :deep(.uni-button-color) {
            color: $uni-color-primary !important;
        }
    }
    .empty_box {
        width: 360rpx;
        height: 400rpx;
        color: #8c8c8c;
        font-size: 28rpx;
        text-align: center;
        margin: auto;
        padding-top: 40%;
        .empty_img {
            width: 360rpx;
        }
    }
}
</style>

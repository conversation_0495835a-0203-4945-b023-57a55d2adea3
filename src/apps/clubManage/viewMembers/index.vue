<template>
    <z-paging ref="paging" class="container_box">
        <view class="main">
            <uni-list :border="false">
                <uni-list-item v-for="(item, index) in state.list" @click="handleClick(item)" :clickable="state.memberRole !== 'member' && state.memberRole !== 'none'" :key="index">
                    <template v-slot:body>
                        <view class="item_box">
                            <view class="left">
                                <view v-if="state.memberRole !== 'member' && state.memberRole !== 'none'">
                                    <img class="img" v-if="item.isChecked" src="@nginx/workbench/groupManage/checkmarkempty.png" />
                                    <view class="sign" :disabled="item.role === 'admin'" v-else></view>
                                </view>
                                <view class="avator">{{ item.name.slice(0, 1) }}</view>
                                <view>{{ item.name }}</view>
                                <view
                                    v-if="item.role !== 'member'"
                                    class="flag"
                                    :style="{
                                        color: item.role === 'owner' ? '#F48F03' : '#00B781',
                                        background: item.role === 'owner' ? '#FFEFD8' : '#CBFFF0'
                                    }"
                                >
                                    {{ item.role === "owner" ? "负责人" : "管理员" }}
                                </view>
                            </view>
                            <button class="btn" v-if="item.role === 'admin' && state.memberRole === 'owner'" @click.stop="handleEdit('cancel', item)">取消管理员</button>
                        </view>
                    </template>
                </uni-list-item>
            </uni-list>
        </view>
        <template #bottom v-if="state.memberRole !== 'member' && state.memberRole !== 'none'">
            <view class="footer">
                <button class="btn" v-if="state.memberRole !== 'admin'" style="width: 200rpx; color: #f48f03; border-color: #f48f03; background: #fff" @click="handleEdit('transfer')">转让</button>
                <button class="btn" style="color: #f5222d; border-color: #f5222d; background: #fff; margin-left: 25rpx; margin-right: 0" @click="handleEdit('remove')">移除成员</button>
                <button :class="['btn', adminTotal >= 3 || selectGroup.length + adminTotal > 3 ? 'disabled' : '']" v-if="state.memberRole !== 'admin'" @click="handleEdit('manage')">设为管理员</button>
            </view>
        </template>
    </z-paging>
    <uni-popup ref="alertDialog" type="dialog">
        <uni-popup-dialog class="dialog_box" type="info" cancelText="取消" confirmText="确认" :content="state.content" @confirm="dialogConfirm" @close="dialogClose"> </uni-popup-dialog>
    </uni-popup>
</template>
<script setup>
const state = reactive({
    clubId: "",
    list: [],
    memberRole: "",
    flag: ""
})

onLoad((options) => {
    state.clubId = options.clubId
    state.memberRole = options.memberRole
    getMemberList(state.clubId)
})

// 选中项
const selectGroup = computed(() => {
    return state.list.filter((item) => item.isChecked)
})

// 当前有多少个管理员
const adminTotal = computed(() => {
    const total = state.list.filter((item) => item.role === "admin").length
    return total
})

function getMemberList(clubId) {
    http.post("/app/club/member/list", { clubId }).then((res) => {
        state.list = res.data
    })
}

const handleClick = (item) => {
    if (item.role === "admin") {
        return
    }

    item.isChecked = !item.isChecked
}

let stash = {}
// 提交校验 提示 提交事件
const typeObj = {
    cancel: {
        ruleFn: () => true,
        submitFn: () => {
            const params = {
                clubId: state.clubId,
                userIds: [stash.userId]
            }
            http.post("/app/club/member/unassign-admin", params).then((res) => {
                uni.showToast({
                    title: "取消管理员成功",
                    icon: "none"
                })
                dialogClose(true)
            })
        },
        content: "确定取消管理员吗？"
    },
    transfer: {
        ruleFn: () => {
            if (selectGroup.value.length > 1) {
                uni.showToast({
                    title: "最多只能选择一个成员转让",
                    icon: "none"
                })
                return false
            } else if (selectGroup.value.length === 1 && selectGroup.value[0].role === "owner") {
                uni.showToast({
                    title: "负责人不能转让给自己",
                    icon: "none"
                })
                return false
            }
            return true
        },
        submitFn: () => {
            const params = {
                id: state.clubId,
                newOwnerId: selectGroup.value[0].userId
            }
            http.post("/app/club/club/transfer", params).then((res) => {
                uni.showToast({
                    title: "转让社团成功",
                    icon: "none"
                })
                dialogClose()
                navigateTo({ url: "/groupManage/myGroup/index" })
            })
        },
        content: "确定将社团的权限转让吗？"
    },
    remove: {
        ruleFn: () => {
            const owner = selectGroup.value.find((i) => i.role === "owner")
            if (owner) {
                uni.showToast({
                    title: "不能移除负责人",
                    icon: "none"
                })
                return false
            } else if (state.memberRole === "admin") {
                const admin = selectGroup.value.find((i) => i.role === "admin")
                if (admin) {
                    uni.showToast({
                        title: "不能移除管理员",
                        icon: "none"
                    })
                    return false
                }
            }
            return true
        },
        submitFn: () => {
            const params = {
                clubId: state.clubId,
                userIds: selectGroup.value.map((i) => i.userId)
            }
            http.post("/app/club/member/remove", params).then((res) => {
                uni.showToast({
                    title: "移除成员成功",
                    icon: "none"
                })
                dialogClose(true)
            })
        },
        content: "确定将选中的成员移除吗？"
    },
    manage: {
        ruleFn: () => {
            const owner = selectGroup.value.find((i) => i.role === "owner")
            if (owner) {
                uni.showToast({
                    title: "负责人不能设为管理员",
                    icon: "none"
                })
                return false
            }
            return true
        },
        submitFn: () => {
            const params = {
                clubId: state.clubId,
                userIds: selectGroup.value.map((i) => i.userId)
            }
            http.post("/app/club/member/assign-admin", params).then((res) => {
                uni.showToast({
                    title: "设置管理员成功",
                    icon: "none"
                })
                dialogClose(true)
            })
        },
        content: "确定将选中的成员设为管理员？"
    }
}

const alertDialog = ref(null)

const handleEdit = (type, item) => {
    state.flag = type
    if (type === "cancel") {
        state.content = typeObj[type].content
        stash = item
        alertDialog.value.open()
    } else {
        if (selectGroup.value.length === 0) return
        const flag = typeObj[type].ruleFn()
        if (!flag) return
        state.content = typeObj[type].content
        alertDialog.value.open()
    }
}

// 弹窗确认
const dialogConfirm = () => {
    typeObj[state.flag].submitFn()
}

// 弹窗关闭
function dialogClose(refresh = false) {
    alertDialog.value.close()
    // 刷新数据
    if (refresh) {
        getMemberList(state.clubId)
    }
}
</script>
<style lang="scss" scoped>
.container_box {
    padding-top: 20rpx;
    .main {
        padding: 0 28rpx;
        background-color: $uni-text-color-inverse;
        padding-bottom: 200rpx;
        min-height: calc(100vh - 310rpx);
        :deep(.uni-list-item__container) {
            padding: 0;
        }
        .item_box {
            display: flex;
            flex: 1;
            height: 140rpx;
            box-sizing: border-box;
            justify-content: space-between;
            align-items: center;
            .left {
                flex: 1;
                display: flex;
                font-size: 28rpx;
                color: #333;
                align-items: center;
                .sign {
                    width: 36rpx;
                    height: 36rpx;
                    box-sizing: border-box;
                    border: 3rpx solid #999;
                    background: $uni-bg-color;
                    border-radius: 50%;
                }
                .sign[disabled="true"] {
                    background: #eee;
                }
                .img {
                    width: 36rpx;
                    height: 36rpx;
                    border-radius: 50%;
                }
                .avator {
                    width: 60rpx;
                    height: 60rpx;
                    background-color: $uni-color-primary;
                    text-align: center;
                    line-height: 60rpx;
                    color: $uni-text-color-inverse;
                    border-radius: 50%;
                    margin: 0 16rpx 0 10rpx;
                }
                .flag {
                    width: 72rpx;
                    height: 32rpx;
                    font-size: 20rpx;
                    line-height: 32rpx;
                    text-align: center;
                    margin-left: 8rpx;
                    border-radius: 4rpx;
                }
            }
            .btn {
                width: 150rpx;
                height: 60rpx;
                color: $uni-color-primary;
                background-color: $uni-text-color-inverse;
                font-size: 24rpx;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                white-space: nowrap;
                &:after {
                    border-color: $uni-color-primary;
                }
            }
        }
    }
    .footer {
        width: 100%;
        padding: 0 30rpx;
        background: $uni-bg-color;
        font-size: 32rpx;
        display: flex;
        box-sizing: border-box;
        justify-content: flex-end;
        .btn {
            height: 92rpx;
            line-height: 92rpx;
            border-radius: 10rpx;
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            width: 220rpx;
            margin-left: 0;
            margin-top: 30rpx;
            margin-bottom: 66rpx;
            font-size: 32rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 24rpx;
            border: 2rpx solid transparent;
            white-space: nowrap;
            &:first-child {
                margin-left: 0;
            }
            &:after {
                border: none;
            }
        }
        .disabled {
            background: #ccc !important;
            pointer-events: none;
        }
    }
    .dialog_box {
        :deep(.uni-dialog-title) {
            display: none;
        }
        :deep(.uni-dialog-content) {
            padding-top: 88rpx;
            padding-bottom: 88rpx;
        }
        :deep(.uni-dialog-button-text) {
            color: $uni-color-primary !important;
        }
    }
}
</style>

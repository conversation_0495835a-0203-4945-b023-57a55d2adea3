<!-- 发布范围选择器 -->
<template>
    <view class="rangeBox">
        <!-- 头部 -->
        <uni-nav-bar statusBar fixed :border="false" @clickRight="outputPeople" @clickLeft="clickLeft" left-icon="left" title="发布范围">
            <!-- #ifdef H5 || H5-WEIXIN-->
            <template #right>
                <view class="saveBtn">确定</view>
            </template>
            <!-- #endif -->
            <!-- #ifdef APP-PLUS -->
            <template #right>
                <view class="saveBtn">确定</view>
            </template>
            <!-- #endif -->
        </uni-nav-bar>
        <!-- 选择的地方 -->
        <view class="treeBox">
            <view class="treeHeadBox">
                <view class="treeHeadBox_itemBox" @click="clickheadArr(item)" v-for="item in state.headArr" :key="item.id">
                    <view :class="{ activeColor: state.selectTap === item.id }" class="treeHeadBox_itemBox_name">{{ item.name }}</view>
                    <view v-if="state.selectTap === item.id" class="activeLine"> </view>
                </view>
            </view>

            <!-- 面包屑 可以滑动 -->
            <view class="breadcrumbBox">
                <view class="breadcrumb">
                    <view class="breadcrumb-item" v-for="(item, index) in state.bread" :key="item.id" @click="selectBread(item, index)">
                        <view class="breadcrumb-item-slot">{{ item.name }}</view>
                        <text class="breadcrumb-item-separator">></text>
                    </view>
                </view>
            </view>

            <!-- 接口里获取到的数据列表 -->
            <scroll-view class="scroll-Y" :scroll-y="true" :scrollTop="state.scrollTop" :show-scrollbar="false" :lower-threshold="50" :scroll-with-animation="true">
                <view class="peopleList">
                    <!-- 全选 -->
                    <view @click="allSelectPeople" class="allSelectBox" v-if="state.reqListArr && state.reqListArr.length && state.reqListArr[0]?.pid != 0">
                        <view class="allSelect">
                            <view class="selectImgBox">
                                <image v-show="state.selectPeople" class="selectImg" src="@nginx/workbench/punchTheClock/selectYes.png"></image>
                                <image v-show="!state.selectPeople" class="selectImg" src="@nginx/workbench/punchTheClock/selectNo.png"></image>
                            </view>
                            <view class="selectName">全选</view>
                        </view>
                    </view>

                    <view class="reqListBox" v-if="state.reqListArr && state.reqListArr.length">
                        <view class="itemSelect" v-for="item in state.reqListArr" :key="item.id">
                            <view class="itemSelect_left" @click="selectItemThis(item)">
                                <view class="selectImgBox" v-if="item.pid != 0">
                                    <image v-if="isSelected(item)" class="selectImg" src="@nginx/workbench/punchTheClock/selectYes.png"></image>
                                    <image v-if="!isSelected(item)" class="selectImg" src="@nginx/workbench/punchTheClock/selectNo.png"></image>
                                </view>
                                <view class="avatarBox" v-if="item.typeValue === 'people_dept' || item.typeValue === 'student'">
                                    <uv-avatar :text="item.name?.charAt(0)" size="30" fontSize="14" bg-color="#00B781"></uv-avatar>
                                </view>
                                <view class="selectName">{{ item.name }}</view>
                            </view>
                            <view class="itemSelect_right" v-if="item.isSub" @click="nextLevel(item)">
                                <view class="nextImgBox">
                                    <image class="nextImg" src="@nginx/workbench/punchTheClock/nextIcon.png"></image>
                                </view>
                                <view class="nextName" :style="[isSelected(item) ? { color: '#999999' } : { color: '#00B781' }]">下级</view>
                            </view>
                        </view>
                    </view>
                    <yd-empty :isMargin="true" text="暂无数据" v-if="!state.reqListArr.length" />
                </view>
            </scroll-view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <div class="saveTreeBox">
            <button class="saveTree" @click="outputPeople">确认</button>
        </div>
        <!-- #endif -->
    </view>
</template>

<script setup>
import { selectMember } from "./treeSubmitList.js"

const state = ref({
    treeType: 1,
    businessType: 10,
    searchreqListArr: [],
    reqListArr: [],
    selected: [],
    bread: [],
    selectTap: 2,
    headArr: [
        { id: 2, name: "班级", treeType: 1, businessType: 10 },
        { id: 1, name: "部门", treeType: 2, businessType: 20 }
    ]
})

const clickheadArr = (item) => {
    state.value.bread = []
    state.value.selectTap = item.id
    state.value.treeType = item.treeType
    state.value.businessType = item.businessType
    getSelectListTwo()
}

function areAllIdsInArrayA(arrayA, arrayB) {
    // 提取数组A中所有对象的id到一个Set中
    const idsInA = new Set(arrayA.map((obj) => obj.id))

    // 检查数组B中每个对象的id是否都存在于idsInA中
    for (let obj of arrayB) {
        if (!idsInA.has(obj.id)) {
            // 如果发现任何一个对象的id不在idsInA中，则返回false
            return false
        }
    }
    // 如果所有对象的id都在idsInA中，则返回true
    return true
}

// 获取到选人组件列表数据
function getSelectListTwo(options = {}) {
    const params = {
        treeType: state.value.treeType,
        pid: 0,
        businessType: state.value.businessType,
        code: null,
        isRule: false,
        typeValue: null,
        ...options
    }

    http.post("/cloud/v3/tree/selectTree", params)
        .then(({ data }) => {
            const isAll = state.value.selected.find((i) => {
                return data[0]?.id == i.id
            })
            nextLevel(data[0] || [], isAll)
        })
        .finally(() => {})
}

function removeDuplicatesById(array) {
    // 使用一个Map来存储已经遇到过的id及其对应的对象
    const seen = new Map()

    // 遍历数组中的每个对象
    const filteredArray = array.filter((obj) => {
        // 如果Map中还没有这个id，就添加进去，并返回true保留该对象
        if (!seen.has(obj.id)) {
            seen.set(obj.id, true)
            return true
        }
        // 如果Map中已经有这个id了，就返回false，表示这个对象是重复的，应该过滤掉
        return false
    })

    return filteredArray
}

// 获取到选人组件列表数据
function getSelectList(options = {}, isAll = false) {
    const params = {
        treeType: state.value.treeType,
        pid: 0,
        businessType: state.value.businessType,
        code: null,
        isRule: false,
        typeValue: null,
        ...options
    }

    http.post("/cloud/v3/tree/selectTree", params)
        .then(({ data }) => {
            state.value.reqListArr = data
            if (isAll || areAllIdsInArrayA(state.value.selected, data)) {
                allSelectPeople()
            }
        })

        .finally(() => {})
}

const nextLevel = (item, isAll) => {
    state.value.selectPeople = false
    if (item.pid == 0 || !~state.value.selected.findIndex((i) => i.id === item.id)) {
        // 拼面包屑
        state.value.bread = [
            ...state.value.bread,
            {
                id: item.id,
                name: item.name,
                ...item
            }
        ]
        getSelectList({ typeValue: item.typeValue, pid: item.id }, isAll)
    }
}
const isSelected = computed(() => {
    return (item) => !!~state.value.selected.findIndex((i) => i.id === item.id)
})

// 点面包
const selectBread = (item, index) => {
    state.value.selectPeople = false
    state.value.bread = state.value.bread.slice(0, index + 1)
    getSelectList({ typeValue: item.typeValue, pid: item.id })
}

// 清空
const selectBreadAll = (item, index) => {
    if (item.pid != 0) {
        state.value.bread = []
        getSelectList()
    }
}

// 选中这个item
const selectItemThis = (item) => {
    const index = state.value.selected.findIndex((i) => i.id === item.id)
    if (~index) {
        state.value.selected.splice(index, 1)
    } else {
        state.value.selected.push(item)
    }
    state.value.selectPeople = areAllIdsInArrayA(state.value.selected, state.value.reqListArr)
}

// 全选
const allSelectPeople = () => {
    state.value.selectPeople = !state.value.selectPeople
    if (state.value.selectPeople) {
        // 合并去重
        state.value.selected = [...new Set([...state.value.reqListArr, ...state.value.selected])]
        state.value.selected = state.value.selected.filter((i) => {
            return i.typeValue != "school"
        })
    } else {
        // 取消全选就直接过滤不要的
        state.value.selected = state.value.selected.filter((item1) => !state.value.reqListArr.some((item2) => item2.id === item1.id || item2.pid == item1.id))
    }
    // 去重
    state.value.selected = removeDuplicatesById(state.value.selected)
    state.value.selectPeople = areAllIdsInArrayA(state.value.selected, state.value.reqListArr)
}

function clickLeft() {
    uni.navigateBack()
}

const outputPeople = () => {
    const arr = state.value.selected.filter((i) => {
        return i.pid != 0
    })
    selectMember.value.treeSubmitList = JSON.parse(JSON.stringify(arr))
    selectMember.value.treeSubmitListName = arr.map((item) => item.name).join("、")
    console.log(selectMember.value.treeSubmitList, "收集到的东西")
    clickLeft()
}

onLoad(() => {
    getSelectListTwo()
})

// 可以多次触发
onShow(() => {
    state.value.selected = JSON.parse(JSON.stringify(selectMember.value.treeSubmitList))
})
</script>

<style lang="scss" scoped>
.saveBtn {
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-color-primary;
    line-height: 40rpx;
    text-align: right;
    font-style: normal;
}

.searchBox {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    padding: 0 30rpx;
    height: 120rpx;
    background: $uni-bg-color;
}

.treeBox {
    background: $uni-bg-color;
    .treeHeadBox {
        padding: 0 204rpx;
        height: 88rpx;
        background: $uni-bg-color;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        font-size: 30rpx;
        line-height: 42rpx;
        text-align: center;
        color: $uni-text-color-grey;
        font-style: normal;
        .treeHeadBox_itemBox {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .treeHeadBox_itemBox_name {
                margin-bottom: 12rpx;
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color-grey;
                line-height: 42rpx;
                text-align: center;
                font-style: normal;
            }
            .activeColor {
                color: $uni-color-primary;
            }
            .activeLine {
                width: 40rpx;
                height: 4rpx;
                background: $uni-color-primary;
                border-radius: 2rpx;
            }
        }
    }
}

.breadcrumbBox {
    overflow-x: scroll;
    font-size: 28rpx;
    padding: 0rpx 30rpx 30rpx;
    .breadcrumb {
        display: flex;
    }
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.breadcrumb-item-slot {
    padding: 0 10px;
    color: $uni-text-color-grey;
}

.breadcrumb-item:first-child .breadcrumb-item-slot {
    padding-left: 0;
}

.breadcrumb-item:last-child > .breadcrumb-item-separator {
    display: none;
}

.breadcrumb-item-separator {
    color: #cccccc;
}

.breadcrumb .breadcrumb-item:not(:last-child) .breadcrumb-item-slot {
    color: $uni-color-primary;
}

.peopleList {
    font-size: 28rpx;
}

.allSelectBox {
    border-bottom: 1px solid $uni-border-color;
    .allSelect {
        display: flex;
        align-items: center;
        padding: 0rpx 30rpx 30rpx;
        .selectImgBox {
            width: 36rpx;
            height: 36rpx;
            .selectImg {
                width: 36rpx;
                height: 36rpx;
            }
        }
        .selectName {
            padding-left: 20rpx;

            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            text-align: left;
            font-style: normal;
        }
    }
}

.reqListBox {
    padding-left: 30rpx;
    padding-right: 30rpx;
    .itemSelect {
        padding: 30rpx 0rpx;
        display: flex;
        border-bottom: 1px solid $uni-border-color;
        align-items: center;
        justify-content: space-between;

        .selectImgBox {
            width: 36rpx;
            height: 36rpx;
            .selectImg {
                width: 36rpx;
                height: 36rpx;
            }
        }
        .avatarBox {
            padding-left: 20rpx;
        }
        .selectName {
            padding-left: 20rpx;

            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;

            text-align: left;
            font-style: normal;
        }
    }
    .itemSelect_left {
        display: flex;
        align-items: center;
        flex: 1;
    }
    .itemSelect_right {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        border-left: 1px solid $uni-border-color;
        padding-left: 30rpx;
        .nextImgBox {
            width: 40rpx;
            height: 40rpx;
            .nextImg {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }
}

.scroll-Y {
    height: calc(100vh - 400rpx);
}

// #ifdef MP-WEIXIN
.saveTreeBox {
    background: $uni-bg-color;
    padding: 30rpx;
    width: calc(100vw - 60rpx);
    position: fixed;
    bottom: 0;
    left: 0;
    .saveTree {
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }
}
// #endif
</style>

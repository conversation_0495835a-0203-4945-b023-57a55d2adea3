<template>
    <view class="add_task">
        <!-- 新增编辑的页面 -->
        <view v-show="pageType === 'add'">
            <!-- 头部 -->
            <uni-nav-bar statusBar fixed :border="false" @clickLeft="clickLeft" left-icon="left" title="发布任务"> </uni-nav-bar>
            <uni-list-item class="select_sub" :border="false" @click="selectSub" clickable showArrow title="选择科目">
                <template #footer>
                    <span class="subject_name">{{ subjectName }}</span>
                </template>
            </uni-list-item>
            <div class="pb10 form_item">
                <span class="title">打卡任务</span>
                <input class="y_uni_input" :value="form.taskName" :maxlength="20" @input="(e) => (form.taskName = e.detail.value)" placeholder="请输入打卡任务" />
            </div>
            <div class="form_item">
                <span class="title">任务描述</span>
                <textarea class="y_uni_textarea" :maxlength="200" :value="form.taskDescription" @blur="(e) => (form.taskDescription = e.detail.value)" auto-height placeholder="请输入任务描述" />
            </div>
            <div class="mt20 pb10 form_item">
                <span class="title">发布范围</span>
                <input class="y_uni_input" :value="form.treeSubmitListName" @click="showSelectMember" placeholder="请选择组织架构" />
            </div>
            <div class="form_item">
                <span class="title">发布时间与截止时间</span>
                <div class="mt20 time_box">
                    <uni-datetime-picker type="date" v-model="form.publishStart">
                        <div class="time_item" :style="{ color: form.publishStart ? '#333' : 'gray' }">
                            {{ form.publishStart || "请选择开始时间" }}
                        </div>
                    </uni-datetime-picker>
                    <span style="padding: 0rpx 10rpx">至</span>
                    <uni-datetime-picker type="date" v-model="form.publishEnd">
                        <div class="time_item" :style="{ color: form.publishEnd ? '#333' : 'gray' }">
                            {{ form.publishEnd || "请选择结束时间" }}
                        </div>
                    </uni-datetime-picker>
                </div>
            </div>

            <view class="frequency" @click="selectfrequency">
                <view class="frequency_left"> 打卡频次 </view>
                <view class="frequency_right">
                    <view class="week_text">{{ comDayName }}</view>
                    <uni-icons type="right" size="20"></uni-icons>
                </view>
            </view>
            <view class="frequency">
                <view class="frequency_left"> 提交时必须包含图片 </view>
                <view class="frequency_right">
                    <switch :checked="form.includeimages" style="transform: scale(0.7)" color="#00b781" @change="isTodayChange" />
                </view>
            </view>

            <view class="footBox">
                <view class="footBox_con" @click="reqSave">
                    <button class="footBox_con_btn" :loading="isSaving">
                        {{ isSaving ? "保存中..." : "保存" }}
                    </button>
                </view>
            </view>
        </view>

        <!-- 选择科目放这里就没错的 -->
        <view class="subject_box" v-show="pageType === 'subject'">
            <!-- 头部 -->
            <uni-nav-bar statusBar fixed :border="false" @clickLeft="subjectBack" @clickRight="saveSubject" left-icon="left" title="选择科目">
                <!-- #ifdef H5 || H5-WEIXIN-->
                <template #right>
                    <view class="save_btn">保存</view>
                </template>
                <!-- #endif -->
                <!-- #ifdef APP-PLUS-->
                <template #right>
                    <view class="save_btn">保存</view>
                </template>
                <!-- #endif -->
            </uni-nav-bar>
            <view class="subjectBox_list">
                <view class="subjectBox_item_box">
                    <view class="subjectBox_item" @click="selectSubject({ id: -1, name: '不限' })">
                        <view class="subjectBox_item_text"> 不限 </view>
                        <view v-if="isSelected({ id: -1, name: '不限' })">
                            <uni-icons color="#00b781" type="checkmarkempty" size="20"></uni-icons>
                        </view>
                    </view>
                </view>
                <view class="line_box"></view>
                <view class="subjectBox_item_box">
                    <view class="subjectBox_item" @click="selectSubject(item)" v-for="item in state.subJectListArr" :key="item.id">
                        <view class="subjectBox_item_text"> {{ item.name }} </view>
                        <view v-if="isSelected(item)">
                            <uni-icons color="#00b781" type="checkmarkempty" size="20"></uni-icons>
                        </view>
                    </view>
                </view>
                <!-- #ifdef MP-WEIXIN -->
                <div class="save_subject_box">
                    <button class="save_subject" @click="saveSubject">保存</button>
                </div>
                <!-- #endif -->
            </view>
        </view>

        <!-- 打卡频次的放这里这里就没错的 -->
        <view class="subject_box" v-show="pageType === 'frequency'">
            <!-- 头部 -->
            <uni-nav-bar statusBar fixed :border="false" @clickRight="saveFrequency" @clickLeft="lossFrequency" left-icon="left" title="打卡频次">
                <!-- #ifdef H5 || H5-WEIXIN-->
                <template #right>
                    <view class="save_btn">保存</view>
                </template>
                <!-- #endif -->
                <!-- #ifdef APP-PLUS-->
                <template #right>
                    <view class="save_btn">保存</view>
                </template>
                <!-- #endif -->
            </uni-nav-bar>
            <view class="check_box">
                <view class="check_box_page">
                    <view class="check_box_page_text"> 单次打卡 </view>
                    <view class="CheckBox_page_switch">
                        <switch :checked="form.single" style="transform: scale(0.7)" color="#00b781" @change="singleChange" />
                    </view>
                </view>
                <view class="check_box_page custom_box">
                    <view class="check_box_page_text"> 自定义打卡 </view>
                    <view class="CheckBox_page_switch">
                        <switch :checked="form.custom" style="transform: scale(0.7)" color="#00b781" @change="customChange" />
                    </view>
                </view>
                <view v-if="form.custom">
                    <view class="check_box_page" @click="showList">
                        <view class="check_box_page_text">
                            {{ comDayName || "全部打卡频次" }}
                        </view>
                        <view class="CheckBox_page_switch">
                            <uni-icons v-if="!showListvalue" type="right" size="20"></uni-icons>
                            <uni-icons v-if="showListvalue" type="down" size="20"></uni-icons>
                        </view>
                    </view>
                    <view v-if="showListvalue">
                        <view class="check_box_page" v-for="item in dayObjArr" :key="item.id" @click="selectDay(item)">
                            <view class="check_box_page_text"> {{ item.name }} </view>
                            <view class="CheckBox_page_switch">
                                <uni-icons color="#00b781" v-if="isSelectedDay(item)" type="checkmarkempty" size="20"></uni-icons>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <!-- #ifdef MP-WEIXIN -->
            <div class="saveFrequencyBox">
                <button class="saveFrequency" @click="saveFrequency">保存</button>
            </div>
            <!-- #endif -->
        </view>
    </view>
</template>

<script setup>
import { onLoad, onShow, onUnload } from "@dcloudio/uni-app"
import { selectMember } from "./treeSubmitList"

// 就简单几个类型就随便定义一下算球了
const pageType = ref("add")
const isEdit = ref(false)
const isSaving = ref(false)
const showListvalue = ref(false)

const state = ref({
    dayListArr: [],
    subJectListArr: [],
    selectedSubject: []
})
const parameter = ref({})

// 收集到的发送请求参数全部塞这里面
const form = ref({
    includeimages: false,
    subjectIdList: [],
    taskName: "",
    taskDescription: "",
    requirementList: ["upload_picture"],
    publishStart: null,
    publishEnd: null
})

const saveSubject = () => {
    pageType.value = "add"
}

const selectSub = () => {
    pageType.value = "subject"
}

const selectfrequency = () => {
    pageType.value = "frequency"
}
const subjectBack = () => {
    // state.value.selectedSubject = [];
    pageType.value = "add"
}

const dayObjArr = [
    {
        name: "周一",
        id: "1"
    },
    {
        name: "周二",
        id: "2"
    },
    {
        name: "周三",
        id: "3"
    },
    {
        name: "周四",
        id: "4"
    },
    {
        name: "周五",
        id: "5"
    },
    {
        name: "周六",
        id: "6"
    },
    {
        name: "周日",
        id: "7"
    },
    {
        name: "全部",
        id: "8"
    }
]

const showList = () => {
    showListvalue.value = !showListvalue.value
}

function clickLeft() {
    uni.navigateBack()
}

const showSelectMember = () => {
    // 跳进选人的地方
    navigateTo({
        url: "/apps/punchTheClock/addTask/range"
    })
}

// 获取一手科目列表
function subList(options = {}) {
    const params = {}
    http.get("/app/subject/subjectList", params)
        .then(({ data }) => {
            // 这快有可能要过滤一下参数 isDelete 我的猜想 如果是用 filter 过滤一下就好了
            state.value.subJectListArr = data
        })
        .finally(() => {})
}

const MAX_SUBJECTS = 3 // 最多选择3个科目
const SPECIAL_ID = -1
const selectSubject = (item) => {
    const isSelected = state.value.selectedSubject.some((i) => i.id === item.id)
    const hasSpecial = state.value.selectedSubject.some((i) => i.id === SPECIAL_ID)

    if (isSelected) {
        // 如果已选择，则移除该科目
        state.value.selectedSubject = state.value.selectedSubject.filter((i) => i.id !== item.id)
    } else if (item.id === SPECIAL_ID) {
        // 如果是特殊 ID，则只选择该科目
        state.value.selectedSubject = [item]
    } else {
        // 先处理特殊 ID 的情况（如果存在）
        if (hasSpecial) {
            state.value.selectedSubject = state.value.selectedSubject.filter((i) => i.id !== SPECIAL_ID)
        }

        // 检查是否已达到最大选择数量
        if (state.value.selectedSubject.length >= MAX_SUBJECTS) {
            uni.showToast({ title: "最多选择3个科目", icon: "none" })
        } else {
            // 否则，添加该科目到选择列表
            state.value.selectedSubject.push(item)
        }
    }
}

const selectDay = (item) => {
    const index = state.value.dayListArr.findIndex((i) => i.id === item.id)
    if (~index) {
        if (item.id === "8") {
            state.value.dayListArr = []
        } else {
            if (state.value.dayListArr.length === "8") {
                state.value.dayListArr.splice(7, 1)
                state.value.dayListArr.splice(index, 1)
            } else {
                state.value.dayListArr.splice(index, 1)
            }
        }
    } else {
        if (item.id === "8") {
            state.value.dayListArr = JSON.parse(JSON.stringify(dayObjArr))
        } else {
            // 如果7天都存在 就把所有的加进去
            if (state.value.dayListArr.length === 6) {
                state.value.dayListArr = JSON.parse(JSON.stringify(dayObjArr))
            } else {
                state.value.dayListArr.push(item)
            }
        }
    }
}

const isSelected = computed(() => {
    return (item) => !!~state.value.selectedSubject.findIndex((i) => i.id === item.id)
})

const isSelectedDay = computed(() => {
    return (item) => !!~state.value.dayListArr.findIndex((i) => i.id === item.id)
})

// 转转转转转转转转转转转转转转转转转转转转转转转转
const reqSave = () => {
    if (!isSaving.value) {
        isSaving.value = true
        // 收集科目id
        form.value.subjectIdList = state.value.selectedSubject.map((item) => item.id)
        // 提交时是不是必须包含图片
        if (form.value.includeimages) {
            form.value.requirementList = ["upload_picture"]
        } else {
            form.value.requirementList = []
        }

        if (form.value.single) {
            form.value.frequency = "single"
            form.value.customerOption = []
        }
        if (form.value.custom) {
            form.value.frequency = "custom"
            form.value.customerOption = state.value.dayListArr.filter((item) => item.id !== "8").map((item) => item.id)
        }

        // 弄一个映射对象
        const mapObj = {
            school: "school",
            campus: "campus",
            academics: "academics",
            grade: "grade",
            classes: "classes",
            dept: "children",
            college: "college",
            professional: "professional"
        }
        console.log(form.value.treeSubmitList, "form.value.treeSubmitList")
        // 处理一个究极的范围数据直接map吧
        form.value.publishScopeObjList = form.value.treeSubmitList.map((item) => {
            return {
                id: item.id,
                objType: item.treeType === 1 ? "class" : "dept",
                objLevel: mapObj[item.typeValue]
            }
        })
        let apiURL = isEdit.value ? "/attweb/attendanceSignTask/update" : "/attweb/attendanceSignTask/app/add"
        //请求直接发出去得了
        http.post(apiURL, {
            ...form.value,
            id: isEdit.value ? parameter.value.id : null
        })
            .then((res) => {
                uni.showToast({
                    title: res.message,
                    icon: "none",
                    duration: 2000
                })
                uni.navigateBack()
            })
            .finally(() => {
                isSaving.value = false
            })
    }
}
const comDayName = computed(() => {
    const namearr = state.value.dayListArr.filter((item) => item.id !== "8")
    return namearr.map((item) => item.name).join("/")
})

const isTodayChange = (e) => {
    form.value.includeimages = e.detail.value
}
const singleChange = (e) => {
    form.value.single = e.detail.value
    if (form.value.single) {
        state.value.dayListArr = []
        form.value.custom = false
    }
}

const customChange = (e) => {
    form.value.custom = e.detail.value
    if (form.value.custom) {
        form.value.single = false
    }
}

const lossFrequency = () => {
    // state.value.dayListArr = [];
    pageType.value = "add"
}

const saveFrequency = () => {
    pageType.value = "add"
}

// 弄一个映射对象
const mapObjTwo = {
    school: "school",
    campus: "campus",
    academics: "academics",
    grade: "grade",
    classes: "classes",
    children: "dept",
    college: "college",
    professional: "professional"
}

const subjectName = computed(() => {
    return state.value.selectedSubject
        .map((i) => {
            return i.name
        })
        .join("/")
})
async function getInfo(id) {
    http.get("/attweb/attendanceSignTask/app/get", { id }).then((res) => {
        const { frequency, requirementList, subjectList, weekendList, publishScopeObjList } = res.data
        form.value = res.data
        form.value.single = frequency == "single"
        form.value.custom = frequency == "custom"
        form.value.includeimages = requirementList.length
        state.value.selectedSubject = subjectList
        const arr = publishScopeObjList.map((i) => {
            return i.name
        })
        selectMember.value.treeSubmitListName = arr.join("、")
        selectMember.value.treeSubmitList = publishScopeObjList.map((i) => {
            return {
                ...i,
                typeValue: mapObjTwo[i.objLevel],
                treeType: i.objType == "class" ? 1 : 2
            }
        })
        form.value.treeSubmitListName = arr.join("、")
        form.value.treeSubmitList = publishScopeObjList.map((i) => {
            return {
                ...i,
                typeValue: mapObjTwo[i.objLevel],
                treeType: i.objType == "class" ? 1 : 2
            }
        })
        console.log(publishScopeObjList)
        state.value.dayListArr = weekendList
    })
}

// 可以多次触发
onShow(() => {
    console.log(selectMember.value.treeSubmitList, "selectMember.value.treeSubmitList")
    form.value.treeSubmitListName = JSON.parse(JSON.stringify(selectMember.value.treeSubmitListName))
    form.value.treeSubmitList = JSON.parse(JSON.stringify(selectMember.value.treeSubmitList))
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    parameter.value = options
    if (options.id) {
        isEdit.value = true
        getInfo(options.id)
    } else {
        isEdit.value = false
        state.value.selectedSubject = [
            {
                id: -1,
                name: "不限"
            }
        ]
    }
    subList()
})

onUnload(() => {
    selectMember.value.treeSubmitListName = ""
    selectMember.value.treeSubmitList = []
})
</script>

<style lang="scss" scoped>
.add_task {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .form_item {
        min-height: 112rpx;
        background: $uni-bg-color;
        padding: 30rpx;
        .title {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            line-height: 42rpx;
        }
        .y_uni_input {
            margin-top: 20rpx;
            height: 40rpx;
            background: $uni-bg-color-grey;
            border-radius: 4rpx;
            padding: 20rpx;
        }
        .y_uni_textarea {
            width: calc(100% - 40rpx);
            margin-top: 20rpx;
            min-height: 318rpx;
            background: $uni-bg-color-grey;
            border-radius: 4rpx;
            padding: 20rpx;
        }
        .time_box {
            display: flex;
            align-items: center;
            .time_item {
                height: 80rpx;
                background: $uni-bg-color-grey;
                border-radius: 4rpx;
                text-align: center;
                line-height: 80rpx;
            }
        }
    }
    .mt20 {
        margin-top: 20rpx;
    }
    .pb10 {
        padding-bottom: 10rpx;
    }

    .save_btn {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-color-primary;
        line-height: 40rpx;
        text-align: right;
        font-style: normal;
    }
    .select_sub {
        font-weight: 600;
        font-size: 30rpx;
        color: $uni-text-color;
        min-height: 100rpx;
        margin-bottom: 20rpx;
        :deep(.uni-list-item__content-title) {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
        }
    }
    .frequency {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: $uni-bg-color;
        margin-bottom: 20rpx;
        padding-left: 15px;
        padding-right: 15px;
        .frequency_left {
            font-weight: 600;
            font-size: 30rpx;
            color: $uni-text-color;
            text-align: left;
            font-style: normal;
        }
        .frequency_right {
            display: flex;
            align-items: center;
            .week_text {
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color-grey;
                line-height: 40rpx;
                text-align: left;
                font-style: normal;
            }
        }
    }

    .footBox {
        height: 166rpx;
        .footBox_con {
            width: 100%;
            position: fixed;
            bottom: 0;
            height: 166rpx;
            background: $uni-bg-color;

            .footBox_con_btn {
                font-size: 32rpx;
                color: $uni-text-color-inverse;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 30rpx 30rpx 0rpx 30rpx;
                height: 92rpx;
                background: $uni-color-primary;
                border-radius: 10rpx;
            }
        }
    }
}

.subject_box {
    background: $uni-bg-color;
    .subjectBox_list {
        // #ifdef MP-WEIXIN
        padding-bottom: 182rpx;
        .save_subject_box {
            background: $uni-bg-color;
            padding: 30rpx;
            width: calc(100vw - 60rpx);
            position: fixed;
            bottom: 0;
            left: 0;
            .save_subject {
                background: $uni-color-primary;
                color: $uni-text-color-inverse;
            }
        }
        // #endif
        .subjectBox_item_box {
            padding: 0 30rpx;
            .subjectBox_item_no {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 100rpx;
                background: $uni-bg-color;
            }
            .subjectBox_item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
                height: 100rpx;
                background: $uni-bg-color;
                border-bottom: 1rpx solid $uni-border-color;

                .subjectBox_item_text {
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-text-color;
                    line-height: 40rpx;
                    text-align: left;
                    font-style: normal;
                }
            }
        }
        .unlimited {
            margin-bottom: 20rpx;
        }
    }
}
// #ifdef MP-WEIXIN
.saveFrequencyBox {
    background: $uni-bg-color;
    padding: 30rpx;
    width: calc(100vw - 60rpx);
    position: fixed;
    bottom: 0;
    left: 0;
    .saveFrequency {
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }
}
// #endif

.line_box {
    height: 20rpx;
    width: 100%;
    background: $uni-bg-color-grey;
}
.subject_name {
    max-width: 460rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: $uni-text-color-grey;
    text-align: right;
    line-height: 40rpx;
}
.check_box {
    padding: 0 30rpx;

    .check_box_page {
        display: flex;
        align-items: center;
        height: 100rpx;
        justify-content: space-between;
        .check_box_page_text {
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
    }
    .custom_box {
        border-bottom: 1rpx dashed $uni-border-color;
    }
}
</style>

<template>
    <div>
        <!-- 家长单独一个页面接口 -->
        <eltern-task-list v-if="identityType == 'eltern'" :identityType="identityType" />
        <!-- 老师和学生跳一个页面接口（学生去掉部分功能） -->
        <task-list v-else :identityType="identityType" />
    </div>
</template>

<script setup>
import ElternTaskList from "./components/elternTaskList.vue"
import TaskList from "./components/taskList.vue"
import useStore from "@/store"
const { user } = useStore()

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
</script>

<style lang="scss" scoped></style>

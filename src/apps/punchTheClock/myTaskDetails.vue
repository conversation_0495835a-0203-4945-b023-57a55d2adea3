<template>
    <div>
        <uni-nav-bar statusBar fixed :border="false" left-icon="left" @clickLeft="clickLeft" title="任务详情"> </uni-nav-bar>
        <div class="task_details">
            <div class="task_content">
                <div class="title_box">
                    <div v-if="info.subjectList && info.subjectList[0].name != '不限'">
                        <div class="tip ellipsis" v-for="(item, index) in info.subjectList" :key="index">
                            {{ item.name }}
                        </div>
                    </div>

                    <div class="title ellipsis">{{ info.taskName }}</div>
                </div>
                <div class="content">{{ info.taskDescription }}</div>
            </div>
            <div class="task_content">
                <div class="info_item">
                    <div class="info_lable">发布范围</div>
                    <div class="info_value">
                        <span v-for="(item, index) in info.publishScopeObjList" :key="index"> <span v-if="index != 0">、</span> {{ item.name }} </span>
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">发布时间</div>
                    <div class="info_value">
                        {{ info.publishStart }}
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">截止时间</div>
                    <div class="info_value">
                        {{ info.publishEnd }}
                    </div>
                </div>
                <div class="info_item">
                    <div class="info_lable">打卡频次</div>
                    <div class="info_value" v-if="info.frequency == 'custom'">
                        <!-- 如果是custom自定义的 并且自定义的天数数组为7（周一到周日）那就是每天 -->
                        <span v-if="info.weekendList && info.weekendList.length == 7"> 每天 </span>
                        <!-- 如果自定义天数数组长度不是7则循环出来 -->
                        <span v-else>
                            <span v-for="(item, index) in info.weekendList" :key="index"> <span v-if="index != 0">、</span>{{ item.name }}</span>
                        </span>
                    </div>
                    <!-- 如果不等于custom,就是单次打卡 -->
                    <!-- single就是单次打卡 -->
                    <div class="info_value" v-else>单次打卡</div>
                </div>
                <!-- requirementList 数组不能为空才有打卡要求和上传图片 -->
                <div class="info_item" v-if="isRequire(info.requirementList)">
                    <div class="info_lable">打卡要求</div>
                    <div class="info_value">
                        <!-- 打卡要求只有上传图片？？？ -->
                        {{ { upload_picture: "提交时必须包含图片" }[info.requirementList] }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
const info = ref({})
const parameter = ref({})

const isRequire = computed(() => {
    return (arr) => {
        return arr && arr.length > 0
    }
})

function clickLeft() {
    uni.navigateBack()
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    parameter.value = options
    getInfo(options.id)
})

function getInfo(id) {
    uni.showLoading({
        title: "加载中"
    })
    http.get("/attweb/attendanceSignTask/app/get", { id })
        .then((res) => {
            info.value = res.data
        })
        .finally(() => {
            uni.hideLoading()
        })
}
</script>

<style lang="scss" scoped>
.task_details {
    padding-bottom: 220rpx;
    .title_box {
        display: flex;
        align-items: center;
        .tip {
            display: inline-block;
            min-width: 64rpx;
            max-width: 64rpx;
            border-radius: 4rpx;
            border: 1rpx solid $uni-color-primary;
            font-weight: 400;
            font-size: 20rpx;
            color: $uni-color-primary;
            line-height: 32rpx;
            text-align: center;
            margin-left: 10rpx;
            margin-bottom: -4rpx;
        }
        .title {
            flex: 1;
        }
    }
    .content {
        margin-top: 30rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
        // text-indent: 60rpx;
    }
    .task_content {
        background: $uni-bg-color;
        padding: 15rpx 30rpx;
        font-weight: 400;
        margin-top: 20rpx;
        .info_item {
            padding: 15rpx 0rpx;
            .info_lable {
                font-size: 24rpx;
                color: #666666;
                line-height: 34rpx;
            }
            .info_value {
                font-size: 28rpx;
                color: $uni-text-color;
                margin-top: 10rpx;
                line-height: 40rpx;
            }
            .upload_img {
                width: 150rpx;
                height: 150rpx;
            }
            .clock_time_box {
                margin-top: 20rpx;
                height: 100%;
                position: relative;
                display: flex;
                .time_info {
                    width: 16rpx;
                    position: absolute;
                    top: 8rpx;
                    height: 16rpx;
                    background: #b3b3b3;
                    border-radius: 50%;
                }
                .line {
                    position: absolute;
                    height: 100%;
                    left: 6rpx;
                    top: 8rpx;
                    width: 4rpx;
                    background: #b3b3b3;
                }
                .clock_time {
                    margin-left: 30rpx;
                }
            }
        }
    }
}
</style>

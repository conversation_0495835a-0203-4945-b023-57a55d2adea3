<!-- 学生进来的页面 -->
<template>
    <view class="registrationPage">
        <!-- 头部 -->
        <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="routerBack" title="活动报名"> </uni-nav-bar>
        <!-- 筛选 -->
        <drop-down :menu="menu" @clickItem="clickItem" ref="dropDownRef"> </drop-down>
        <!-- 滑动加载区域 -->
        <scroll-view v-if="state.activityCardList && state.activityCardList.length > 0" class="scroll-Y" :scrollTop="state.scrollTop" :show-scrollbar="true" :lower-threshold="100" :scroll-y="true" :scroll-with-animation="true" @scrolltolower="handlerScrollBottom">
            <view class="achievement-item" @click="enterDetails(item)" v-for="item in state.activityCardList" :key="item.id">
                <view class="titleBox">
                    <view class="title_state" :style="{ backgroundColor: comColor(item.status) }">{{ item.statusName }}</view>
                    <view class="title_name">{{ item.activityName }}</view>
                </view>

                <view class="numBox">
                    <view class="numBox_name">总报名额度：{{ item.total || "无限制" }}</view>
                    <view class="numBox_name">已报名：{{ item.enrollCount }}</view>
                </view>
                <view class="timeBox" v-if="item.status === 1">
                    <view class="timeBox_name">报名时间：{{ item.registerStartTime }} 至 {{ item.registerEndTime }}</view>
                </view>
                <view class="timeBox" v-else>
                    <view class="timeBox_name">活动时间：{{ item.activityStartTime }} 至 {{ item.activityEndTime }}</view>
                </view>
            </view>
            <uni-load-more :showText="state.loadMore.showText" :contentText="state.loadMore.contentText" :status="state.loadMore.status" @clickLoadMore="state.scrollTop = 0" />
        </scroll-view>
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>
<script setup>
import useStore from "@/store"
import DropDown from "./dropDown.vue"

const { user } = useStore()
const menu = ref({
    type: {
        label: "全部活动",
        child: [
            { label: "全部活动", value: "all" },
            { label: "我报名的", value: "1" }
        ]
    },
    dateNum: {
        label: "创建时间",
        child: [
            { label: "创建时间", value: "all" },
            { label: "7天内", value: "7" },
            { label: "30天内", value: "30" },
            { label: "180天内", value: "180" }
        ]
    },
    status: {
        label: "报名状态",
        child: [
            { label: "报名状态", value: "all" },
            { label: "未开始", value: "1" },
            { label: "报名中", value: "2" },
            { label: "已结束", value: "3" },
            { label: "已关闭", value: "4" }
        ]
    }
})

const statusObj = {
    1: {
        text: "未开始",
        color: "#FFAC08"
    },
    2: {
        text: "报名中",
        color: "#00B781"
    },
    3: {
        text: "已结束",
        color: "#ADADAD"
    },
    4: {
        text: "已关闭",
        color: "#ADADAD"
    },
    default: {
        text: "-",
        color: ""
    } // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}

// 计算颜色
const comColor = computed(() => (status) => {
    return getStatusObj(status).color
})

function mergeArrays(arr1, arr2) {
    let mergedArray = Array.from(new Set(arr1.concat(arr2).map(JSON.stringify))).map(JSON.parse)
    return mergedArray
}

const state = ref({
    myChildren: [],
    activityCardList: [],
    loadMore: {
        status: "more", // more	加载前, loading	加载中, no-more	没有更多数据
        showText: true, // 显示文本
        contentText: {
            contentdown: "点击加载更多"
        }
    },
    scrollTop: 0,
    identity: 0,
    studentId: user.identityInfo.id
})

// 搜索条件
const query = ref({
    pageNo: 1,
    pageSize: 100,
    total: 0
})

// 重置数据
const resetList = () => {
    state.value.activityCardList = []
    query.value.pageNo = 1
    query.value.total = 0
    init()
}

const init = () => {
    const params = {
        ...query.value,
        studentId: state.value.studentId
    }
    http.post("/app/activity/enroll/studentEnrollPage", params).then(({ data }) => {
        const { list, pageNo, pageSize, total } = data
        query.value.total = total
        state.value.activityCardList = mergeArrays(state.value.activityCardList, list)
        if (pageNo * pageSize >= total) {
            state.value.loadMore.status = "no-more"
        } else {
            state.value.loadMore.status = "more"
        }
    })
}

const clickItem = (item) => {
    const { name, value } = item
    query.value[name] = value === "all" ? null : value
    resetList()
    state.scrollTop = 0
}

// 滚动加载
const handlerScrollBottom = (e) => {
    state.value.scrollTop = e.target.offsetTop
    const { pageNo, pageSize, total } = query.value
    if (pageNo * pageSize <= total) {
        state.value.loadMore.status = "loading"
        query.value.pageNo++
        init()
    }
}

// 进入详情页面
const enterDetails = (item) => {
    navigateTo({
        url: "/apps/registration/details",
        query: {
            studentId: state.value.studentId,
            activityId: item.id,
            belong: query.value.type || "all"
        }
    })
}

onMounted(() => {
    init()
})
</script>
<style lang="scss" scoped>
.registrationPage {
    min-height: 100vh;
    background-color: $uni-bg-color-grey;
}

.scroll-Y {
    .achievement-item {
        margin: 24rpx 32rpx;
        padding: 24rpx;
        background-color: $uni-bg-color;
        border-radius: 12rpx;
        .titleBox {
            display: flex;
            align-items: center;
            padding-bottom: 26rpx;
            .title_state {
                padding: 4rpx 10rpx;
                width: max-content;
                height: 36rpx;
                border-radius: 8rpx;
                font-weight: 500;
                font-size: 20rpx;
                color: $uni-text-color-inverse;
                line-height: 36rpx;
                text-align: center;
                font-style: normal;
                flex-shrink: 0; // 不压缩这里的空间
            }
            .title_name {
                font-weight: 600;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
                text-align: left;
                font-style: normal;
                padding-left: 16rpx;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .numBox {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 20rpx;
            .numBox_name {
                font-weight: 400;
                font-size: 26rpx;
                color: $uni-text-color;
                text-align: justify;
                font-style: normal;
            }
        }
        .timeBox {
            display: flex;
            align-items: center;
            .timeBox_name {
                font-weight: 400;
                font-size: 24rpx;
                color: #666666;
                text-align: left;
                font-style: normal;
            }
        }
    }
}

.addImg {
    width: 140rpx;
    height: 140rpx;
    position: fixed;
    right: 0;
    bottom: 218rpx;
}

.popup-children {
    background-color: $uni-bg-color;
    height: 400rpx;
    position: relative;
    border-radius: 20rpx 20rpx 0 0;

    .title {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
    }

    .popup-close {
        position: absolute;
        top: 10rpx;
        right: 30rpx;
    }

    .uni-list-item {
        display: flex;
        justify-content: space-between;
        padding: 20rpx;
        .active {
            :deep(.uni-radio-input) {
                background-color: $uni-color-primary !important;
                border-color: $uni-color-primary !important;
            }
        }
    }
}

.switchchildBox {
    width: 140rpx;
    height: 140rpx;
    border-radius: 50%;
    background-color: $uni-color-primary;
    position: fixed;
    right: 0;
    bottom: 218rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    font-weight: 400;
    font-size: 26rpx;
    color: $uni-text-color-inverse;
}
</style>

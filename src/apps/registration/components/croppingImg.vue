<template>
    <view class="cropping-img-con">
        <img ref="croppingImg" :src="props.url" />
        <view class="croppimg-img-btn">
            <uv-icon name="close" color="#ffffff" size="24" class="btn1" @click="cannel"></uv-icon>
            <uv-icon name="checkmark" color="#ffffff" size="24" class="btn2" @click="confirm"></uv-icon>
        </view>
    </view>
</template>

<script setup>
import Cropper from "cropperjs"
import "cropperjs/dist/cropper.css"

const props = defineProps({
    url: {
        type: String,
        default: ""
    }
})

const init = () => {
    cropper.value = new Cropper(croppingImg.value, {
        viewMode: 1,
        dragMode: "move",
        initialAspectRatio: 1,
        cropBoxResizable: false, // 是否可以改变裁剪框的尺寸
        aspectRatio: 1,
        preview: ".before",
        background: false,
        autoCropArea: 0.6,
        zoomOnWheel: false,
        aspectRatio: 9 / 16,
        minContainerWidth: 500,
        minContainerHeight: 500,

        crop: () => {
            // console.log(cropper.value.getCroppedCanvas().toDataURL('image/jpeg'))
        }
    })
}

/* 图片裁剪 */
const cropper = ref()
const croppingImg = ref("")
const cannel = () => {
    emit("returnImg", "")
}

const emit = defineEmits(["returnImg"])
const confirm = () => {
    uni.showLoading({
        title: "加载中"
    })
    let res = cropper.value
        .getCroppedCanvas({
            imageSmoothingQuality: "low"
        })
        .toDataURL("image/jpeg")
    http.uploadFile("/file/common/upload", res, {
        folderType: "visitorFace"
    })
        .then((res) => {
            emit("returnImg", res)
        })
        .catch(() => {
            emit("returnImg", "")
        })
        .finally(() => {
            uni.hideLoading()
        })
}

// 可以多次触发
onShow(() => {
    nextTick(() => {
        init()
    })
})

// 暴露变量
defineExpose({
    init
})
</script>

<style lang="scss" scoped>
.cropping-img-con {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background-color: #000;

    .croppimg-img-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        position: absolute;
        bottom: 80rpx;
        .btn1 {
            padding-left: 40rpx;
        }
        .btn2 {
            padding-right: 40rpx;
        }
    }
}
</style>

<template>
    <!-- 活动报名 -->
    <div>
        <eltern-index v-if="identityType == 'eltern'" :identityType="identityType" />
        <student-index v-else-if="identityType == 'student'" :identityType="identityType" />
        <teacher-index v-else :identityType="identityType" />
    </div>
</template>

<script setup>
import elternIndex from "./components/elternIndex.vue"
import studentIndex from "./components/studentIndex.vue"
import teacherIndex from "./components/teacherIndex.vue"
import useStore from "@/store"
const { user } = useStore()

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
</script>

<style lang="scss" scoped></style>

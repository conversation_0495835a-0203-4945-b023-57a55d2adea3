<template>
    <view class="select_filter">
        <view class="filter_item filter_left"> <slot name="left"></slot> </view>
        <view class="filter_item" v-if="identityType != 'teacher' && isContent"> <slot name="content"></slot> </view>
        <view class="filter_item date_picker">
            <uni-datetime-picker :border="false" :clearIcon="false" type="daterange" placeholder="选择时间" v-model="date" @change="changeDate" rangeSeparator="至">
                <template #default>
                    <view v-if="date && date.length">{{ `${date[0]?.substring(5)}至${date[1]?.substring(5)}` }}</view>
                    <view v-else>请选择</view>
                </template>
            </uni-datetime-picker>
        </view>
    </view>
</template>

<script setup>
import { identityType } from "../data"
import dayjs from "dayjs"
import isoWeek from "dayjs/plugin/isoWeek"

dayjs.extend(isoWeek)

const emit = defineEmits(["changeDate"])
const props = defineProps({
    isContent: {
        type: Boolean,
        default: false
    }
})

const getLastWeekRange = computed(() => {
    const currentWeekMonday = dayjs().isoWeekday(1)
    const lastWeekMonday = currentWeekMonday.subtract(7, "day")
    const lastWeekSunday = lastWeekMonday.add(6, "day")
    return [lastWeekMonday.format("YYYY-MM-DD"), lastWeekSunday.format("YYYY-MM-DD")]
})

const date = ref(getLastWeekRange.value)

function changeDate() {
    emit("changeDate", { startDate: date.value[0], endDate: date.value[1] })
}

const getDate = () => {
    return { startDate: date.value[0], endDate: date.value[1] }
}

defineExpose({ getDate })
</script>

<style lang="scss" scoped>
.select_filter {
    padding: 30rpx;
    background: $uni-bg-color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter_left {
        padding-right: 10rpx;
    }
    .filter_item {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;

        &::after {
            content: "";
            display: block;
            border: 10rpx solid transparent;
            border-top: 10rpx solid $uni-color-primary;
            border-bottom-width: 1px;
            margin-left: 10rpx;
        }
    }
    .date_picker {
        justify-content: flex-end;
        :deep(.uni-date) {
            flex: none;
            width: auto;
        }

        :deep(.uni-icons) {
            display: none;
        }
    }
}
</style>

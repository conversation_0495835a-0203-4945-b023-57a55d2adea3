<template>
    <view class="student_attendance">
        <!-- 考勤类型 -->
        <view class="title_box">
            <view class="select_type" @click="selectTypeRef.open()">
                <text class="type">{{ typeObj.name || "请选择" }}</text>
                <uni-icons type="down" size="18" color="#666666"></uni-icons>
            </view>
            <view class="right" @click="goDetails">
                <text>查看详情</text>
                <uni-icons type="right" size="18" color="#11C685"></uni-icons>
            </view>
        </view>
        <div class="attendance_tip">
            <image class="image" src="@nginx/workbench/weeklyPublication/ligth.png" mode="scaleToFill" />
            <text class="text"> {{ tipText }}</text>
        </div>

        <!--  -->
        <view class="attendance_chart" v-if="selecyTypeObj[typeObj.value]?.errorCount > 0">
            <qiun-data-charts type="ring" v-if="chartData?.categories?.length" :opts="chartOpts" :chartData="chartData" />
            <text class="date_picker_text"> {{ queryDate.startDate?.substring(5) }} 至 {{ queryDate.endDate?.substring(5) }}： </text>
        </view>
        <view class="no_error" v-else>
            <image class="image" src="@nginx/workbench/weeklyPublication/no_error.png" mode="scaleToFill" />
        </view>

        <!-- 选择类型 -->
        <yd-select-popup ref="selectTypeRef" title="请选择考勤类型" :list="tabs" @closePopup="closeType" :fieldNames="{ value: 'value', label: 'name' }" :selectId="[typeObj.value]" />
    </view>
</template>

<script setup>
import qiunDataCharts from "@/subModules/components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue"
import { tabs, chartOpts } from "../data"

const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    },
    queryDate: {
        type: Object,
        default: () => {}
    }
})

const data = computed(() => props.data)
const queryDate = computed(() => props.queryDate)
const selectTypeRef = ref(null)
const typeObj = ref({ name: "出入校", value: "goOutSchool" })

const selecyTypeObj = {
    course: data.value.classroomData,
    inOutData: data.value.inOutData
}

const tipText = computed(() => {
    const time = `${queryDate.value.startDate?.substring(5)}至${queryDate.value.endDate?.substring(5)}`
    const typeName = typeObj.value.name
    if (typeObj.value == "inOutData") {
        return data.value?.inOutData?.errorCount == 0 ? `你${time}的${typeName}考勤无异常记录，太棒啦～` : `你的${time}的${typeName}考勤有异常记录！`
    } else {
        return data.value?.classroomData?.errorCount == 0 ? `你${time}的${typeName}考勤无异常记录，太棒啦～` : `你的${time}的${typeName}考勤有异常记录！`
    }
})

const chartData = ref({})
const defaultData = ref({
    categories: ["迟到", "早退", "请假", "缺勤"],
    series: [
        {
            textOffset: 10,
            data: [
                { color: "#FC941F", legendText: "迟到", name: "迟到", value: 0 },
                { color: "#1EC1C3", legendText: "早退", name: "早退", value: 0 },
                { color: "#333333", legendText: "请假", name: "请假", value: 0 },
                { color: "#FD4F45", legendText: "缺勤", name: "缺勤", value: 0 }
            ]
        }
    ]
})

const closeType = (val) => {
    if (!val && val.value == typeObj.value.value) return
    typeObj.value = val
    setData()
}

function setData() {
    setTimeout(() => {
        const dataVal = data.value
        const obj = typeObj.value.value == "course" ? dataVal.classroomData : dataVal.inOutData
        const chartDataNum = {
            1: obj?.lateCount || 0,
            2: obj?.leaveEarlyCount || 0,
            3: obj?.leaveCount || 0,
            4: obj?.absenceCount || 0
        }
        defaultData.value.series[0].data.forEach((item, index) => {
            item.value = Number(chartDataNum[index + 1])
            item.legendText = `${defaultData.value.categories[index]}: ${Number(chartDataNum[index + 1])} 次`
        })
        chartOpts.subtitle.name = typeObj.value.value == "course" ? `${dataVal?.classroomAttendance || 0}%` : `${dataVal?.inOutAttendance || 0}%`
        chartData.value = defaultData.value // 图标需要一次性造好数据赋值 ，不然会一闪一闪的
    }, 500)
}

function goDetails() {
    navigateTo({
        url: `/apps/weeklyPublication/attendanceWeekly/details`
    })
}

defineExpose({ setData })
</script>

<style lang="scss" scoped>
.student_attendance {
    background: #ffffff;
    box-shadow: 0rpx 8rpx 8rpx 0rpx rgba(220, 245, 238, 0.5);
    border-radius: 20rpx;
    padding: 30rpx 24rpx;

    .title_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
        .select_type {
            padding: 0rpx 24rpx;
            height: 48rpx;
            background: #f3fcf9;
            border-radius: 24rpx;
            display: flex;
            align-items: center;
            .type {
                font-weight: 500;
                font-size: 26rpx;
                color: #1e1e1e;
                line-height: 35rpx;
            }
        }
        .right {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 26rpx;
            color: $uni-color-primary;
            line-height: 36rpx;
        }
    }
    .attendance_tip {
        background: #f6f6f6;
        padding: 12rpx;
        border-radius: 8rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        .image {
            width: 22rpx;
            height: 28rpx;
            margin-right: 16rpx;
        }
        .text {
            font-weight: 400;
            font-size: 24rpx;
            color: #909399;
            line-height: 36rpx;
            flex: 1;
        }
    }
    .no_error {
        margin: 40rpx 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .image {
            width: 308rpx;
            height: 250rpx;
        }
    }
    .attendance_chart {
        position: relative;

        .date_picker_text {
            position: absolute;
            top: 90rpx;
            right: 20rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            line-height: 34rpx;
        }
    }
}
</style>

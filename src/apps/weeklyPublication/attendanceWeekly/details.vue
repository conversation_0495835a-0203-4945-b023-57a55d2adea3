<template>
    <view class="weekly_details">
        <uni-nav-bar statusBar fixed left-icon="left" title="考勤详情" :border="false" @clickLeft="clickLeft"> </uni-nav-bar>

        <!-- 筛选条件 -->
        <select-filter ref="filterRef" @changeDate="changeDate">
            <template #left>
                <!-- 选择班级 -->
                <div class="select_classes" v-if="identityType == 'teacher'">
                    <uni-data-picker v-model="classesId" :localdata="classList" @change="onChangeClass" :map="{ text: 'showName', value: 'id' }" v-slot:default="{ data, error }" popup-title="请选择班级">
                        <view v-if="error" class="error">
                            <text>{{ error }}</text>
                        </view>
                        <view v-else-if="data.length" class="selected">
                            {{ data[data.length - 1]?.text }}
                        </view>
                        <view v-else>
                            <text>请选择</text>
                        </view>
                    </uni-data-picker>
                </div>
                <!-- 选择学生 -->
                <view class="select_student" v-else @click="selectStudentRef.open()"> {{ studentObj.studentName || "选择学生" }} </view>
            </template>
        </select-filter>

        <!-- 考勤统计卡片 -->
        <view class="statistics">
            <attendance-card :info="info" />
        </view>
        <!-- 出入校和课程切换 -->

        <view class="attendance_data">
            <uv-tabs :list="tabs" :current="activeTab" @click="clickTabs" :activeStyle="{ color: '#00b781' }" :inactiveStyle="{ color: '#606266' }" lineWidth="20" :customStyle="{ background: '#fff' }" lineColor="#00b781" :scrollable="false"></uv-tabs>
        </view>
        <!-- 考勤统计图表 -->
        <view class="charts_title"> {{ queryDate?.startDate }}至{{ queryDate?.endDate }} {{ { 0: "出入校", 1: "课程" }[activeTab] }}考勤 </view>
        <attendance-charts :data="chartsData" />
        <!-- 考勤人员列表 -->
        <view class="personnel_list">
            <view class="status_list">
                <view v-for="(item, index) in statusList" :key="index" @click="clickStatus(item)">
                    <view class="status_item" :class="{ active_item: status == item.value }">
                        {{ item.name }}
                    </view>
                </view>
            </view>
        </view>
        <personnel-list :list="list" />
        <!-- 学生列表 -->
        <yd-select-popup v-if="identityType != 'teacher'" ref="selectStudentRef" title="请选择学生" :list="studentList" @closePopup="closeStudent" :fieldNames="{ value: 'studentId', label: 'studentName' }" :selectId="[studentObj.studentId]" />
    </view>
</template>

<script setup>
import personnelList from "../components/personnelList.vue"
import AttendanceCharts from "../components/attendanceCharts.vue"
import SelectFilter from "../components/selectFilter.vue"
import AttendanceCard from "../components/attendanceCard.vue"
import useStore from "@/store"
import { getLastChildId, tabs, identityType, tabsId, statusList } from "../data"

const { user } = useStore()
const selectStudentRef = ref(null)
const filterRef = ref(null)
const classList = ref([])
const classesId = ref("")
const studentObj = ref({})
const studentList = ref([])
const queryDate = ref({})
const status = ref(1)
const info = ref({})
const activeTab = ref(0)
const chartsData = ref({})
const tabValue = ref("goOutSchool")
const list = ref([])

const identityQuery = computed(() => {
    return identityType.value == "teacher" ? { classesId: classesId.value } : { studentId: studentObj.value.studentId }
})

const chartsLabel = computed(() => {
    return {
        0: info.value.inOutData,
        1: info.value.classroomData
    }
})

// 获取统计数据
function getInfo() {
    const query = { ...queryDate.value, ...identityQuery.value }
    http.post("/app/attendance/studentAttendance", query).then((res) => {
        info.value = res.data
        chartsData.value = chartsLabel.value[activeTab.value]
    })
}

// 获取人员列表
function getList() {
    const params = {
        type: tabsId[tabValue.value],
        status: status.value,
        ...identityQuery.value,
        ...queryDate.value
    }
    http.post("/app/attendance/studentViewDetail", params).then((res) => {
        list.value = res.data
    })
}

// 点击选择班级
function onChangeClass(res) {
    const selectList = res.detail.value
    classesId.value = selectList[selectList.length - 1].value
    getInfo()
    getList()
}

// 选择学生
function closeStudent(val) {
    if (!val && val.studentId == studentObj.value.studentId) return
    studentObj.value = val
    getInfo()
    getList()
}

// 切换考勤状态
function clickStatus(item) {
    status.value = item.value
    getList()
}

// 切换日期
function changeDate(date) {
    queryDate.value = date
    getInfo()
    getList()
}

// 获取班级
async function getClassList() {
    await http.get("/app/timetable/getClassList").then(async (res) => {
        classList.value = res.data
        if (res.data && res.data.length) {
            classesId.value = await getLastChildId(res.data[0])
        }
    })
}

function clickTabs(item) {
    activeTab.value = item.index
    tabValue.value = item.value
    chartsData.value = chartsLabel.value[activeTab.value]
    getList()
}

onMounted(async () => {
    if (identityType.value == "teacher") {
        await getClassList()
    } else {
        studentList.value = user.studentInfo
        studentObj.value = user.studentInfo[0]
    }
    queryDate.value = await filterRef.value.getDate()
    getInfo()
    getList()
})

function clickLeft() {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.weekly_details {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .select_classes {
        :deep(.selected) {
            display: flex;
        }
    }
    .statistics {
        padding-bottom: 30rpx;
        background: $uni-bg-color;
    }
    .attendance_data {
        background: $uni-bg-color;
        margin-top: 30rpx;
    }
    .charts_title {
        padding: 30rpx 30rpx 0 30rpx;
        background: $uni-bg-color;
        font-weight: 500;
        font-size: 30rpx;
        color: #000000;
        line-height: 42rpx;
    }
    .personnel_list {
        margin-top: 30rpx;
        background: $uni-bg-color;
        padding: 30rpx;

        .status_list {
            display: flex;
            align-items: center;
            overflow-x: auto;
            max-width: 100%;
            .status_item {
                padding: 0rpx 30rpx;
                height: 56rpx;
                background: #f9faf9;
                border-radius: 4rpx;
                font-size: 28rpx;
                color: #999999;
                line-height: 56rpx;
                text-align: center;
                margin-right: 20rpx;
                border: 1rpx solid #f9faf9;
            }
            .active_item {
                background: #ffffff;
                border: 1rpx solid $uni-color-primary;
                color: $uni-color-primary;
            }
        }
    }
}
</style>

<template>
    <div class="album_detail_page">
        <z-paging ref="paging" v-model="dataList" @query="queryList" loading-more-no-more-text="已显示全部">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="校园风采"> </uni-nav-bar>
                <div class="header">
                    <span class="num">共{{ total || 0 }}张</span>
                    <span class="cancel_operate" v-if="listType == 'operate' && isTeacher" @click="cancelOperate">取消</span>
                    <span v-if="params.name != '未分组' && listType != 'operate' && isTeacher" class="update" @click="updateAlbum">编辑相册</span>
                </div>
            </template>
            <div class="detail_content">
                <div v-if="listType == 'operate'" class="operate_list">
                    <uv-checkbox-group activeColor="#00b781" shape="circle" v-model="checkboxValue">
                        <div class="list">
                            <uv-checkbox v-for="(item, index) in dataList" :key="index" :label="item.id" :name="item.id">
                                <image mode="aspectFit" class="image_class" :src="item.url" alt="" />
                                <div class="shadow_mask"></div>
                            </uv-checkbox>
                        </div>
                    </uv-checkbox-group>
                </div>
                <div class="list" v-else>
                    <div class="item" @touchstart="gtouchstart" @touchmove="gtouchmove" @touchend="showDeleteButton(index)" v-for="(item, index) in dataList" :key="item.id">
                        <image @click="previewImage(index)" mode="aspectFit" class="image_class" :src="item.url" alt="" />
                    </div>
                </div>
                <div class="uploadImage" @click="handleUpload" v-if="listType != 'operate' && isTeacher">
                    <uni-icons type="plusempty" size="30" color="#fff"></uni-icons>
                </div>
            </div>
            <template #empty>
                <slot name="empty">
                    <yd-empty text="暂无数据" />
                </slot>
            </template>
            <template #bottom>
                <div class="operate_btn" v-if="listType == 'operate'">
                    <uv-checkbox-group activeColor="#00b781" shape="circle" v-model="isAll">
                        <uv-checkbox :name="0" @change="changeAll">
                            <div class="select_class">
                                <div class="all_class">全选</div>
                                <div class="select_text">
                                    已选 <span class="num">{{ checkboxValue.length || 0 }}</span> 张
                                </div>
                            </div>
                        </uv-checkbox>
                    </uv-checkbox-group>
                    <div class="btn_box">
                        <button class="btn_class primary_btn" @click="handleMove">移动</button>
                        <button class="btn_class delete_btn" @click="handleDelete" :loading="deleteLoading" :disabled="deleteLoading">删除</button>
                    </div>
                </div>
            </template>
        </z-paging>
        <!-- 移动相册弹框 -->
        <yd-select-popup title="移动至" ref="selectPopupRef" :list="albumList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeAlbum" />
        <!-- 确认删除框 -->
        <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
            <view style="padding: 33px 0px 10px 0px"> 确定删除已选的{{ checkboxValue.length || 0 }}张照片？ </view>
        </yd-popup>
    </div>
</template>

<script setup>
import useStore from "@/store"

import { onLoad } from "@dcloudio/uni-app"
const { user } = useStore()
const dataList = ref([]) // 相册图片列表
const listType = ref("read") // read只读模式 / operate编辑模式
const confirmRef = ref(null) // 删除前二次弹框
const checkboxValue = ref([]) // 编辑时多选选中项
const paging = ref(null) // 图片列表
const total = ref(0) // 图片总数
const params = ref({}) // 相册参数
const deleteLoading = ref(false) // 删除按钮loading
const isAll = ref([]) // 全选值
const selectPopupRef = ref(null) // 选择移动相册的弹框
const albumList = ref([]) // 选择移动相册的列表
const timeOutEvent = ref(null) // 节流事件

const isTeacher = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    return !["eltern", "student"].includes(roleCode)
})

// 图片列表
function queryList(pageNo, pageSize) {
    http.post("/brand/album/page", { ...params.value, pageNo, pageSize }).then(({ data }) => {
        paging.value.complete(data.list)
        total.value = data.total
    })
}

// 编辑相册
function updateAlbum() {
    navigateTo({
        url: "/apps/campusStyle/updateAlbum",
        query: {
            id: params.value.id,
            isShow: params.value.isShow,
            name: params.value.name
        }
    })
}

function clickLeft() {
    uni.navigateBack()
}

// 上传图片
function handleUpload() {
    uni.chooseImage({
        count: 1,
        success: (res) => {
            console.log(res)
            http.uploadFile("/file/common/upload", res.tempFiles[0].path, { folderType: "app" }).then((url) => {
                if (url) {
                    const obj = {
                        classesId: params.value.classesId,
                        classifyId: params.value.id,
                        kind: params.value.kind,
                        type: params.value.type,
                        urls: [url]
                    }
                    http.post("/brand/album/create", obj).then((res) => {
                        uni.showToast({ title: res.message, icon: "none" })
                        paging.value.reload()
                    })
                }
            })
        }
    })
}

// 预览图片
function previewImage(index) {
    const urls = dataList.value.map((item) => item.url) || []
    uni.previewImage({
        urls,
        current: index
    })
}
//真正长按后应该执行的内容
function longPress() {
    console.log("长按")
    timeOutEvent.value = 0
    if (!isTeacher.value) return
    //执行长按要执行的内容，如弹出菜单
    listType.value = "operate"
}

// 松开鼠标
async function showDeleteButton(index) {
    clearTimeout(timeOutEvent.value) //清除定时器
    if (timeOutEvent.value != 0) {
        console.log("点击")
        //这里写要执行的内容（如onclick事件）
    }
    return false
}

// 如果手指有移动，则取消所有事件，此时说明用户只是要移动而不是长按
function gtouchmove() {
    clearTimeout(timeOutEvent.value) //清除定时器
    timeOutEvent.value = 0
}

//长按事件（起始）
function gtouchstart() {
    timeOutEvent.value = setTimeout(function () {
        longPress()
    }, 500) //这里设置定时器，定义长按500毫秒触发长按事件
    return false
}

// 取消编辑
async function cancelOperate() {
    listType.value = "read"
    checkboxValue.value = []
    isAll.value = []
}

// 编辑全选图片
function changeAll(item) {
    if (item) {
        checkboxValue.value = dataList.value.map((item) => item.id)
    } else {
        checkboxValue.value = []
    }
}

// 移动的相册
function closeAlbum(item) {
    if (item) {
        // 移动相册
        http.post("/brand/album/updateClassify", {
            classifyId: item.id,
            ids: checkboxValue.value || []
        }).then(async (res) => {
            await cancelOperate()
            paging.value.reload()
            setTimeout(() => {
                uni.showToast({ title: res.message, icon: "none" })
            }, 500)
        })
    }
}

// 删除确认弹框
function dialogConfirm() {
    deleteLoading.value = true
    http.post("/brand/album/delete", { ids: checkboxValue.value || [] })
        .then(async (res) => {
            await cancelOperate()
            paging.value.reload()
            setTimeout(() => {
                uni.showToast({ title: res.message, icon: "none" })
            }, 500)
        })
        .finally(() => {
            deleteLoading.value = false
        })
}

// 删除按钮
function handleDelete() {
    if (checkboxValue.value && checkboxValue.value.length > 0) {
        confirmRef.value.open()
    } else {
        uni.showToast({
            title: "请选择要删除的图片",
            icon: "none"
        })
    }
}

// 移动相册按钮
function handleMove() {
    if (checkboxValue.value && checkboxValue.value.length > 0) {
        if (albumList.value && albumList.value.length > 0) {
            selectPopupRef.value.open()
        } else {
            uni.showToast({
                title: "暂无可移动的相册！",
                icon: "none"
            })
        }
    } else {
        uni.showToast({
            title: "请选择要移动的图片",
            icon: "none"
        })
    }
}

watch(
    () => checkboxValue.value,
    (val) => {
        if (val.length == dataList.value.length) {
            isAll.value = [0]
        } else {
            isAll.value = []
        }
    }
)

onLoad((option) => {
    Object.keys(option).forEach((key) => {
        option[key] = decodeURIComponent(option[key])
    })
    params.value = option
    params.value.classifyId = option.id
    // 获取移动相册列表
    http.post("/brand/album/classify/list", {
        kind: option.kind,
        classesId: option.classesId
    }).then((res) => {
        albumList.value = res.data.filter((i) => i.id != option.id)
    })
})
</script>

<style lang="scss" scoped>
.album_detail_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
}

.detail_content {
    padding: 0rpx 30rpx 30rpx 30rpx;
}
.header {
    padding: 30rpx 30rpx 0rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .num {
        font-weight: 500;
        font-size: 28rpx;
        color: $uni-text-color;
        line-height: 40rpx;
    }
    .update {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-color-primary;
        line-height: 40rpx;
        text-align: right;
    }
    .cancel_operate {
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-color-primary;
        line-height: 40rpx;
        text-align: right;
    }
}
.list {
    display: grid;
    width: 100%;
    /* 设置为4列，每列宽度相等 */
    grid-template-columns: repeat(3, 1fr);
    gap: 10rpx; /* 单元格之间的间距 */
    margin-top: 20rpx;
    .item {
        border: 1rpx solid $uni-border-color;
        width: 210rpx;
        height: 210rpx;
        position: relative;
    }
}
.uploadImage {
    position: fixed;
    bottom: 226rpx;
    right: 30rpx;
    width: 112rpx;
    height: 112rpx;
    background: $uni-color-primary;
    box-shadow: 0rpx 8rpx 8rpx 0rpx #11c68533;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
}
.operate_list {
    :deep(.uv-checkbox__icon-wrap) {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        z-index: 99;
    }
    :deep(.uv-checkbox__label-wrap) {
        padding: 0rpx;
    }
    :deep(.uv-checkbox) {
        border: 1rpx solid $uni-border-color;
        width: 210rpx;
        height: 210rpx;
        position: relative;
    }
}

.image_class {
    height: 210rpx;
    width: 210rpx;
}
.shadow_mask {
    height: 210rpx;
    width: 210rpx;
    position: absolute;
    top: 0rpx;
    left: 0rpx;
    z-index: 98;
    background: #00000050;
}
.operate_btn {
    padding: 30rpx 30rpx 60rpx 30rpx;
    height: 50rpx;
    display: flex;
    background: $uni-bg-color;
    align-items: center;
    justify-content: space-between;
    .select_class {
        display: flex;
        align-items: center;
        .all_class {
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-text-color;
            line-height: 40rpx;
        }
        .select_text {
            font-weight: 400;
            font-size: 24rpx;
            color: #666666;
            line-height: 34rpx;
            padding-left: 30rpx;
            .num {
                color: $uni-color-primary;
            }
        }
    }

    .btn_box {
        display: flex;
        justify-content: flex-end;
        .btn_class {
            min-width: 112rpx;
            height: 60rpx;
            font-weight: 400;
            font-size: 26rpx;
            line-height: 60rpx;
            text-align: center;
            background: $uni-bg-color;
        }
        .primary_btn {
            color: $uni-color-primary;
            border: 1rpx solid $uni-color-primary;
        }
        .delete_btn {
            color: $uni-color-error;
            border: 1rpx solid $uni-color-error;
            margin-left: 20rpx;
        }
    }
}
</style>

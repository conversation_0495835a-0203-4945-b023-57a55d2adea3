<template>
    <view class="table_list">
        <view class="item_tr">
            <view
                class="item_td"
                :class="{
                    left_class: index == 0,
                    right_class: index == tr.length - 1
                }"
                v-for="(item, index) in tr"
                :key="item.key"
            >
                {{ item.label }}
            </view>
        </view>
        <view v-if="list && list.length">
            <view class="list_item" v-for="(item, index) in list" :key="index">
                <view
                    class="item_td"
                    v-for="(tIt, tIx) in tr"
                    :key="tIx"
                    :class="{
                        left_class: tIx == 0,
                        right_class: tIx == tr.length - 1
                    }"
                >
                    <text v-if="tIt.key == 'status'" :style="{ color: statueColor[item[tIt.key]] }" class="ellipsis">
                        {{ statusText[item[tIt.key]] }}
                    </text>
                    <text v-else class="ellipsis">
                        {{ item[tIt.key] }}
                    </text>
                </view>
            </view>
        </view>
        <yd-empty v-else :isMargin="true" />
    </view>
</template>

<script setup>
import { computed } from "vue"

const props = defineProps({
    type: {
        type: [String, Number],
        default: 0
    },
    list: {
        type: Array,
        default: () => []
    }
})

const statueColor = {
    0: "#00b781",
    1: "#F5222D",
    2: "#FC941F",
    3: "#1EC1C3",
    4: "#00b781",
    5: "#717171",
    6: "#595959"
}

const statusText = {
    0: "正常",
    1: "缺勤",
    2: "迟到",
    3: "早退",
    4: "无效打卡",
    5: "请假",
    6: "未签到"
}

const eventTr = [
    {
        label: "次数",
        key: "sequence"
    },
    {
        label: "姓名",
        key: "studentName"
    },
    {
        label: "班级",
        key: "classesName"
    },
    {
        label: "打卡时段",
        key: "periodOfTime"
    },
    {
        label: "状态",
        key: "status"
    }
]

const goOutSchoolTr = [
    {
        label: "姓名",
        key: "studentName"
    },
    {
        label: "班级",
        key: "classesName"
    },
    {
        label: "名称",
        key: "attendanceName"
    },
    {
        label: "打卡时段",
        key: "periodOfTime"
    },
    {
        label: "状态",
        key: "status"
    }
]

const courseTr = [
    {
        label: "课次",
        key: "sectionName"
    },
    {
        label: "科目",
        key: "attendanceName"
    },
    {
        label: "姓名",
        key: "studentName"
    },
    {
        label: "打卡时段",
        key: "periodOfTime"
    },
    {
        label: "状态",
        key: "status"
    }
]

const list = computed(() => props.list)
const tr = computed(() => {
    const obj = {
        0: goOutSchoolTr,
        1: eventTr,
        2: courseTr
    }
    return obj[props.type]
})
</script>

<style lang="scss" scoped>
.table_list {
    background: $uni-bg-color;
    padding: 16rpx 30rpx;
    .list_item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 400;
        font-size: 24rpx;
        color: #606266;
        line-height: 34rpx;
        padding: 0 20rpx;
        .item_td {
            height: 32rpx;
            text-align: center;
            flex: 1;
            padding: 32rpx 0;
            border-bottom: 1rpx solid $uni-border-color;
        }
    }
    .item_tr {
        display: flex;
        align-items: center;
        background: #f3fcf9;
        border-radius: 10rpx;
        padding: 20rpx;

        .item_td {
            flex: 1;
            font-weight: 400;
            font-size: 28rpx;
            color: $uni-text-color-grey;
            line-height: 40rpx;
            text-align: center;
        }
    }
    .left_class {
        text-align: left !important;
    }
    .right_class {
        text-align: right !important;
    }
}
</style>

<template>
    <view class="select_status">
        <view class="status_box">
            <text class="status_label">选择类型</text>
            <view class="status_list">
                <view class="status_item" v-for="item in list" :key="item.value" :class="{ active_class: activeStatus == item.value }" @click="changeTypeFn(item)">
                    {{ item.label }}
                </view>
            </view>
        </view>
    </view>
</template>

<script setup>
import { computed } from "vue"

const emit = defineEmits(["changeStatus"])
const props = defineProps({
    list: {
        type: Array,
        default: () => []
    },
    activeStatus: {
        type: Number,
        default: null
    }
})

const list = computed(() => props.list)
const activeStatus = computed(() => props.activeStatus)

function changeTypeFn(item) {
    emit("changeStatus", item)
}
</script>

<style lang="scss" scoped>
.select_status {
    margin: 10rpx 30rpx;
    .status_box {
        display: flex;
        padding: 10rpx;
        align-items: center;
        .status_label {
            font-weight: 400;
            font-size: 28rpx;
            color: #666666;
            line-height: 40rpx;
            margin-right: 28rpx;
        }
        .status_list {
            display: flex;
            flex: 1;
            align-items: center;
            overflow-x: auto;
            .status_item {
                min-width: 100rpx;
                flex: 1;
                height: 56rpx;
                border-radius: 28rpx;
                margin: 0 10rpx;
                border: 1rpx solid $uni-border-color;
                font-weight: 400;
                font-size: 26rpx;
                color: $uni-text-color-grey;
                line-height: 56rpx;
                text-align: center;
            }
            .active_class {
                border: 1rpx solid $uni-color-primary;
                color: $uni-color-primary;
            }
        }
    }
}
</style>

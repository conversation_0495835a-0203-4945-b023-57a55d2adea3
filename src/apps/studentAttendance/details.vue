<template>
    <view class="details">
        <uni-nav-bar statusBar fixed left-icon="left" :title="pageTitle" :border="false" @clickLeft="routerBack"> </uni-nav-bar>
        <view class="page_details">
            <view class="bg_box">
                <view class="select_box" v-if="query.type != 0 && identityType == 'teacher'">
                    <text class="select_title">选择人员类型</text>
                    <view class="select_condition" @click="selectUserTypeRef.open()">
                        {{ userTypeTitle[userType] }}
                    </view>
                </view>
                <view class="select_box" v-if="query.type == 1">
                    <text class="select_title">选择事件</text>
                    <view class="select_condition" @click="selectEventRef.open()">
                        {{ eventItem.name || "选择事件" }}
                    </view>
                </view>
                <view class="select_box" v-if="query.type == 2">
                    <text class="select_title">选择课堂考勤类型</text>
                    <view class="select_condition" @click="selectCourseRef.open()">
                        {{ courseType.label || "选择课堂考勤类型" }}
                    </view>
                </view>
                <!-- 选择日期时间 -->
                <select-date @changeDate="changeDate" />
                <!-- 选择视图（年级班级） -->
                <select-grade-class v-if="query.type != 1 && identityType == 'teacher'" :gradeClassList="gradeClassList" @changeData="changeGradeClass" />
            </view>
            <!-- 选择类型 -->
            <select-status :list="statusList" @changeStatus="changeStatus" :activeStatus="ydStatus" />
            <table-list :type="query.type" :list="viewDetail" />
        </view>
        <!-- 选择人员类型 -->
        <yd-select-popup title="请选择人员类型" ref="selectUserTypeRef" :list="userTypeList" @closePopup="closeUserType" :selectId="[userType]" />

        <!-- 选择事件 -->
        <yd-select-popup title="请选择事件" ref="selectEventRef" :list="eventList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeEvent" :selectId="[eventItem.id]" />

        <!-- 选择课堂考勤类型 -->
        <yd-select-popup title="请选择课堂考勤类型" ref="selectCourseRef" :list="courseTypeList" @closePopup="closeCourseType" :selectId="[courseType.value]" />
    </view>
</template>

<script setup>
import useStore from "@/store"
import TableList from "./components/tableList.vue"
import SelectGradeClass from "./components/selectGradeClass.vue"
import SelectStatus from "./components/selectStatus.vue"
import SelectDate from "./components/selectDate.vue"
import { typeTitle, userTypeTitle, userTypeList, courseTypeList } from "./data"
const query = ref({})
const selectUserTypeRef = ref(null)
const selectEventRef = ref(null)
const selectCourseRef = ref(null)
const userType = ref(1)
const eventList = ref([])
const eventItem = ref({})
const gradeClassList = ref([])
const statusList = ref([])
const grade = ref({})
const classes = ref({})
const ydStatus = ref(null)
const viewDetail = ref([])
const courseType = ref({ value: 2, label: "行政班课堂考勤" })
const timeObj = ref({
    startDate: "",
    endDate: ""
})
const { user } = useStore()

const pageTitle = computed(() => {
    return `${typeTitle[query.value.type]}考勤明细` || "考勤明细"
})

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})
async function getData() {
    const params = {
        ...timeObj.value,
        type: query.value.type,
        status: ydStatus.value
    }
    if (identityType.value == "teacher") {
        // 出入校和课程要传班级id和年级id
        if (query.value.type != 1) {
            params.rollId = grade.value.id || null
            params.classesId = classes.value.value || null
        }
        // 课程选择人员类型和课堂考勤类型
        if (query.value.type == 2) {
            params.userType = userType.value
            params.type = courseType.value.value || query.value.type
        }
    } else {
        // 家长传学生
        params.studentId = query.value.studentId
        if (query.value.type == 2) {
            params.type = courseType.value.value || query.value.type
        }
    }
    // 事件传人员类型和时间ID
    if (query.value.type == 1) {
        await getEventList()
        params.userType = userType.value
        params.attendanceId = eventItem.value.id || null
    }
    http.post("/attweb/app/eventStatistical/studentViewDetail", params).then((res) => {
        viewDetail.value = res.data
    })
    console.log(params)
}

// 选择人员类型
function closeUserType(item) {
    if (!item || item.value == userType.value) return
    userType.value = item.value
    getData()
}

// 选择事件
function closeEvent(item) {
    if (!item || item.id == eventItem.value.id) return
    eventItem.value = item
    getData()
}

// 获取事件下拉数据
async function getEventList() {
    await http
        .post("/attweb/app/eventStatistical/attendanceEventList", {
            ...timeObj.value,
            userType: userType.value
        })
        .then((res) => {
            eventList.value = res.data
        })
}

// 选择时间
function changeDate(time) {
    timeObj.value = time
    getData()
}

// 选择年级/班级
function changeGradeClass({ ydGrade, ydClasses }) {
    console.log(ydGrade, ydClasses)
    grade.value = ydGrade
    classes.value = ydClasses
    getData()
}

function changeStatus(myStatus) {
    ydStatus.value = myStatus.value
    getData()
}

onLoad((option) => {
    Object.keys(option).forEach((key) => {
        option[key] = decodeURIComponent(option[key])
    })
    query.value = option
})

// 选择课堂考勤类型
function closeCourseType(item) {
    if (!item || item.value == courseType.value) return
    courseType.value = item
    getData()
}

function getGradeClass() {
    http.get("/app/timetable/getClassList", { AppCode: "studentAttendance" }).then((res) => {
        gradeClassList.value = res.data
    })
}

async function getStatusList() {
    await http.post("/cloud/SystemDict/get", ["attendance_status"]).then((res) => {
        statusList.value = [{ value: null, label: "全部" }].concat(res.data[0]?.list || [])
        ydStatus.value = statusList.value[0]?.value
    })
}

onMounted(async () => {
    await getStatusList()
    if (identityType.value == "teacher") {
        getGradeClass()
    }
})
</script>

<style lang="scss" scoped>
.details {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .page_details {
        .bg_box {
            margin-top: 20rpx;
            background: $uni-bg-color;
            padding: 0rpx 20rpx 30rpx 20rpx;
        }
        .select_box {
            display: flex;
            align-items: center;
            padding: 20rpx 0;
            justify-content: space-between;
            border-bottom: 1rpx solid $uni-border-color;
            .select_title {
                font-weight: 400;
                font-size: 28rpx;
                color: #666666;
                line-height: 40rpx;
            }
            .select_condition {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;

                &::after {
                    content: "";
                    display: block;
                    border: 10rpx solid transparent;
                    border-top: 10rpx solid $uni-color-primary;
                    border-bottom-width: 1px;
                    margin-left: 10rpx;
                }
            }
        }
    }
    :deep(.date_box) {
        padding-top: 20rpx;
    }
}
</style>

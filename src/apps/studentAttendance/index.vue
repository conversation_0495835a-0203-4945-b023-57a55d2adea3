<template>
    <yd-page-view class="student_attendance" title="考勤数据" :hideBottom="true" :hideLeft="false" :leftWidth="70" :rightWidth="70">
        <!-- #ifdef MP-WEIXIN-->
        <template v-slot:title>
            <view class="right_class right_class_title" v-if="identityType != 'teacher'" @click="selectStudent">
                <text class="ellipsis">{{ studentObj.name }}</text>
                <text>的考勤数据</text>
            </view>
        </template>
        <!-- #endif -->
        <!-- #ifdef H5 || H5-WEIXIN-->
        <template v-slot:right>
            <view class="right_class" v-if="identityType != 'teacher'" @click="selectStudent">
                <span class="text ellipsis">{{ studentObj.name }}</span>
                <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
            </view>
        </template>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <template v-slot:right>
            <view class="right_class ellipsis" @click="selectStudent" v-if="identityType != 'teacher'">
                <span class="text">{{ studentObj.name }}</span>
                <img class="image" src="@nginx/workbench/moralEducationEvaluation/selectWeek.png" alt="" />
            </view>
        </template>
        <!-- #endif -->
        <uv-tabs :list="tabs" :current="activeTab" @click="clickTabs" :activeStyle="{ color: '#00b781' }" :inactiveStyle="{ color: '#606266' }" lineWidth="20" :customStyle="{ background: '#fff' }" lineColor="#00b781" :scrollable="false"></uv-tabs>
        <!-- 考勤 -->
        <scroll-view v-if="tabs && tabs.length > 0" :enable-flex="true" :scroll-with-animation="true" @scroll="handleScroll" :scroll-y="true" :scroll-top="scrollTop" :scroll-into-view="scrollIntoViewId" class="attendance_box">
            <view class="attendance" v-for="item in tabs" :key="item.value" :id="`${item.value}_attendance`">
                <attendance :identityType="identityType" :type="item.type" :gradeClassList="gradeClassList" :student="studentObj" />
            </view>
        </scroll-view>
    </yd-page-view>
    <yd-select-popup v-if="identityType != 'teacher'" title="请选择事件" ref="selectStudentRef" :list="studentList" :fieldNames="{ value: 'id', label: 'name' }" @closePopup="closeStudent" :selectId="[studentObj.id]" />
</template>

<script setup>
import useStore from "@/store"
import attendance from "./components/attendance.vue"
import { tabs } from "./data"

const selectStudentRef = ref(null)
const activeTab = ref(0) // tabs的选中下标
const gradeClassList = ref([])

const scrollIntoViewId = ref("")
const scrollTop = ref(0)
const instance = getCurrentInstance().proxy
const statusBarHeight = ref(0)

const studentList = ref([])
const studentObj = ref({})
const { user } = useStore()

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

// 节流函数
function throttle(fn, delay) {
    let lastCall = 0
    return function (...args) {
        const now = new Date().getTime()
        if (now - lastCall < delay) {
            return
        }
        lastCall = now
        return fn(...args)
    }
}

function selectStudent() {
    selectStudentRef.value.open()
}

function closeStudent(val) {
    if (!val && val.id == studentObj.id) return
    studentObj.value = val
}

function getStudent() {
    http.get("/app/student/getStudentList").then((res) => {
        studentList.value = res.data
        studentObj.value = res.data[0]
    })
}

function getGradeClass() {
    http.get("/app/timetable/getClassList", { AppCode: "studentAttendance" }).then((res) => {
        gradeClassList.value = res.data
    })
}

function setScroll() {
    tabs.value.forEach((item, index) => {
        const query = uni.createSelectorQuery().in(instance)
        query
            .select(`#${item.value}_attendance`)
            .boundingClientRect(function (data) {
                if (data && data.top > 88 && data.top < 200) {
                    activeTab.value = index
                }
            })
            .exec()
    })
}

const handleScroll = throttle(setScroll, 100)

function clickTabs(item) {
    const query = uni.createSelectorQuery().in(instance)
    query
        .select(`#${item.value}_attendance`)
        .fields({ rect: true }, function (data) {
            console.log(scrollTop.value, data.top)
            if (data) {
                // #ifdef H5-WEIXIN || H5
                scrollTop.value = scrollTop.value + (data.top - 88)
                // #endif
                // #ifdef MP-WEIXIN || APP-PLUS
                scrollTop.value = scrollTop.value + (data.top - statusBarHeight.value - 88)
                // #endif
            }
        })
        .exec()
}

onLoad(() => {
    uni.getSystemInfo({
        success: (res) => {
            // 获取手机顶部状态栏的高度
            statusBarHeight.value = res.statusBarHeight || 0
        }
    })
})

onMounted(() => {
    if (identityType.value != "teacher") {
        getStudent()
    } else {
        getGradeClass()
    }
})
</script>

<style lang="scss" scoped>
.student_attendance {
    background-color: $uni-bg-color-grey;
    min-height: 100vh;
    .attendance_box {
        max-height: calc(100vh - 176rpx);
        height: calc(100vh - 176rpx);
        overflow-y: auto;
        width: 100%;
    }
}
.right_class {
    display: flex;
    justify-content: flex-end;
    height: 100%;
    align-items: center;

    .text {
        font-size: 28rpx;
        font-weight: 400;
        color: $uni-text-color;
        line-height: 40rpx;
    }

    .image {
        width: 44rpx;
        height: 44rpx;
    }
}
.right_class_title {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 34rpx;
    color: $uni-text-color;
    line-height: 48rpx;
    &::after {
        content: "";
        display: block;
        border: 10rpx solid transparent;
        border-top: 10rpx solid $uni-color-primary;
        border-bottom-width: 1px;
        margin-left: 10rpx;
    }
    .ellipsis {
        flex: 1;
    }
}
</style>

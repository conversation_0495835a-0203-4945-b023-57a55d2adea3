<template>
    <div>
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe class="webview" ref="myIfarmeRef" :src="url" frameborder="0"></iframe>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS -->
        <web-view class="webview" ref="myIfarmeRef" :src="url"></web-view>
        <!-- #endif -->
    </div>
</template>

<script setup>
import { getToken } from "@/utils/storageToken.js"
import useStore from "@/store"
const { user } = useStore()
const myIfarmeRef = ref()
const url = ref("")

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "parent"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

// 使用Html5QrCode扫码并获取结果
const openScanQRCode = () => {
    console.log("使用Html5QrCode扫码并获取结果")
}

// 处理扫码结果
const handleScanResult = (result) => {
    console.log("接收到扫码结果:", result)
    // 将扫码结果发送给iframe内部页面
    if (myIfarmeRef.value) {
        myIfarmeRef.value.contentWindow.postMessage({ scanResult: true, code: result }, "*")
    }
}

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    const token = getToken()?.replace("Bearer ", "")
    const ydUrl = `${import.meta.env.VITE_BASE_LIBRARY}/#/pages/cloudLogin/index?token=${token}&system=cloud&info=${identityType.value}`
    if (!!options.taskType) {
        //待办
        url.value = `${ydUrl}&taskType=${options.taskType}`
    } else if (!!options.type) {
        // 首页
        url.value = `${ydUrl}&type=${options.type}`
    } else if (options.scanResult) {
        url.value = `${ydUrl}&scanResult=${options.scanResult}`
        // 如果是从扫码页面返回的，需要处理扫码结果
        if (options.scanResult) {
            console.log("从扫码页面返回，处理结果:", options.scanResult)
            // 延迟一点执行，确保iframe已经加载
            setTimeout(() => {
                handleScanResult(options.scanResult)
            }, 300)
        }
    } else {
        // 工作台
        url.value = ydUrl
    }
    // #ifdef H5-WEIXIN || H5
    // 监听iframe发送的消息
    window.onmessage = (event) => {
        // 根据上面制定的结构来解析iframe内部发回来的数据
        if (event.data.libAppBack) {
            uni.switchTab({
                url: "/pages/workbench/index"
            })
        }
        if (event.data.libHomeBack) {
            uni.switchTab({
                url: "/pages/home/<USER>"
            })
        }
        if (event.data.libTodoBack) {
            uni.switchTab({
                url: "/pages/chat/index"
            })
        }
        // 接收到iframe请求扫码的消息
        if (event.data?.scanQRCode && !event.data.code) {
            // 使用Html5QrCode进行扫码
            openScanQRCode()
        }
    }
    //  #endif
})
</script>

<style lang="scss" scoped>
.webview {
    height: 100vh;
    width: 100vw;
}
</style>
<template>
    <div class="collapse_list">
        <uni-collapse ref="collapse" accordion @change="collapseStatusFn">
            <uni-collapse-item @click="collapseItem(item)" :show-arrow="false" :border="false" v-for="(item, index) in list" :key="index">
                <template v-slot:title>
                    <uni-list :border="false" class="collapse_item">
                        <uni-list-item>
                            <template v-slot:header>
                                <view class="item_left">
                                    <div :class="item.collapseStatus == 1 ? 'expand_image' : 'retract_image'"></div>
                                    <slot name="left" :data="item">
                                        {{ item.bedNum }}
                                    </slot>
                                </view>
                            </template>
                            <template v-slot:body>
                                <text class="item_body">
                                    <slot name="body" :data="item">
                                        {{ item.studentName }}
                                    </slot>
                                </text>
                            </template>
                            <template v-slot:footer>
                                <div class="item_right">
                                    <slot name="right" :data="item">
                                        {{ item.classesName }}
                                    </slot>
                                </div>
                            </template>
                        </uni-list-item>
                    </uni-list>
                </template>
                <slot name="content" :childList="item"></slot>
            </uni-collapse-item>
        </uni-collapse>
    </div>
</template>

<script setup>
const collapseStatus = ref(null)
const collapse = ref(null)
const props = defineProps({
    list: {
        type: Array,
        default: []
    }
})

function collapseStatusFn(status) {
    collapseStatus.value = status
}

function collapseItem(item) {
    props.list.forEach((i) => {
        i.collapseStatus = null
    })
    if (collapseStatus.value) {
        item.collapseStatus = "1"
    } else {
        item.collapseStatus = null
    }
}
</script>

<style lang="scss" scoped>
.collapse_list {
    .collapse_item {
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        line-height: 40rpx;
        .item_left {
            max-width: 30%;
            text-align: left;
            padding-left: 10rpx;
            font-weight: 600;
            display: flex;
            align-items: center;
            .retract_image,
            .expand_image {
                height: 28rpx;
                width: 28rpx;
            }
            .retract_image {
                background: url("https://alicdn.1d1j.cn/announcement/20230727/2acd3a4bdc3045e09bf5af1fbfed3b1a.png") no-repeat;
                background-size: contain;
            }
            .expand_image {
                background: url("https://alicdn.1d1j.cn/announcement/20230727/662e6f5e270f400c8748269d74a8d208.png") no-repeat;
                background-size: contain;
            }
        }
        .item_body {
            min-width: 35%;
            max-width: 35%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .item_right {
            max-width: 30%;
            padding-right: 12rpx;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            text-align: end;
        }
    }
}
:deep(.uni-list-item__container) {
    justify-content: space-between;
}
</style>

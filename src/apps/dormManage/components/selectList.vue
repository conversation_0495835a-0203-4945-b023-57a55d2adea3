<template>
    <view class="select_list_container">
        <uni-list :border="false">
            <uni-list-item @click="_clickItem(item)" clickable v-for="(item, index) in list" :key="item.id + index" class="item_box">
                <template #body>
                    <view class="main_box">
                        <!-- <WordHighlighter
              :highlightStyle="{
                color: '#4566d5',
                backgroundColor: 'transparent',
              }"
              :caseSensitive="false"
              :splitBySpace="true"
              :query="lightQuery"
              @matches="
                e => {
                  matchedWords = e
                }
              "
            >
            </WordHighlighter> -->
                        <slot :item="item">
                            <div class="box">
                                <img v-if="item.avatar" class="l_img" :src="item.avatar" />
                                <span v-else class="avatar">{{ item.name.slice(-2) }}</span>
                                <span class="l_text">{{ item.name }}</span>
                                <span>{{ item.roomNo }}</span>
                            </div>
                        </slot>
                    </view>
                </template>
                <template #footer>
                    <view class="right_box">
                        <radio class="radio_box" @click="_clickItem(item)" v-if="selectFlag === 'multiple'" :checked="item.checked" />
                        <view v-if="selectFlag === 'radio'" :class="['radio_btn', { radio_check: item.checked }]"></view>
                    </view>
                </template>
            </uni-list-item>
        </uni-list>
    </view>
</template>
<script setup>
const emit = defineEmits(["itemClick"])
const prop = defineProps({
    selectFlag: {
        type: String,
        default: ""
    },
    list: {
        type: Array,
        default: () => []
    },
    rightType: {
        // 右箭头判断字段
        type: String,
        default: ""
    },
    rightList: {
        // 包含右箭头字段的数组
        type: Array,
        default: () => []
    }
})

function _clickItem(item) {
    if (prop.selectFlag === "radio") {
        prop.list.forEach((i) => (i.checked = false))
    }
    item.checked = !item.checked
    emit("itemClick", item)
}
</script>
<style lang="scss" scoped>
.select_list_container {
    background-color: #f0f2f5;
    margin: 0 28rpx;
    :deep(.uni-list-item__container) {
        padding: 0;
    }
    :deep(.uni-list-item--hover) {
        background-color: #fff !important;
    }
    .item_box {
        &:last-of-type {
            .main_box,
            .right_box {
                border-bottom: none;
            }
        }
    }
    .main_box {
        background-color: #fff;
        font-size: 28rpx;
        color: #333;
        padding: 30rpx 0;
        flex: 1;
        border-bottom: 1rpx solid #d9d9d9;
        .box {
            display: flex;
            align-items: center;
            .avatar {
                display: inline-block;
                font-size: 24rpx;
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                overflow: hidden;
                color: #fff;
                background-color: #4566d5;
                line-height: 60rpx;
                text-align: center;
            }
            .l_img {
                display: inline-block;
                width: 60rpx;
                height: 60rpx;
                border-radius: 50%;
                overflow: hidden;
                // margin-right: 20rpx;
            }

            .l_text {
                margin: 0 20rpx;
            }
        }
    }
    .right_box {
        border-bottom: 1rpx solid #d9d9d9;
        display: flex;
        align-items: center;

        .radio_box {
            margin-right: 10rpx;
            :deep(.uni-radio-wrapper) {
                vertical-align: bottom;
            }
            :deep(.uni-radio-input) {
                width: 36rpx;
                height: 36rpx;
            }
        }
        .radio_btn {
            display: inline-block;
            width: 36rpx;
            height: 36rpx;
            border: 1rpx solid #999999;
            border-radius: 50%;
            margin-right: 16rpx;
            flex-shrink: 0;
            box-sizing: border-box;
        }
        .radio_check {
            border: 10rpx solid #4566d5;
        }
    }
}
</style>

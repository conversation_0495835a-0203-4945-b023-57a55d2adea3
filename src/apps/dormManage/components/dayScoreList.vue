<template>
    <div class="day_score_info">
        <div class="statistics">
            <div class="deduct">
                <span class="title">总扣分</span>
                <span class="fraction">{{ scoreObj?.totalSubtractScore || 0 }}分</span>
            </div>
            <div class="divider"></div>
            <div class="bonus">
                <span class="title">总加分</span>
                <span class="fraction">{{ scoreObj?.totalAddScore || 0 }}分</span>
            </div>
            <div class="divider"></div>
            <div class="total">
                <span class="title">总计</span>
                <span class="fraction">{{ totalScore || "0分" }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    // 分数
    scoreObj: {
        type: Object,
        default: () => {}
    },
    // 总分
    totalScore: {
        type: String,
        default: "0分"
    }
})
const scoreObj = computed(() => {
    return props.scoreObj
})

const totalScore = computed(() => {
    return props.totalScore
})
</script>

<style lang="scss" scoped>
.day_score_info {
    padding: 30rpx;

    .statistics {
        height: 154rpx;
        background: #f0f2f5;
        border-radius: 20rpx;
        display: flex;
        justify-content: space-between;
        padding: 0rpx 80rpx;
        align-items: center;
        .deduct,
        .bonus,
        .total {
            display: flex;
            flex-direction: column;
            align-items: center;
            .title {
                font-size: 28rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                color: #666666;
                line-height: 40rpx;
            }
            .fraction {
                margin-top: 10rpx;
                font-size: 32rpx;
                font-family:
                    PingFangSC-Regular,
                    PingFang SC;
                font-weight: 400;
                line-height: 44rpx;
                color: #333333;
            }
        }
        .deduct {
            .fraction {
                color: #f5222d;
            }
        }
        .bonus {
            .fraction {
                color: #4566d5;
            }
        }

        .divider {
            width: 2rpx;
            height: 48rpx;
            background: #d9d9d9;
        }
    }
}
</style>

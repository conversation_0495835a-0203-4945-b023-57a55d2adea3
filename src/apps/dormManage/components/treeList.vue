<template>
  <view class="tree_class">
    <uni-list :border="false" v-for="(item, index) in list" :key="item.floor + 'TreeList' + index">
      <uni-list-item @click="_clickItem(item)" clickable class="item_box">
        <template #header>
          <view class="left_box" v-if="flag === 'rollValue' ? item.rollValue !== 'student' : item.hierarchy !== 3">
            <!-- 自定义左箭头 -->
            <cover-image
              class="transform_icon interval"
              v-if="item.up"
              src="https://alicdn.1d1j.cn/announcement/20230828/31214cdacb6d4180a17920d898b24bc5.png"
              alt=""
            />
            <cover-image
              v-else
              class="interval"
              src="https://alicdn.1d1j.cn/announcement/20230828/31214cdacb6d4180a17920d898b24bc5.png"
              alt=""
            />
          </view>
          <radio class="radio_box" @click="_clickItem(item)" v-if="flagList.includes(item[flag]) && multiple" :checked="item.checked" />
          <view v-else-if="flagList.includes(item[flag]) && !multiple" :class="['radio_btn', { radio_check: item.checked }]"></view>
        </template>
        <template #body>
          <view class="main_box">
            <!-- <slot :item="item"></slot> -->
            <template v-if="flag === 'rollValue'">
              <view class="tree_item_box_rollValue">
                <view v-if="item.rollValue === 'student'" class="left_box">
                  <view class="left">
                    <img v-if="item.avatar" :src="item.avatar" class="img" />
                    <view v-else class="chart_at">{{ item.name.charAt(0) }}</view>
                    <text class="text">{{ item.name }}</text>
                  </view>
                  <text>{{ item.gender === 1 ? '男' : '女' }}</text>
                </view>
                <template v-else>
                  <text>{{ item.name }}</text>
                  <text class="text_right">{{ item.peopleCount }}个学生</text>
                </template>
              </view>
            </template>
            <template v-if="flag === 'hierarchy'">
              <view class="tree_item_box_hierarchy">
                <view v-if="item.hierarchy === 3" class="left_box">
                  <img v-if="item.avatar" :src="item.avatar" class="img" />
                  <view v-else class="chart_at">{{ item.studentName.charAt(0) }}</view>
                  <text class="text">{{ item.studentName }}</text>
                </view>
                <template v-else-if="item.hierarchy === 1">
                  <text>{{ item.floorName }}</text>
                  <text class="text_right">{{ item.peopleNum }}个学生</text>
                </template>
                <template v-else-if="item.hierarchy === 2">
                  <text>{{ item.roomName }}</text>
                  <text class="text_right">{{ item.peopleNum }}个学生</text>
                </template>
              </view>
            </template>
          </view>
        </template>
        <template #footer>
          <uni-icons v-if="rightList.includes(item[rightType])" class="arrow_ico" type="right" color="#000000" size="18"></uni-icons>
        </template>
      </uni-list-item>
      <view v-show="item.up" v-if="item?.children && item?.children.length > 0" style="padding-left: 12px">
        <view v-if="item.up">
          <tree-list
            :list="item.children"
            :tree-click-item="treeClickItem"
            :flag="flag"
            :flagList="flagList"
            :rightType="rightType"
            :rightList="rightList"
            :multiple="multiple"
          >
            <!-- <template #default="slotScope">
              <slot :item="slotScope.item"></slot>
            </template> -->
          </tree-list>
        </view>
      </view>
    </uni-list>
  </view>
</template>

<script setup>
const prop = defineProps({
  multiple: {
    type: Boolean,
    default: true,
  },
  list: {
    type: Array,
    default: () => [],
  },
  flag: {
    // 选择框判断字段
    type: String,
    default: '',
  },
  flagList: {
    // 包含选择字段的数组
    type: Array,
    default: () => [],
  },
  rightType: {
    // 右箭头判断字段
    type: String,
    default: '',
  },
  rightList: {
    // 包含右箭头字段的数组
    type: Array,
    default: () => [],
  },
  treeClickItem: {
    type: Function,
    default() {
      return function () {}
    },
  },
})

function _clickItem(item) {
  item.up = !item.up
  prop.treeClickItem(item)
}
</script>

<style lang="scss" scoped>
.tree_class {
  text-align: left;
  .item_box {
    min-height: 40rpx;
  }
  .left_box {
    margin: auto 0;
  }
  .radio_box {
    margin: auto 0;
    margin-right: 10rpx;
    :deep(.uni-radio-wrapper) {
      vertical-align: bottom;
    }
    :deep(.uni-radio-input) {
      width: 36rpx;
      height: 36rpx;
    }
  }
  .radio_btn {
    display: inline-block;
    width: 36rpx;
    height: 36rpx;
    border: 1rpx solid #999999;
    border-radius: 50%;
    margin: auto 0;
    margin-right: 16rpx;
    flex-shrink: 0;
    box-sizing: border-box;
  }
  .radio_check {
    border: 10rpx solid #4566d5;
  }
  .transform_icon {
    transform: rotate(90deg);
  }
  .custom_title {
    margin-left: 15rpx;
  }
  .arrow_ico {
    margin-top: 4rpx;
  }
  .interval {
    width: 28rpx;
    height: 28rpx;
    margin-right: 10rpx;
  }
  .main_box {
    width: 100%;
    overflow: hidden;
    .tree_item_box_rollValue {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-size: 32rpx;
      color: #333333;
      .left_box {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          .img {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            overflow: hidden;
          }
          .chart_at {
            display: inline-block;
            width: 60rpx;
            height: 60rpx;
            text-align: center;
            line-height: 60rpx;
            color: #fff;
            font-size: 32rpx;
            border-radius: 50%;
            background-color: #4566d5;
            font-weight: 600;
          }
          .text {
            font-size: 28rpx;
            color: #333;
            margin-left: 24rpx;
          }
        }
      }
      .text_right {
        font-size: 28rpx;
        color: #999999;
        margin: auto 0;
      }
    }

    .tree_item_box_hierarchy {
      width: 100%;
      display: flex;
      justify-content: space-between;
      font-size: 32rpx;
      color: #333333;
      .left_box {
        display: flex;
        align-items: center;
        .img {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          overflow: hidden;
        }
        .chart_at {
          display: inline-block;
          width: 60rpx;
          height: 60rpx;
          text-align: center;
          line-height: 60rpx;
          color: #fff;
          font-size: 32rpx;
          border-radius: 50%;
          background-color: #4566d5;
          font-weight: 600;
        }
        .text {
          font-size: 28rpx;
          color: #333;
          margin-left: 24rpx;
        }
      }
      .text_right {
        font-size: 28rpx;
        color: #999999;
        margin: auto 0;
      }
    }
  }
}
</style>

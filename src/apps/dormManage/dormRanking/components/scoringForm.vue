<template>
  <div class="list_class">
    <div class="item" v-for="(item, index) in scoreList" :key="index">
      <div class="title">{{ item.firstIndicatorName }}</div>
      <div
        class="norm_list"
        v-for="(norm, normIndex) in item.indicatorDetailList"
        :key="normIndex + 'norm'"
      >
        <div class="norm_title">
          {{ norm.scoreStandard }}
        </div>
        <div class="norm_score">
          <span class="text">得分</span>
          <uni-number-box
            :min="norm.scoreRangeMin || 0"
            :max="norm.scoreRangeMax || 10"
            v-model="norm.initializeScore"
            :step="norm.minScoreUnit || 0.5"
          />
        </div>
        <div class="divider"></div>
        <div class="norm_score">
          <span class="text">备注</span>
          <textarea
            class="textarea_class"
            placeholder-class="placeholder_class"
            autoHeight
            v-model="norm.remarked"
            placeholder="请输入"
          ></textarea>
        </div>
        <div class="divider"></div>
      </div>
    </div>
    <!-- 底部 -->
    <div class="footer_box">
      <button @click="submitForm" :loading="loading">提交</button>
    </div>
  </div>
</template>

<script setup>
import { reactive, onMounted, ref, shallowRef, computed } from "vue";
const emit = defineEmits(["submitForm"]);
const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  // 打分指标数组
  scoreList: {
    type: Array,
    default: () => [],
  },
});

const loading = computed(() => {
  return props.loading;
});

const scoreList = computed(() => {
  return props.scoreList;
});

function submitForm() {
  emit("submitForm", scoreList.value);
}
</script>

<style lang="scss" scoped>
.list_class {
  .item {
    display: flex;
    flex-direction: column;
    margin-bottom: 20rpx;
    min-height: 400rpx;
    background: #fff;
    padding: 30rpx;
    .title {
      font-size: 28rpx;
      font-weight: 600;
      color: #333333;
      line-height: 40rpx;
    }
    .norm_list {
      display: flex;
      margin-top: 30rpx;
      flex-direction: column;
      .norm_title {
        font-size: 28rpx;
        font-weight: 400;
        color: #4566d5;
        line-height: 40rpx;
      }
      .norm_score {
        padding: 34rpx 0rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .text {
          font-size: 28rpx;
          font-weight: 400;
          color: #333333;
          line-height: 40rpx;
        }
        .textarea_class {
          width: 72%;
          font-size: 28rpx;
          font-weight: 400;
          color: #999999;
          line-height: 40rpx;
          text-align: right;
        }
        .placeholder_class {
          font-size: 28rpx;
          font-weight: 400;
          color: #999999;
          line-height: 40rpx;
        }
      }
      .divider {
        height: 2rpx;
        background: #d8d8d8;
      }
    }
  }
  .footer_box {
    width: 92%;
    height: 106rpx;
    background: #ffffff;
    position: fixed;
    bottom: 0rpx;
    left: 0rpx;
    padding: 30rpx;

    button {
      background: #4566d5;
      border-radius: 10rpx;
      font-size: 32rpx;
      height: 92rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 92rpx;
    }
  }
}
</style>

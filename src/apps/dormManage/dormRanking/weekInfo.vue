<template>
    <div class="scoreInfo">
        <!-- 头部自定义导航栏 -->
        <view class="head">
            <uni-nav-bar fixed statusBar shadow left-icon="left" :border="false" @clickLeft="routerBack">
                <view class="title_box"> {{ pageParams.value?.title }} </view>
            </uni-nav-bar>
        </view>
        <ScoreList :list="state.list" :myClass="pageParams.value" @clickItem="clickItem" />
    </div>
</template>

<script setup>
import ScoreList from "./components/scoreList.vue"
const state = reactive({
    list: []
})

const pageParams = {}
onLoad((params) => {
    pageParams.value = params
})

function clickItem(item) {
    navigateTo({
        url: "/apps/dormManage/dormRanking/dayInfo",
        query: {
            ...item,
            activityId: pageParams.value.activityId,
            isBack: true
        }
    })
}

function routerBack() {
    uni.navigateBack()
}

function getInfoFn() {
    const { activityId, cycleId, targetId } = pageParams.value
    const params = {
        activityId, // 活动标识
        cycleId, // 周期标识
        targetId // 我的班级
    }
    http.post("/app/moralEducationActivityMobile/identityDetail", params).then((res) => {
        state.list = res.data
    })
}

onMounted(() => {
    getInfoFn()
})
</script>

<style lang="scss" scoped>
// 头部
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-family:
            PingFangSC-Medium,
            PingFang SC;
        font-weight: 500;
        color: #333333;
    }
}
</style>

<template>
    <view class="select_admin_box">
        <uni-nav-bar fixed statusBar @clickLeft="back" leftText="取消">
            <view class="top_nav_bar">
                <view class="top_text">选择人员</view>
            </view>
        </uni-nav-bar>
        <view class="search">
            <uv-input placeholder="搜索学生姓名" prefixIcon="search" shape="circle" prefixIconStyle="font-size: 22px;color: #909399" @focus="focus"></uv-input>
        </view>
        <view class="top" @click="buildingChange">
            <div class="left">
                <img src="https://alicdn.1d1j.cn/1531914651808833538/default/5b63db3133f14be78620993dabb946d6.png" class="yd_img" />
                <text>{{ building.buildingName || "请选择楼栋" }}</text>
            </div>
            <uni-icons type="forward" size="18" color="#333" />
        </view>
        <view class="main_box">
            <treeList :flagList="[3]" flag="hierarchy" :rightList="['right']" rightType="rightValue" :list="treeListData" :multiple="multiple" :tree-click-item="treeClick"> </treeList>
        </view>
        <view class="footer_box">
            <button type="default" :class="['yd_btn_primary', { btn_disable: disable }]" @click="save">确认</button>
        </view>
        <SelectPopup @closePopup="closePopup" ref="buildingListRef" :list="buildingList" title="请选择" />
    </view>
</template>
<script setup>
import treeList from "../components/treeList.vue"
import SelectPopup from "../components/selectPopup.vue"
import { setCheck, getCheck } from "../utils/treeMethod.js"

let disable = ref(false)

// 树组件
const treeListData = ref([])
const buildingListRef = ref()
const buildingList = ref([])
const building = reactive({
    buildingName: "",
    buildingId: ""
})

const multiple = ref(false)

onLoad((params) => {
    multiple.value = params.multiple ? Boolean(params.multiple) : false
})

function buildingChange() {
    buildingListRef.value.open()
}

function getTreeList() {
    http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryFloorRoomAndBed", {
        buildingId: building.buildingId,
        filterEmptyBed: true
    }).then((res) => {
        treeListData.value = res.data || []
    })
}

function getBuilding() {
    http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryBuilding", {}).then((res) => {
        buildingList.value = res.data || []
        building.buildingName = res.data[0]?.name || ""
        building.buildingId = res.data[0]?.id || ""
        getTreeList()
    })
}
getBuilding()

function closePopup(item) {
    if (!item) return

    building.buildingName = item.name
    building.buildingId = item.id
    getTreeList()
}

const treeClick = (item) => {
    if (item.hierarchy === 3) {
        const params = {
            sourceArr: treeListData.value,
            obj: item,
            type: "hierarchy",
            flag: 3,
            multiple: multiple.value
        }
        setCheck(params)
    }
}

const save = () => {
    const arr = getCheck(treeListData.value, "hierarchy", 3)
    if (!arr.length) return
    disable.value = true
    const businessId = []
    const businessName = []
    arr.forEach((item) => {
        businessId.push(item.studentId)
        businessName.push(item.studentName)
    })
    uni.$emit("dormTraffic", { businessId, businessName })
    const params = {
        originalStudent: arr[0].studentId,
        studentName: arr[0].studentName,
        avatar: arr[0].avatar
    }
    uni.$emit("dormTransfer", params)

    uni.navigateBack()
}

const back = () => {
    uni.navigateBack()
}

const focus = () => {
    navigateTo({
        url: "/apps/dormManage/searchView/index",
        query: {
            selectFlag: multiple.value ? "multiple" : "radio"
        }
    })
}
</script>
<style lang="scss" scoped>
.select_admin_box {
    background-color: #f0f2f5;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_nav_bar {
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
    }
    .search {
        background: #fff;
        padding: 30rpx;
        :deep(.uv-input--circle) {
            background-color: #f0f2f5;
        }

        :deep(.uv-input__content) {
            height: 33rpx;
        }
    }
    .top {
        padding: 14rpx 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        color: #333;
        font-size: 32rpx;
        font-weight: 600;
        margin: 20rpx 0;
        justify-content: space-between;

        .left {
            display: flex;
            align-items: center;
        }
        .yd_img {
            width: 64rpx;
            height: 64rpx;
            margin-right: 20rpx;
            border-radius: 50%;
            border: 4rpx solid #cad6ff;
        }
    }
    .main_box {
        background-color: #fff;
        padding-bottom: 150rpx;
        .tree_item_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 32rpx;
            color: #333333;
            .left_box {
                display: flex;
                align-items: center;
                .img {
                    width: 60rpx;
                    height: 60rpx;
                    border-radius: 50%;
                    overflow: hidden;
                }
                .chart_at {
                    display: inline-block;
                    width: 60rpx;
                    height: 60rpx;
                    text-align: center;
                    line-height: 60rpx;
                    color: #fff;
                    font-size: 32rpx;
                    border-radius: 50%;
                    background-color: #4566d5;
                    font-weight: 600;
                }
                .text {
                    font-size: 28rpx;
                    color: #333;
                    margin-left: 24rpx;
                }
            }
            .text_right {
                font-size: 28rpx;
                color: #999999;
                margin: auto 0;
            }
        }
    }
    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        .btn_disable {
            background-color: #d8d8d8;
            color: #fff;
            pointer-events: none;
        }
        button {
            font-size: 32rpx;
            margin-left: 0;
            margin-right: 0;
        }
    }
}
</style>

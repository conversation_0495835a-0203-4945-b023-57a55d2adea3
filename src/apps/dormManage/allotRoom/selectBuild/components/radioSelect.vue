<template>
  <view class="radio_select_box">
    <view
      :class="['item', { isChecked: item.checked }]"
      v-for="(item, index) in list"
      :key="index"
      @click="handleClick(item)"
    >
      <view class="left">
        <slot name="left" :left="item">
          <view class="img">
            <cover-image
              :src="
                item.img ||
                'https://alicdn.1d1j.cn/1531914651808833538/default/9d907ff1a80c43a18aa6cbd1f49b2343.png'
              "
            ></cover-image>
          </view>
          <view>{{ item.name }}</view>
        </slot>
      </view>
      <view class="right"
        ><radio @click="handleClick(item)" :value="`${index}`" :checked="item.checked"
      /></view>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  value: {
    type: Object,
    default: () => ({}),
  },
  list: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:value'])

const handleClick = item => {
  props.list.forEach(i => (i.checked = false))
  item.checked = !item.checked
  emit('update:value', item)
}
</script>
<style lang="scss">
.radio_select_box {
  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40rpx 28rpx;
    .left {
      flex: 1;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 32rpx;
      font-weight: 600;
      padding: 28rpx;
      border: 1rpx solid #d9d9d9;
      border-radius: 20rpx;
      margin-right: 20rpx;
      .img {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 60rpx;
      }
    }
    .right {
      :deep(.uni-radio-input) {
        margin-right: 0;
      }
    }
  }
  .isChecked {
    border-color: #4566d5;
  }
}
</style>

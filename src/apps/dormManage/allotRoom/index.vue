<template>
    <view class="allot_room_container">
        <view class="top_box">
            <uv-steps :current="current">
                <uv-steps-item :title="allotStep.one || '选择性别'"></uv-steps-item>
                <uv-steps-item :title="allotStep.two || '分配对象'"></uv-steps-item>
                <uv-steps-item :title="allotStep.three || '分配方式'"></uv-steps-item>
            </uv-steps>
        </view>
        <!-- 内容信息 -->
        <view class="main_box">
            <view class="title">请选择您要分配{{ titleObj[current] }}</view>
            <radioSelect v-if="current === 0" v-model:value="allotStep.sexVal" :list="sexList"></radioSelect>
            <radioSelect v-else-if="current === 1" v-model:value="allotStep.objVal" :list="objList"></radioSelect>
            <radioSelect v-else="current === 2" v-model:value="allotStep.modeVal" :list="modeList">
                <template #left="{ left }">
                    <view>{{ left.name }}</view>
                </template>
            </radioSelect>
        </view>
        <!-- 底部按钮 -->
        <div class="footer_box">
            <button v-if="current === 0" type="default" class="yd_btn_primary" @click="next">下一步</button>
            <view v-else class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button class="yd_btn_primary" @click="next">下一步</button>
            </view>
        </div>
    </view>
</template>
<script setup>
import radioSelect from "../components/radioSelect.vue"

const titleObj = {
    0: "的性别",
    1: "的对象",
    2: "寝室的方式"
}

let current = ref(0)

let allotStep = reactive({
    one: "",
    two: "",
    three: "",
    sexVal: {},
    objVal: {},
    modeVal: {}
})

const sexList = ref([
    {
        name: "男生",
        value: 1,
        img: "https://alicdn.1d1j.cn/announcement/20230825/413787e5cc964ad388fe6134fdf8d65d.png"
    },
    {
        name: "女生",
        value: 0,
        img: "https://alicdn.1d1j.cn/announcement/20230825/085395fb71f9482ab0e8f552237c49cb.png"
    }
])

const objList = ref([
    {
        name: "按班级分",
        value: 1,
        img: "https://alicdn.1d1j.cn/announcement/20230828/2b4dfc9001ed49fa9aa2028897bbb27e.png"
    },
    {
        name: "按学生分",
        value: 2,
        img: "https://alicdn.1d1j.cn/announcement/20230828/5e2e35d7328d42f2968cfaf91bbc0cc2.png"
    }
])

const modeList = ref([
    { name: "按整栋楼分寝", value: 1 },
    { name: "按楼层分寝", value: 2 },
    { name: "按寝室分寝", value: 3 },
    { name: "按床位分寝", value: 4 }
])

const previous = () => {
    current.value--
}

const routeView = {
    1: "/apps/dormManage/allotRoom/selectBuild/index",
    2: "/apps/dormManage/allotRoom/selectFloor/index",
    3: "/apps/dormManage/allotRoom/selectDorm/index",
    4: "/apps/dormManage/allotRoom/selectBed/index"
}

const next = () => {
    if (current.value === 0 && allotStep.sexVal.name) {
        allotStep.one = allotStep.sexVal.name
        current.value++
    } else if (current.value === 1 && allotStep.objVal.name) {
        allotStep.two = allotStep.objVal.name
        // 按班级分
        if (allotStep.objVal.value === 1) {
            navigateTo({
                url: "/apps/dormManage/allotRoom/classAllot/index",
                query: {
                    gender: allotStep.sexVal.value
                }
            })
        } else {
            // 按学生分
            navigateTo({
                url: "/apps/dormManage/allotRoom/studentAllot/index",
                query: {
                    gender: allotStep.sexVal.value
                }
            })
        }
    } else {
        allotStep.three = allotStep.modeVal.name
        const params = {
            one: allotStep.one,
            two: allotStep.two,
            three: allotStep.three,
            studentGender: allotStep.gender,
            studentIdList: typeof allotStep.studentIdList === "string" ? allotStep.studentIdList : JSON.stringify(allotStep.studentIdList) || "[]",
            classesIdList: typeof allotStep.classesIdList === "string" ? allotStep.classesIdList : JSON.stringify(allotStep.classesIdList) || "[]"
        }
        if (!routeView[allotStep.modeVal.value]) return
        navigateTo({
            url: routeView[allotStep.modeVal.value],
            query: params
        })
    }
}

uni.$on("nextStep", function (data) {
    if (data.step == 2) {
        Object.assign(allotStep, data)
        current.value++
    } else if (data.step === 3) {
        current.value++
    }
})
</script>
<style lang="scss" scoped>
.allot_room_container {
    background-color: #f0f2f5;
    .top_box {
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: #f0f2f5;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 28rpx;
        padding-bottom: 140rpx;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            padding-bottom: 40rpx;
        }
    }

    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 20rpx;
        padding: 30rpx;
        .yd_btn_primary {
            background-color: #4566d5;
            color: #fff;
        }
        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
:deep(.uv-steps-item) {
    .uv-steps-item__content {
        margin-left: 0px !important;
    }
}
</style>

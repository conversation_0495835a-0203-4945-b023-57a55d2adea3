<template>
  <view class="yd_select_box">
    <view
      :class="[
        'yd_select',
        {
          active_class: item.checked,
          poiner_none: item.studentId,
        },
      ]"
      :style="{ width: item.width || '210rpx' }"
      v-for="(item, index) in list"
      :key="index"
      @click="handleClickInfo(item, index)"
    >
      <view class="item_box">
        <view class="top"></view>
        <view class="bottom">床位:{{ item.bedNo }}</view>
      </view>
      <img
        v-if="item.checked"
        class="yd_subscript"
        src="https://alicdn.1d1j.cn/1531914651808833538/default/d67f686001ed4947b1f7a572102d8e89.png"
      />
    </view>
  </view>
</template>
<script setup>
const prop = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  value: {
    type: [String, Array, Number],
    default: "",
  },
});

const emit = defineEmits(["update:value", "handleClick"]);

const toEmpty = () => {
  prop.list.forEach((i) => (i.checked = false));
};

const getChecked = () => {
  const item = prop.list.filter((i) => i.checked);
  return item;
};

const handleClickInfo = (item) => {
  if (prop.multiple) {
    item.checked = !item.checked;
    emit("update:value", getChecked());
  } else {
    toEmpty();
    item.checked = !item.checked;
    emit("update:value", item.value);
  }
  emit("handleClick", item);
};
</script>
<style lang="scss" scoped>
.yd_select_box {
  display: flex;
  flex-wrap: wrap;

  .yd_select {
    border-radius: 4px;
    position: relative;
    cursor: pointer;
    border: 1rpx solid #d9d9d9;
    margin: 50rpx 0 0 50rpx;
    .item_box {
      height: 280rpx;
      border-radius: 10rpx;
      padding-top: 16rpx;
      display: flex;
      flex-direction: column;
      .top {
        width: 135rpx;
        height: 35rpx;
        border: 1rpx solid #d9d9d9;
        border-radius: 10rpx;
        margin: 0 auto;
        margin-bottom: 16rpx;
      }
      .bottom {
        // flex: 1;
        font-size: 28rpx;
        color: #333;
        border-top: 1rpx solid #d9d9d9;
        border-radius: 30rpx 30rpx 10rpx 10rpx;
        // display: flex;
        // justify-content: center;
        // align-items: center;
        padding: 72rpx 20rpx 0 20rpx;
        height: 80rpx;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
      }
    }
    .yd_subscript {
      position: absolute;
      bottom: -1px;
      right: -1px;
    }
  }

  .active_class {
    border-color: #4566d5;
    overflow: hidden;
    .item_box {
      .top,
      .bottom {
        border-color: #4566d5;
      }
    }
  }
  .poiner_none {
    pointer-events: none;
    background-color: #d9d9d9;
    .item_box {
      .top {
        border-color: #fff;
      }
      .bottom {
        border-color: #fff;
      }
    }
  }
}
</style>

<template>
    <view class="class_allot_container">
        <view class="top">
            <img :src="schoolInfo.avatar || 'https://alicdn.1d1j.cn/1531914651808833538/default/26c5462c5b8f4dd7a49732cda6e1003f.png'" class="yd_img" />
            <text>{{ schoolInfo.name }}</text>
        </view>
        <view class="main_box">
            <treeList :flagList="['classes']" flag="rollValue" :rightList="['right']" rightType="rightValue" :list="treeListData" :tree-click-item="treeClick"> </treeList>
        </view>
        <view class="footer_box">
            <view class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button :class="['yd_btn_primary', { btn_disable: !disable }]" @click="next">下一步</button>
            </view>
        </view>
    </view>
</template>
<script setup>
import treeList from "../../components/treeList.vue"
import { setCheck, getCheck } from "../../utils/treeMethod.js"

let gender = ""
let disable = computed(() => getCheck(treeListData.value, "rollValue", "classes").filter((i) => i.peopleCount).length)

onLoad((options) => {
    gender = options.gender
    getTree()
})

const getTree = async () => {
    const {
        data: { list }
    } = await http.get("/app/roll/dormitory/v1/listTree", { gender })
    treeListData.value = list
    schoolInfo.avatar = list[0].avatar
    schoolInfo.name = list[0].name
}
const schoolInfo = reactive({})

// 树组件
const treeListData = ref()

const treeClick = (item) => {
    if (item.rollValue === "classes") {
        const params = {
            sourceArr: treeListData.value,
            obj: item,
            type: "rollValue",
            flag: "classes",
            multiple: true
        }
        setCheck(params)
    }
}

const previous = () => {
    uni.navigateBack(1)
}

const next = () => {
    let arr = getCheck(treeListData.value, "rollValue", "classes").filter((i) => i.peopleCount)
    if (!arr.length) return
    disable.value = false
    const two =
        arr.reduce((pre, curr) => {
            pre.peopleCount += curr.peopleCount
            return pre
        }).peopleCount + "人"
    const classesIdList = arr.map((i) => i.id)
    uni.navigateBack({
        success() {
            uni.$emit("nextStep", { step: 2, two, classesIdList, gender })
        }
    })
}
</script>
<style lang="scss" scoped>
.class_allot_container {
    background-color: #f0f2f5;
    padding-top: 20rpx;
    .top {
        padding: 14rpx 28rpx;
        background-color: #fff;
        display: flex;
        align-items: center;
        color: #333;
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 20rpx;
        .yd_img {
            width: 64rpx;
            height: 64rpx;
            margin-right: 20rpx;
            border-radius: 50%;
        }
    }
    .main_box {
        background-color: #fff;
        padding-bottom: 160rpx;
        .tree_item_box {
            width: 100%;
            display: flex;
            justify-content: space-between;
            font-size: 32rpx;
            color: #333333;
            .text_right {
                font-size: 28rpx;
                color: #999999;
                margin: auto 0;
            }
        }
    }
    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;

        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            .yd_btn_primary {
                background-color: #4566d5;
                color: #fff;
            }
            .btn_disable {
                background-color: #d8d8d8;
                color: #fff;
                pointer-events: none;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
</style>

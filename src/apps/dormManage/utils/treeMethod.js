// 清空所有选中数据
const setEmpty = (arr, type, flag) => {
  if (Array.isArray(arr)) {
    arr.forEach((i) => {
      if (i[type] === flag) {
        i.checked = false;
      }
      if (i.children && i.children.length) setEmpty(i.children, type, flag);
    });
  }
};

// 递归调用选中状态
/**
 * @param {Array} sourceArr 树组件原始数据
 * @param {Object} obj 当前选中数据
 * @param {String} type 判断选中字段
 * @param {String} flag 选中字段值
 * @param {Boolean} multiple 选中状态  默认多选
 */
export const setCheck = ({ sourceArr, obj, type, flag, multiple = true }) => {
  if (multiple) {
    if (obj[type] === flag) {
      obj.checked = !obj.checked;
    }
    if (obj.children && obj.children.length) {
      obj.children.forEach((i) => setCheck(sourceArr, i, type, flag, multiple));
    }
  } else {
    setEmpty(sourceArr, type, flag);
    obj.checked = !obj.checked;
  }
};

// 获取所有选中项
/**
 *
 * @param {*} arr 树组件原始数据
 * @param {*} type 判断选中字段
 * @param {*} flag 选中字段值
 * @param {*} newArr 收集数据集合返回值
 * @returns
 */
export const getCheck = (arr, type, flag, newArr = []) => {
  if (Array.isArray(arr)) {
    arr.forEach((i) => {
      if (i[type] === flag && i.checked === true) {
        newArr.push(i);
      }
      if (i.children && i.children.length)
        getCheck(i.children, type, flag, newArr);
    });
  }
  return newArr;
};

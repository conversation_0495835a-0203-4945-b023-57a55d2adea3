<template>
    <div class="release_record">
        <div class="picker_date">
            <view class="date_class" @click="selectDateChange">
                <span class="text" v-if="!state.startDate || !state.endDate">全部日期</span>
                <view v-else class="text">{{ state.startDate + " 至 " + state.endDate }}</view>
                <div class="image"></div>
            </view>
        </div>
        <div class="release_list">
            <div class="release_item" v-for="(item, index) in state.releaseList" :key="index">
                <div class="item">
                    <span class="title title_box">
                        <span>通行人员</span>
                        <uv-icon size="18" :name="item.isShow ? 'arrow-up' : 'arrow-down'" @click="showInfo(item)" color="#999999"></uv-icon>
                    </span>
                    <span class="text" :class="item.isShow ? 'show_text' : 'text'">{{ item.releasePeopleList && item.releasePeopleList.length ? item.releasePeopleList.join("、") : "-" }}</span>
                </div>
                <div class="item">
                    <span class="title">放行人</span>
                    <span class="text">{{ item.releaseBy }}</span>
                </div>
                <div class="item">
                    <span class="title">放行日期</span>
                    <span class="text">{{ item.releaseDate }}</span>
                </div>
                <div class="item">
                    <span class="title">放行时间</span>
                    <span class="text"> {{ item.releaseTime }}</span>
                </div>
                <div class="item">
                    <span class="title">放行原因</span>
                    <span class="text">{{ item.releaseReason }}</span>
                </div>
            </div>
        </div>
        <uni-calendar ref="calendarRelease" :insert="false" :lunar="false" :range="true" @confirm="confirm" />
        <uni-load-more v-if="state.pagination.pageNo > 1" :status="state.status" :contentText="state.contentText" iconType="circle" />
    </div>
</template>

<script setup>
import { onReachBottom } from "@dcloudio/uni-app"
const calendarRelease = ref("")
const state = reactive({
    status: "more",
    startDate: "",
    endDate: "",
    contentText: {
        contentdown: "加载更多", //more
        contentrefresh: "正在加载...", // loading
        contentnomore: "-- 没有更多内容了 --" //noMore
    },
    pagination: {
        pageNo: 1,
        pageSize: 10,
        total: 0
    },
    releaseList: []
})

const getReleaseList = async () => {
    const params = {
        ...state.pagination,
        startDate: state.startDate,
        endDate: state.endDate
    }
    state.status = "loading"
    await http
        .post("/app/dorm/pass/dormReleaseRecord", params)
        .then(({ data }) => {
            const { list, pageNo, pageSize, total } = data
            state.pagination = { pageNo, pageSize, total }
            if (state.releaseList.length) {
                state.releaseList = state.releaseList.concat(list)
            } else {
                state.releaseList = list || []
            }
        })
        .finally(() => {
            state.status = "noMore"
            uni.stopPullDownRefresh()
        })
}

onReachBottom(() => {
    const { pagination } = state
    if (pagination.total > pagination.pageSize * pagination.pageNo) {
        state.pagination.pageNo++
        getReleaseList()
    }
})

function showInfo(item) {
    item.isShow = !item.isShow
}

function selectDateChange(e) {
    calendarRelease.value.open()
}
function confirm(e) {
    state.startDate = e.range.before
    state.endDate = e.range.after
    state.pagination.pageNo = 1
    state.pagination.pageSize = 10
    state.releaseList = []
    getReleaseList()
}
getReleaseList()
</script>

<style lang="scss" scoped>
.release_record {
    min-height: 94vh;
    background: #f0f2f5;
    .picker_date {
        height: 100rpx;
        background: #ffffff;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 0rpx 30rpx;
        .date_class {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .text {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
            }
            .image {
                height: 28rpx;
                width: 28rpx;
                background: url("https://alicdn.1d1j.cn/announcement/20230726/20daa8cd92c245cda0c06f648bc9e6f8.png") no-repeat;
                background-size: contain;
            }
        }
    }
    .release_list {
        padding: 30rpx;
        .release_item {
            padding: 30rpx;
            min-height: 600rpx;
            background: #ffffff;
            border-radius: 20rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            margin-bottom: 20rpx;

            .item {
                display: flex;
                flex-direction: column;
                margin-bottom: 20px;
                .title {
                    font-size: 24rpx;
                    font-weight: 400;
                    color: #666666;
                    line-height: 34rpx;
                    padding-bottom: 10rpx;
                }
                .title_box {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .text {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }
                .show_text {
                    white-space: pre-wrap;
                }
            }
        }
    }
}
</style>

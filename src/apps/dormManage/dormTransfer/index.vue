<template>
    <div class="dorm_transfer">
        <uni-list :border="false">
            <uni-list-item class="form_item" clickable @click="typeClick">
                <template v-slot:header>
                    <view class="left">
                        <span class="must">*</span>
                        调动方式
                    </view>
                </template>
                <template v-slot:footer>
                    <view class="right">
                        <span class="right_text"> {{ adjustTypeList[form.adjustType] }}</span>
                        <uni-icons type="forward" size="18" color="#999999" />
                    </view>
                </template>
            </uni-list-item>
            <uni-list-item class="form_item" :clickable="!!form.adjustType" @click="toPage('person')">
                <template v-slot:header>
                    <view class="left">
                        <span class="must">*</span>
                        {{ adjustTypeList[form.adjustType] }}对象
                    </view>
                </template>
                <template v-slot:footer>
                    <view class="right">
                        <span class="right_text"> {{ form.studentName || "请选择" }}</span>
                        <uni-icons type="forward" size="18" color="#999999" />
                    </view>
                </template>
            </uni-list-item>
            <uni-list-item class="form_item" :clickable="!!form.studentName" @click="toPage('bed')" v-if="form.adjustType !== 2">
                <template v-slot:header>
                    <view class="left">
                        <span class="must">*</span>
                        {{ adjustTypeList[form.adjustType] }}床位
                    </view>
                </template>
                <template v-slot:footer>
                    <view class="right">
                        <span class="right_text">
                            {{ form.changeBed || "请选择" }}
                        </span>
                        <uni-icons type="forward" size="18" color="#999999" />
                    </view>
                </template>
            </uni-list-item>
            <uni-list-item class="form_item" v-if="form.adjustType === 2">
                <template v-slot:header>
                    <view class="left">
                        <span class="must">*</span>
                        退寝日期
                    </view>
                </template>
                <template v-slot:footer>
                    <picker mode="date" :value="form.checkOutTime" @change="bindDateChange">
                        <view class="right">
                            <span class="right_text">
                                {{ form.checkOutTime || "请选择" }}
                            </span>
                            <uni-icons type="forward" size="18" color="#999999" />
                        </view>
                    </picker>
                </template>
            </uni-list-item>
        </uni-list>
        <div class="reason">
            <span class="title">{{ adjustTypeList[form.adjustType] }}原因</span>
            <textarea class="textarea_class" auto-height :maxlength="200" v-model="form.reason" placeholder-style="color:#999999" placeholder="请输入" />
            <div class="number_words">{{ form.reason.length }} / 200</div>
        </div>
        <div class="buttom_class">
            <button @click="confirm" :class="{ btn_disable: !btnDisable }">确认{{ form.adjustType === 1 ? "换寝" : "退寝" }}</button>
        </div>
        <SelectPopup @closePopup="closePopup" ref="transferRef" :list="typeList" title="选择调动方式" />
        <TipPopup ref="tipChangingBeds" :title="`${'确认以下人员' + adjustTypeList[form.adjustType] + '？'}`" :showTitle="true" confirmBtnText="确认选择" @closePopup="tipClose" @confirm="tipConfirm">
            <template #tipText>
                <div class="tip_popup_class">
                    <img v-if="form.avatar" :src="form.avatar" alt="" class="image" />
                    <text v-else class="avatar_box">{{ form.studentName?.slice(-2) }}</text>
                    <text class="name">{{ form.studentName || "-" }}</text>
                    <text class="text" v-if="form.adjustType === 1">切换至 {{ form.changeBed }} 寝室</text>
                    <text v-else>{{ form.checkOutTime }} 退寝</text>
                </div>
            </template>
        </TipPopup>
    </div>
</template>

<script setup>
import SelectPopup from "../components/selectPopup.vue"
import TipPopup from "../components/tipPopup.vue"
import { adjustTypeList } from "../utils/staticData"

const transferRef = ref()
const tipChangingBeds = ref()
const typeList = ref([
    { name: "换寝", value: 1 },
    { name: "退寝", value: 2 }
])

const form = reactive({
    adjustType: 1,
    reason: "",
    checkOutTime: "",
    changeRoom: "",
    changeBed: "",
    changeStudent: "",
    originalStudent: "",
    studentName: "",
    avatar: ""
})

onLoad((parame) => {
    form.originalStudent = parame.id || ""
    form.studentName = parame.name || ""
    form.avatar = parame.avatar || ""
})

const back = () => {
    uni.navigateBack()
}

function typeClick() {
    transferRef.value.open()
}

const routeObj = {
    person: "/apps/dormManage/selectPersonal/index",
    bed: "/apps/dormManage/dormTransfer/selectBuild/index"
}

const toPage = (val) => {
    console.log("routeObj[val]", routeObj[val])
    if (!routeObj[val]) return
    navigateTo({
        url: routeObj[val],
        query: {
            originalStudent: form.studentName,
            adjustType: form.adjustType
        }
    })
}

function rulrFn(isShow = true) {
    if (form.adjustType !== null) {
        if (form.adjustType === 1 && form.changeRoom && form.changeBed && form.originalStudent) {
            return true
        } else if (form.adjustType === 2 && form.checkOutTime && form.originalStudent) {
            return true
        } else {
            if (isShow) {
                uni.showToast({
                    title: "请填写完整表单",
                    icon: "none"
                })
            }
            return false
        }
    } else {
        if (isShow) {
            uni.showToast({
                title: "请填写完整表单",
                icon: "none"
            })
        }
        return false
    }
}

const btnDisable = computed(() => {
    return rulrFn(false)
})

function confirm() {
    if (rulrFn()) {
        tipChangingBeds.value.open()
    }
}

function bindDateChange(val) {
    form.checkOutTime = val.detail?.value || null
}

function closePopup(obj) {
    if (!obj) return
    form.adjustType = obj.value
}
uni.$on("dormTransfer", function (data) {
    if (data && data.studentData) {
        const { studentData } = data
        form.originalStudent = studentData.originalStudent || ""
        form.studentName = studentData.studentName || ""
        form.avatar = studentData.avatar || ""
    }
    if (!Object.keys(data).length) return
    Object.assign(form, data)
})

function tipClose() {
    tipChangingBeds.value.closeDialog()
}

function tipConfirm() {
    http.post("/app/dormitory/dormitoryRoomAdjustment/adjust", form).then(async (res) => {
        await uni.showToast({
            title: `${form.adjustType == 1 ? "换寝" : "退寝"}成功`
        })
        setTimeout(() => {
            back()
        }, 1000)
    })
}
</script>

<style lang="scss" scoped>
.dorm_transfer {
    min-height: calc(100vh - 80rpx);
    background: #f0f2f5;
    .form_item {
        .left {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
            min-width: 200rpx;
            .must {
                color: #f5222d;
            }
        }
        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            .right_text {
                font-size: 28rpx;
                font-weight: 400;
                color: #999999;
                line-height: 40rpx;
            }
        }
    }
    .reason {
        min-height: 162rpx;
        background: #ffffff;
        margin-top: 20rpx;
        padding: 28rpx;
        .title {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
        .textarea_class {
            padding-top: 20rpx;
            width: 100%;
        }
        .number_words {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
            line-height: 52rpx;
            text-align: right;
            margin-top: 20rpx;
        }
    }
    .buttom_class {
        margin-top: 40rpx;
        padding: 0rpx 30rpx;
        button {
            background-color: #4566d5;
            color: #ffffff;
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            line-height: 92rpx;
        }
        .btn_disable {
            background-color: #d8d8d8;
            pointer-events: none;
        }
    }
}
.tip_popup_class {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    .image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        overflow: hidden;
    }
    .name {
        padding: 30rpx 0;
    }
    .avatar_box {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background-color: #4566d5;
        color: #fff;
        line-height: 120rpx;
        text-align: center;
    }
}
</style>

<template>
    <view class="allot_room_container">
        <uni-nav-bar fixed statusBar>
            <view class="top_nav_bar">
                <view class="top_text">选择床位</view>
            </view>
        </uni-nav-bar>
        <view class="top_box">
            <uv-steps :current="current">
                <uv-steps-item :title="adjustTypeList[building.one]"></uv-steps-item>
                <uv-steps-item :title="building.two"></uv-steps-item>
                <uv-steps-item :title="building.three"></uv-steps-item>
            </uv-steps>
        </view>
        <!-- 内容信息 -->
        <view class="main_box">
            <view class="title">请选择您要分配的床位 </view>
            <view class="msg_box">
                <view class="top_text">{{ building.name }}</view>
                <view class="btn_text">
                    <text>合计寝室：{{ building.totalRoomCount }}间</text>
                    <text>已住：{{ building.checkInPeopleCount }}人</text>
                    <text>剩余床位：{{ building.residueBedCount }}个</text>
                </view>
            </view>
            <!-- 选择按钮 -->
            <view class="select_box">
                <selectBtn :list="btnList" v-model:value="selVal" @handleClick="selectClick"></selectBtn>
            </view>

            <!-- 选项卡开始 -->
            <view class="tabs_box">
                <uv-vtabs :chain="true" :list="list" :height="height" hdHeight="600rpx" keyName="roomNum">
                    <template v-for="(item, index) in list" :key="index">
                        <uv-vtabs-item :index="index">
                            <bed-select :multiple="false" v-model:value="item.bedNo" :list="item.children"></bed-select>
                        </uv-vtabs-item>
                    </template>
                </uv-vtabs>
            </view>

            <!-- 选项卡结束 -->
        </view>

        <!-- 底部按钮 -->
        <div class="footer_box">
            <view class="btn_group">
                <button class="yd_plain" @click="previous">上一步</button>
                <button class="yd_btn_primary" @click="next">确认</button>
            </view>
        </div>
    </view>
</template>
<script setup>
import selectBtn from "../../components/selectBtn.vue"
import bedSelect from "./components/bedSelect.vue"
import { adjustTypeList } from "../../utils/staticData"

const state = reactive({})
// 楼栋信息
let building = reactive({
    atLeastBedCount: 0,
    checkInPeopleCount: 0,
    id: "",
    name: "",
    residueBedCount: 0,
    totalRoomCount: 0
})

let info = reactive({
    one: "",
    two: "",
    three: "选择床位"
})

let current = ref(2)
let selVal = ref(1)
const btnList = ref([]) // 选择楼层按钮
const list = ref([]) // 房间/床位

const height = computed(() => uni.getSystemInfoSync().windowHeight - uni.upx2px(800))

onLoad((params) => {
    building = params
    info.two = params.originalStudent ? `${"换寝对象：" + params.originalStudent}` : "-"
    info.one = params.one ? params.one : "-"
    info.three = params.three ? params.three : "-"
    getDormFloor()
})

// 获取楼层
function getDormFloor() {
    http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryFloor", {
        buildingId: building.id
    }).then((res) => {
        if (res.data) {
            const arr = res.data.map((item) => {
                return {
                    ...item,
                    value: item.floor,
                    name: item.floorName
                }
            })
            btnList.value = arr || []
            getDormRoom(arr[0])
        }
    })
}

// 获取房间
function getDormRoom(item) {
    http.post("/app/dormitory/intelligentDormitorySeparation/dormitoryRoom", {
        buildingId: building.id,
        floor: item.floor,
        filterEmptyBed: false // 筛选出空床位 换寝对象是true,换寝床位是false
    }).then((res) => {
        list.value = res.data || []
    })
}

// 选择楼层
const selectClick = (item) => {
    getDormRoom(item)
}

const previous = () => {
    uni.navigateBack()
}

const next = () => {
    const arr = []
    list.value.forEach((i) => {
        if (i.bedNo) {
            arr.push(i)
        }
    })
    const roomInfo = arr[0]
    const bedInfo = []
    roomInfo?.children.forEach((item) => {
        if (item.checked) {
            bedInfo.push({ ...item, roomId: arr[0].roomId })
        }
    })

    const params = {
        changeStudent: bedInfo[0].studentId,
        changeBed: bedInfo[0].bedNo,
        changeRoom: bedInfo[0].roomId
    }

    uni.$emit("dormTransfer", params)
    uni.navigateBack({ delta: 2 })
}
</script>
<style lang="scss" scoped>
.allot_room_container {
    background-color: #f0f2f5;
    :deep(.uni-navbar__content) {
        border-bottom-color: transparent !important;
    }
    .top_nav_bar {
        text-align: center;
        margin: auto;
        .top_text {
            font-size: 32rpx;
            font-weight: 600;
        }
    }
    .top_box {
        padding: 40rpx 0;
        :deep(.uv-steps-item__wrapper) {
            background-color: #f0f2f5;
        }
    }
    .main_box {
        background-color: #fff;
        border-radius: 40rpx 40rpx 0 0;
        padding: 40rpx 0rpx 0rpx 0rpx;
        height: calc(100vh - 400rpx);
        overflow: hidden;
        .title {
            text-align: center;
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            padding-bottom: 40rpx;
        }
        .msg_box {
            color: #333333;
            padding: 0 28rpx;
            border-bottom: 1rpx solid #d9d9d9;
            .top_text {
                font-size: 32rpx;
                font-weight: 600;
            }
            .btn_text {
                display: flex;
                justify-content: space-between;
                font-size: 28rpx;
                padding: 16rpx 0 40rpx 0;
            }
        }
        .select_box {
            border-bottom: 1rpx solid #d9d9d9;
        }
        .tabs_box {
            .item_box {
                width: 200rpx;
                height: 200rpx;
            }
        }
        .sele_btn_text {
            font-size: 28rpx;
            font-weight: normal;
            padding-top: 16rpx;
        }
    }

    .footer_box {
        position: fixed;
        width: 100%;
        box-sizing: border-box;
        bottom: 0rpx;
        padding: 30rpx;
        background-color: #fff;

        .btn_group {
            display: flex;
            justify-content: space-between;
            .yd_plain {
                background-color: #fff;
                color: #4566d5;
                border: 1rpx solid #4566d5;
            }
            .yd_btn_primary {
                background-color: #4566d5;
                color: #fff;
            }
            button {
                width: 330rpx;
                font-size: 32rpx;
                margin-left: 0;
                margin-right: 0;
            }
        }
    }
}
</style>

<template>
    <div class="scoreInfo">
        <!-- 头部自定义导航栏 -->
        <view class="head">
            <uni-nav-bar fixed statusBar shadow left-icon="left" :border="false" @clickLeft="routerBack">
                <view class="title_box"> {{ pageParams.value.week }} </view>
            </uni-nav-bar>
        </view>
        <DayScoreList :scoreObj="state.scoreObj" :totalScore="pageParams.value?.totalScore || '0分'" />
        <div class="day_list" v-if="state.list && state.list.length > 0">
            <uni-list :border="false">
                <uni-list-item v-for="(item, index) in state.list" :key="index" :title="(item.seq || index + 1) + '. ' + item.secondIndicator" :note="item.groupName" @click="notesFn(item)" link>
                    <template v-slot:footer>
                        <div class="footer_class">
                            <img class="emote" :src="item.score > 0 ? 'https://alicdn.1d1j.cn/announcement/20230706/d2528fdb14ca4be1a26b55c2b0ff4406.png' : 'https://alicdn.1d1j.cn/announcement/20230706/b25979e697554001b7efcfd46a03f550.png'" alt="" />
                            <span class="text" :class="item.score > 0 ? 'bonus_color' : 'deduct_color'">{{ item.score + "分" }}</span>
                        </div>
                    </template>
                </uni-list-item>
            </uni-list>
        </div>
        <div class="not_set" v-else>
            <img class="image" src="https://alicdn.1d1j.cn/announcement/20230706/b38b5f559f77415aa3bedebe883e09dd.png" alt="" />
            <span class="text">暂无数据</span>
        </div>
        <uni-popup ref="notesProps" type="bottom" :safe-area="false">
            <div class="notes_props">
                <div class="title">
                    <span class="text">备注</span>
                    <img class="image" @click="closeFn" src="https://alicdn.1d1j.cn/announcement/20230706/193b257d495e428e9cbe74ff02432f93.png" alt="" />
                </div>
                <div class="textarea_box">
                    <textarea :disabled="true" class="textarea" placeholder="暂无备注" :maxlength="150" auto-height v-model="state.notes"></textarea>
                </div>
            </div>
        </uni-popup>
    </div>
</template>

<script setup>
import DayScoreList from "../components/dayScoreList.vue"

const pageParams = {}
onLoad((params) => {
    pageParams.value = params
})
const notesProps = ref()
const state = reactive({
    notes: "",
    scoreObj: {
        totalAddScore: 0,
        totalSubtractScore: 0
    },
    list: []
})
function notesFn(item) {
    state.notes = item.remark
    notesProps.value.open()
}

function dayInfoFn() {
    const { activityId, cycleId, targetId, backUpDate } = pageParams.value
    const params = {
        activityId, // 活动标识
        cycleId, // 周期标识
        targetId, // 我的班级
        scoreTime: backUpDate
    }
    http.post("/app/moralEducationActivityMobile/scoringDetail", params).then((res) => {
        state.scoreObj = res?.data || {}
        state.list = res.data?.lineList || []
        // state.list = res.data;
    })
}

function routerBack() {
    uni.navigateBack()
}

function closeFn() {
    notesProps.value.close()
}
onMounted(() => {
    // dayInfoFn();
})
</script>

<style lang="scss" scoped>
.head {
    .title_box {
        width: 100%;
        text-align: center;
        line-height: 88rpx;
        font-size: 34rpx;
        font-family:
            PingFangSC-Medium,
            PingFang SC;
        font-weight: 500;
        color: #333333;
    }
}
.day_list {
    max-height: calc(100vh - 350rpx);
    overflow: auto;
    .footer_class {
        display: flex;
        align-items: center;
        .text {
            font-size: 30rpx;
            font-family:
                PingFangSC-Regular,
                PingFang SC;
            font-weight: 400;
            line-height: 42rpx;
        }
        .emote {
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
        }
        .bonus_color {
            color: #00b781;
        }
        .deduct_color {
            color: #f5222d;
        }
    }
}
.notes_props {
    min-height: 300rpx;
    max-height: 750rpx;
    overflow-y: auto;
    background: #fff;
    border-radius: 20rpx 20rpx 0rpx 0rpx;
    padding-bottom: 90rpx;

    .title {
        height: 100rpx;
        display: flex;
        align-items: center;

        .text {
            font-size: 34rpx;
            font-family:
                PingFangSC-Medium,
                PingFang SC;
            font-weight: 500;
            color: #333333;
            line-height: 48rpx;
            width: 100vh;
            text-align: center;
        }

        .image {
            height: 44rpx;
            width: 44rpx;
            margin-right: 18rpx;
        }
    }
    .textarea_box {
        padding: 30rpx;
        .textarea {
            padding: 20rpx;
            width: 94%;
            background: #f0f2f5;
            min-height: 160rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
    }
}
.not_set {
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .image {
        width: 360rpx;
        height: 210rpx;
    }
    .text {
        font-size: 26rpx;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 36rpx;
        padding-top: 30rpx;
    }
}
</style>

<template>
    <div class="count">
        <div class="item" v-for="(item, index) in list" :key="item.value + index" :style="`${'background: ' + statusBackgound[item.value]}`">
            <span class="title" :style="`${'color: ' + statusColor[item.value]}`">{{ statusTetx[item.value] }}</span>
            <div class="count_item" :style="`${'color: ' + statusColor[item.value]}`">
                <span>{{ data[item.key] || 0 }}</span
                >人
            </div>
            <img class="image" :src="statusUrl[item.value]" alt="" />
        </div>
    </div>
</template>

<script setup>
const statusBackgound = ["#FFEAD3", "#DEEFFF", "#FFEBEC"]
const statusColor = ["#FC941F", "#3896EA", "#F5222D"]
const statusTetx = ["晚归", "请假", "未归寝"]
const statusUrl = ["https://alicdn.1d1j.cn/announcement/20230724/0a4f8ac398ec40e69347733f76c383e4.png", "https://alicdn.1d1j.cn/announcement/20230724/8d51c529cb9a4e27b1a9db508ee18baf.png", "https://alicdn.1d1j.cn/announcement/20230724/ead431c4e77b4669a425ecc09519061b.png"]

defineProps({
    data: {
        type: Object,
        default: {}
    }
})

const list = ref([
    {
        value: 0,
        key: "beLateNum"
    },
    {
        value: 1,
        key: "leaveNum"
    },
    {
        value: 2,
        key: "absenteeismNum"
    }
])
</script>

<style lang="scss" scoped>
.count {
    width: 100%;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    height: 208rpx;
    margin: 28rpx 0rpx;

    .item {
        width: 162rpx;
        height: 125rpx;
        border-radius: 20rpx;
        padding: 24rpx 28rpx;
        position: relative;
        .title {
            font-size: 26rpx;
            font-weight: 400;
            line-height: 36rpx;
        }
        .count_item {
            font-size: 40rpx;
            line-height: 58rpx;
            margin-top: 22rpx;
        }
        .image {
            width: 58rpx;
            height: 60rpx;
            position: absolute;
            top: 4rpx;
            right: 4rpx;
        }
    }
}
</style>

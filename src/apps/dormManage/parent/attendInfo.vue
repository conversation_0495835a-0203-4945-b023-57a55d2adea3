<template>
    <div class="attend_container_box">
        <view class="top">
            <view class="left_box">
                <text class="left">{{ attendInfo.studentName.charAt(0) }}</text>
                <text>{{ attendInfo.studentName }} - {{ attendInfo.classesName }}</text>
            </view>
            <text>{{ attendInfo.bedNum }}</text>
        </view>
        <view class="main">
            <view class="box" v-for="(item, index) in dataList" :key="index">
                <view>
                    <text>{{ item.sequence }}</text>
                    <text class="center">{{ item.requiredTime }}</text>
                    <text>{{ item.signInTime }}</text>
                </view>
                <view :style="{ color: changeColor[item.status] }">{{ changeText[item.status] }}</view>
            </view>
        </view>
        <uni-load-more iconType="auto" :status="status" />
    </div>
</template>

<script setup>
import { onReachBottom } from "@dcloudio/uni-app"
import useQueryList from "../hooks/useQueryList.js"

const { getList, dataList, status } = useQueryList()

const attendInfo = reactive({})

let studentId = ""
onLoad((options) => {
    studentId = options.studentId
    getList(getPage, { studentId }).then((res) => {
        Object.assign(attendInfo, res.data)
    })
})

const changeText = {
    0: "正常",
    1: "未归寝",
    2: "晚归",
    5: "请假",
    6: "未打卡"
}

const changeColor = {
    0: "#00B781",
    1: "#F5222D",
    2: "#FC941F",
    5: "#3896EA",
    6: "#5534F0"
}

// 获取宿舍列表
const getPage = (params) => {
    return http.post("/app/dormitory/attendance/eltern/page", params)
}

onReachBottom(() => {
    getList(getPage, { studentId })
})
</script>

<style lang="scss" scoped>
.attend_container_box {
    padding: 0 28rpx;
    .top {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        color: #333;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #d9d9d9;
        .left_box {
            display: flex;
            align-items: center;
            .left {
                display: inline-block;
                width: 88rpx;
                height: 88rpx;
                flex-shrink: 0;
                border-radius: 50%;
                overflow: hidden;
                line-height: 88rpx;
                text-align: center;
                color: #fff;
                background-color: #00b781;
                margin-right: 30rpx;
            }
        }
    }
    .main {
        .box {
            font-size: 28rpx;
            color: #333;
            padding: 30rpx 0;
            border-bottom: 1rpx solid #d9d9d9;
            display: flex;
            justify-content: space-between;
            .center {
                margin: 0 15rpx;
            }
        }
    }
}
</style>

<template>
    <view class="teacher_detail_container">
        <view class="top_box">
            <view class="left_text">
                <img :src="stuDetail.img" v-if="stuDetail.img" />
                <text v-else>{{ "蒙文敬".charAt(0) }}</text>
            </view>
            <view class="right_text">
                <view>蒙文敬</view>
                <view class="btn_text">
                    <text>男</text>
                    <text>六年级2班</text>
                </view>
            </view>
        </view>
        <view class="main_box">
            <msgList :list="list">
                <template #empty>
                    <view class="empty"></view>
                </template>
                <template #title>
                    <view class="title">住宿信息</view>
                </template>
            </msgList>
        </view>
    </view>
</template>

<script setup>
import msgList from "../../components/msgList.vue"

const stuDetail = reactive({
    img: ""
})

onLoad((params) => {})

const list = ref([
    { name: "手机号", value: "17854279147", prop: "codeName" },
    { name: "人员类型", value: "工勤人员", prop: "status" },
    { name: "职位状态", value: "在职", prop: "way" },
    { name: "工号", value: "5889", prop: "val2" }
])

const handleTransfer = () => {
    console.log("寝室调动")
}
</script>

<style lang="scss" scoped>
.teacher_detail_container {
    background-color: #f0f2f5;
    padding-bottom: 40rpx;
    .top_box {
        background-color: #fff;
        padding: 40rpx 28rpx;
        display: flex;
        .left_text {
            display: inline-block;
            width: 130rpx;
            height: 130rpx;
            text-align: center;
            line-height: 130rpx;
            color: #fff;
            font-size: 32rpx;
            border-radius: 50%;
            background-color: #4566d5;
            font-weight: 600;
            margin-right: 24rpx;
        }
        .right_text {
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .btn_text {
                font-size: 28rpx;
                font-weight: normal;
                color: #333;
                text {
                    &:first-of-type {
                        padding-right: 16rpx;
                        border-right: 1rpx solid #d9d9d9;
                        margin-right: 16rpx;
                    }
                }
            }
        }
    }
    .main_box {
        margin-top: 20rpx;
        padding: 0 28rpx;
        background-color: #fff;
        .title {
            color: 28rpx;
            font-weight: 600;
            padding: 28rpx 0;
            background-color: #fff;
            border-bottom: 1rpx solid #d9d9d9;
        }
        .empty {
            height: 16rpx;
            background-color: #f0f2f5;
            margin: 0 -28rpx;
        }
    }
}
</style>

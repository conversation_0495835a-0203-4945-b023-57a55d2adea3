<template>
    <view class="edit_dorm">
        <z-paging>
            <uni-list :border="false" class="form_list">
                <uni-list-item class="form_item">
                    <template v-slot:header>
                        <view class="left">
                            <span class="must">*</span>
                            寝室号
                        </view>
                    </template>
                    <template v-slot:footer>
                        <view class="right" @click="itemChange('寢室号', infoDetail.roomNo, 'roomNo')">
                            <span class="right_text">{{ infoDetail.roomNo }}</span>
                            <uni-icons type="forward" size="18" color="#999999" />
                        </view>
                    </template>
                </uni-list-item>
                <!-- <uni-list-item class="form_item">
			  <template v-slot:header>
				<view class="left"> 场地名称 </view>
			  </template>
			  <template v-slot:footer>
				<view class="right" @click="itemChange('场地名称', infoDetail.roomName, 'roomName')">
				  <span class="right_text">{{ infoDetail.roomName }}</span>
				  <uni-icons type="forward" size="18" color="#999999" />
				</view>
			  </template>
			</uni-list-item> -->
                <uni-list-item class="form_item">
                    <template v-slot:header>
                        <view class="left">
                            <!-- <span class="must">*</span> -->
                            寝室类型
                        </view>
                    </template>
                    <template v-slot:footer>
                        <view class="right" @click="selectDormType">
                            <span class="right_text">{{ infoDetail.roomType }}人间</span>
                            <uni-icons type="forward" size="18" color="#999999" />
                        </view>
                    </template>
                </uni-list-item>
                <uni-list-item class="form_item" clickable @click="handleDormLeader">
                    <template v-slot:header>
                        <view class="left">
                            <!-- <span class="must">*</span> -->
                            寝室长
                        </view>
                    </template>
                    <template v-slot:footer>
                        <view class="right">
                            <span class="right_text">{{ infoDetail.roomAdministrator }}</span>
                            <uni-icons type="forward" size="18" color="#999999" />
                        </view>
                    </template>
                </uni-list-item>
            </uni-list>
        </z-paging>
        <SelectPopup ref="selectDormTypeRef" :list="dormTypelist" @closePopup="seleClosePop" title="请选择" />
        <TipPopup ref="tipDomePopup" @closePopup="tipClose" @confirm="tipConfirm">
            <template #tipText>
                <div>该寝室已住{{ infoDetail.lived }}人，请减少入住学生</div>
            </template>
        </TipPopup>
    </view>
</template>

<script setup>
import SelectPopup from "../../components/selectPopup.vue"
import TipPopup from "../../components/tipPopup.vue"

const infoDetail = reactive({})

const selectDormTypeRef = ref(null)
const dormTypelist = ref([])

// 获取宿舍类型
http.post("/app/SystemDict/get", ["dormitory_room_type"]).then((res) => {
    dormTypelist.value = res.data[0].list.map((i) => ({ ...i, name: i.label }))
})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        infoDetail[key] = decodeURIComponent(options[key])
    })
})

const handleDormLeader = () => {
    navigateTo({
        url: "/apps/dormManage/dormInfo/dormLeader/index",
        query: { id: infoDetail.id }
    })
}

function selectDormType() {
    selectDormTypeRef.value.open()
}

const tipDomePopup = ref(null)
const openPopup = () => {
    tipDomePopup.value.open()
}

function tipClose() {
    tipDomePopup.value.closeDialog()
    return
}

function tipConfirm() {
    tipDomePopup.value.closeDialog()
    return
}

const seleClosePop = async (val) => {
    if (!val) return
    if (parseInt(infoDetail.lived) > val.value) {
        val.isCheck = false
        openPopup()
        return
    }
    const params = {
        id: infoDetail.id,
        roomType: val.value
    }
    await http.post("/app/dormitory/managerManage/updateRoomInfo", params)
    infoDetail.roomType = val.value
    uni.showToast({
        title: "修改成功",
        icon: "none"
    })
}

function itemChange(title, text, code) {
    const params = {
        title: title,
        text: text,
        code,
        id: infoDetail.id
    }
    navigateTo({
        url: "/apps/dormManage/dormInfo/editDorm/textBox",
        query: params
    })
}

uni.$on("editDorm", (data) => {
    infoDetail[data.code] = data.text
})
</script>

<style lang="scss" scoped>
.edit_dorm {
    background: #f0f2f5;
    .form_list {
        // min-height: 500rpx;
        background: #ffffff;
        padding: 0rpx 28rpx;
        :deep(.uni-list-item__container) {
            padding: 12rpx 0rpx !important;
        }
    }
    .form_item {
        min-height: 110rpx;
        .left {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
            line-height: 40rpx;
            .must {
                color: #f5222d;
            }
        }
        .right {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .right_text {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
                padding-right: 14rpx;
            }
        }
    }
    .buttom_class {
        margin-top: 40rpx;
        background: #ffffff;
        height: 166rpx;
        padding: 0rpx 30rpx;
        display: flex;
        width: 92%;
        position: fixed;
        bottom: 0rpx;
        left: 0rpx;
        align-items: center;
        button {
            width: 100%;
            background-color: #4566d5;
            color: #ffffff;
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            line-height: 92rpx;
        }
    }
}
</style>

<template>
    <div class="text_box">
        <z-paging>
            <template #top>
                <view class="head">
                    <uni-nav-bar fixed statusBar left-icon="left" :border="false" :leftWidth="100" :rightWidth="100">
                        <template v-slot:left>
                            <span class="head_left" @click="back">取消</span>
                        </template>
                        <view class="title_box">{{ editData.title }}</view>
                    </uni-nav-bar>
                </view>
            </template>
            <div class="reason">
                <textarea class="textarea_class" auto-height :maxlength="20" v-model="editData.text" placeholder-style="color:#999999" placeholder="请输入" />
                <div class="number_words">{{ editData.text.length }} / 20</div>
            </div>
            <template #bottom>
                <div class="buttom_class">
                    <button @click="save">保存</button>
                </div>
            </template>
        </z-paging>
    </div>
</template>

<script setup>
const editData = reactive({})

onLoad((options) => {
    Object.keys(options).forEach((key) => {
        editData[key] = decodeURIComponent(options[key])
    })
})

const save = async () => {
    const params = {
        id: editData.id,
        [editData.code]: editData.text
    }
    await http.post("/app/dormitory/managerManage/updateRoomInfo", params)
    uni.$emit("editDorm", { code: editData.code, text: editData.text })
    back()
}

function back() {
    uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.text_box {
    min-height: 100vh;
    background: #f0f2f5;
    .head {
        .head_left {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
        }
        .title_box {
            width: 100%;
            text-align: center;
            line-height: 88rpx;
            font-size: 34rpx;
            font-weight: 600;
            color: #333333;
        }
    }
    .reason {
        min-height: 112rpx;
        margin-top: 20rpx;
        background: #ffffff;
        margin-top: 20rpx;
        padding: 28rpx;
        .textarea_class {
            width: 100%;
        }
        .number_words {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
            line-height: 52rpx;
            text-align: right;
            margin-top: 20rpx;
        }
    }
    .buttom_class {
        margin-top: 40rpx;
        background: #ffffff;
        height: 166rpx;
        padding: 0rpx 30rpx;
        display: flex;
        align-items: center;
        button {
            width: 100%;
            background-color: #4566d5;
            color: #ffffff;
            border-radius: 10rpx;
            font-size: 32rpx;
            height: 92rpx;
            font-weight: 400;
            line-height: 92rpx;
        }
    }
}
</style>

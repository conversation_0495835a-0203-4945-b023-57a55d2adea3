<template>
    <view class="teacher_detail_container">
        <z-paging>
            <template #top>
                <view class="top_box">
                    <view class="left_text">
                        <img class="img" :src="stuDetail.avatar" v-if="stuDetail.avatar" />
                        <text v-else>{{ stuDetail.name?.charAt(0) }}</text>
                    </view>
                    <view class="right_text">
                        <view>{{ stuDetail.name }}</view>
                        <view class="btn_text">
                            <text>{{ stuDetail.gender == 1 ? "男" : "女" }}</text>
                            <text>{{ stuDetail.className }}</text>
                        </view>
                    </view>
                </view>
            </template>
            <view class="main_box">
                <msgList :list="list">
                    <template #empty>
                        <view class="empty"></view>
                    </template>
                    <template #title>
                        <view class="title">住宿信息</view>
                    </template>
                </msgList>
            </view>
            <template #bottom>
                <view class="footer">
                    <button @click="handleTransfer">寝室调动</button>
                </view>
            </template>
        </z-paging>
    </view>
</template>
<script setup>
import msgList from "../../components/msgList.vue"

const stuDetail = reactive({})

const params = {}

onLoad((params) => {
    getStudentDetail(params.id)
})

async function getStudentDetail(id) {
    let { data } = await http.get("/app/student/getInfo", { id })
    Object.assign(stuDetail, data)
    params.id = data.id
    params.name = data.name
    params.avatar = data.avatar || ""
    data = { ...data, ...data.stayInformation }
    list.value.forEach((i) => {
        if (data[i.prop]) {
            i.value = data[i.prop]
            if (i.prop === "roomType") {
                i.value = data[i.prop] + "人间"
            }
        }
    })
}

const list = ref([
    { name: "学籍号", value: "-", prop: "studentCode" },
    {
        name: "学籍状态",
        value: "-",
        prop: "status",
        filter: (val) => changeText("status", val)
    },
    {
        name: "就读方式",
        value: "-",
        prop: "accommodation",
        filter: (val) => changeText("accommodation", val)
    },
    { name: "入学日期", value: "-", prop: "admissionDate" },
    { name: "学生手机号码", value: "-", prop: "phone" },
    { name: "学号", value: "-", prop: "studentNo" },
    { slot: "empty" },
    { slot: "title" },
    {
        name: "入住状态",
        value: "-",
        prop: "checkInStatus",
        filter: (val) => changeText("checkInStatus", val)
    },
    { name: "寝室床位", value: "-", prop: "bedNo" },
    { name: "寝室类型", value: "-", prop: "roomType" },
    { name: "入住日期", value: "-", prop: "checkInDateStr" },
    { name: "宿舍长", value: "-", prop: "roomAdministratorName" },
    { name: "退宿时间", value: "-", prop: "checkOutDateStr" }
    // { name: '宿管长', value: '-', prop: 'buildingAdministratorName' },
])

const status = {
    1: "在读",
    2: "休学",
    3: "退学",
    4: "停学",
    5: "复学",
    6: "流失",
    7: "毕业",
    8: "结业",
    9: "肄业",
    10: "转学（转出）",
    11: "死亡",
    12: "保留入学资格",
    13: "公派出国",
    14: "开除",
    15: "下落不明",
    99: "其他"
}

const accommodation = {
    1: "走读",
    2: "住校",
    3: "借宿",
    4: "其他"
}

const checkInStatus = {
    1: "未入住",
    2: "已入住",
    3: "已退宿"
}

const textGroup = {
    status,
    accommodation,
    checkInStatus
}

function changeText(flag, val) {
    return textGroup[flag][val]
}

const handleTransfer = () => {
    navigateTo({
        url: "/apps/dormManage/dormTransfer/index",
        query: params
    })
}
</script>
<style lang="scss" scoped>
.teacher_detail_container {
    background-color: #f0f2f5;
    .top_box {
        background-color: #fff;
        padding: 40rpx 28rpx;
        display: flex;
        .left_text {
            display: inline-block;
            width: 130rpx;
            height: 130rpx;
            text-align: center;
            line-height: 130rpx;
            color: #fff;
            font-size: 32rpx;
            border-radius: 50%;
            overflow: hidden;
            background-color: #4566d5;
            font-weight: 600;
            margin-right: 24rpx;
            .img {
                width: 130rpx;
                height: 130rpx;
            }
        }
        .right_text {
            font-size: 32rpx;
            color: #333;
            font-weight: 600;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .btn_text {
                font-size: 28rpx;
                font-weight: normal;
                color: #333;
                text {
                    &:first-of-type {
                        padding-right: 16rpx;
                        border-right: 1rpx solid #d9d9d9;
                        margin-right: 16rpx;
                    }
                }
            }
        }
    }
    .main_box {
        margin-top: 20rpx;
        padding: 0 28rpx;
        background-color: #fff;
        .title {
            color: 28rpx;
            font-weight: 600;
            padding: 28rpx 0;
            background-color: #fff;
            border-bottom: 1rpx solid #d9d9d9;
        }
        .empty {
            height: 16rpx;
            background-color: #f0f2f5;
            margin: 0 -28rpx;
        }
    }
    .footer {
        padding: 40rpx 30rpx;
        button {
            background-color: #f0f2f5;
            color: #4566d5;
            border: 1rpx solid #4566d5;
        }
    }
}
</style>

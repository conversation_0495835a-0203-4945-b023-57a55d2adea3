<template>
    <div class="dorm_info">
        <div class="dormitory_details">
            <div class="chamber">
                <div :class="['dormitory_number', className[InfoData.status]]">
                    {{ InfoData.roomNo }}
                </div>
                <div class="chamber_main">
                    <div class="main_top">
                        <div>{{ InfoData.roomNo }}</div>
                        <div :class="['dorm_label', className[InfoData.status]]">
                            {{ changeType[InfoData.status] }}
                        </div>
                    </div>

                    <div class="text">{{ InfoData.roomTypeInt }} 人间<span class="main_border">|</span>{{ InfoData.roomName }}</div>
                </div>
                <div class="edit" @click="editDorm">编辑</div>
            </div>
            <div class="dormitory_manage">
                <div class="dormitory_leader">
                    寝室长：<span class="leader" v-if="InfoData.roomAdministrator">{{ InfoData.roomAdministrator }}</span>
                </div>
            </div>
        </div>
        <div class="statistics">
            <div class="statistics_item moral_education">
                <div class="left">
                    <div class="title_image moral_education_title"></div>
                    <span class="text_image">
                        <text v-if="InfoData.educationBusiness">
                            最近
                            <span class="fraction">{{ InfoData.educationBusiness.recentGoal }}</span>
                            分
                        </text>
                        <text v-else>暂无评分</text>
                    </span>
                </div>
                <div class="right moral_education_background"></div>
            </div>
            <div class="statistics_item attendance">
                <div class="left">
                    <div class="title_image attendance_title"></div>
                    <span class="text_image">
                        <text v-if="InfoData.attendanceBusiness && (InfoData.attendanceBusiness.absenteeismNum > 0 || InfoData.attendanceBusiness.notSignInNum > 0)">
                            未归
                            <span class="fraction">{{ InfoData.attendanceBusiness.absenteeismNum }}</span>
                            / 未打卡
                            <span class="fraction">{{ InfoData.attendanceBusiness.notSignInNum }}</span>
                        </text>
                        <text v-else>暂未设置考勤</text>
                    </span>
                </div>
                <div class="right attendance_background"></div>
            </div>
        </div>
        <view class="main_box">
            <view class="top_text">寝室人员列表</view>
            <view class="main">
                <view class="yd_select" v-for="(item, index) in InfoData.eachStudent" :key="index">
                    <view :class="['item_box', item.id > 0 ? className[2] : className[0]]">
                        <view class="top">{{ item.bedNo }}</view>
                        <view class="bottom">
                            <view class="bottom_top" v-if="item.id > 0">
                                <view class="btm_top">{{ item.name }}</view>
                                <view class="middle_text">{{ item.classesName }}</view>
                            </view>
                            <view class="btn_btn">
                                <button @click="handleClick(item)">
                                    {{ item.id > 0 ? "查看" : "智能分寝" }}
                                </button>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </div>
</template>

<script setup>
onLoad((options) => {
    InfoData.id = options.id
    getDetail(options.id)
})

onActivated(() => {
    getDetail(InfoData.id)
})

const InfoData = reactive({
    attendanceBusiness: {
        absenteeismNum: "",
        notSignInNum: ""
    },
    eachStudent: []
})

const className = {
    2: "type_0",
    1: "type_1",
    0: "type_2"
}

const changeType = {
    2: "满员",
    0: "空房",
    1: "未满"
}

// 获取寝室明细
const getDetail = async (id) => {
    const { data } = await http.get("/app/dormitory/managerManage/detail", {
        id
    })
    Object.assign(InfoData, data)
    InfoData.attendanceBusiness = InfoData.attendanceBusiness ? InfoData.attendanceBusiness : {}
}

const handleClick = (val) => {
    // 查看详情
    if (parseInt(val.id) > 0) {
        navigateTo({
            url: "/apps/dormManage/dormInfo/studentDetail/index",
            query: { id: val.id }
        })
    } else {
        // 智能分寝
        navigateTo({
            url: "/apps/dormManage/allotRoom/index",
            query: val
        })
    }
}
function editDorm() {
    const lived = InfoData.eachStudent.filter((i) => i.id > 0).length
    const params = {
        id: InfoData.siteId,
        roomNo: InfoData.roomNo,
        roomType: InfoData.roomTypeInt,
        roomAdministrator: InfoData.roomAdministrator,
        roomAdministratorIdList: InfoData.roomAdministratorIdList,
        roomName: InfoData.roomName,
        lived
    }
    navigateTo({
        url: "/apps/dormManage/dormInfo/editDorm/index",
        query: params
    })
}
</script>

<style lang="scss" scoped>
.dorm_info {
    min-height: 94vh;
    background: #f0f2f5;

    .dormitory_details {
        display: flex;
        flex-direction: column;
        height: 280rpx;
        background: #ffffff;
        padding: 0rpx 28rpx;
        justify-content: center;

        .chamber {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .dormitory_number {
                width: 120rpx;
                height: 120rpx;
                border-radius: 50%;
                font-size: 32rpx;
                font-weight: 600;
                color: #ffffff;
                line-height: 120rpx;
                text-align: center;
                flex-shrink: 0;
            }

            .chamber_main {
                flex: 1;
                margin: 0 20rpx;
                .main_top {
                    display: flex;
                    margin-bottom: 20rpx;
                    word-break: break-all;
                    align-items: center;
                    font-size: 32rpx;
                    .dorm_label {
                        flex-shrink: 0;
                        width: 80rpx;
                        height: 36rpx;
                        background: #ffa645;
                        font-size: 24rpx;
                        font-weight: 400;
                        border-radius: 4rpx;
                        color: #ffffff;
                        line-height: 36rpx;
                        margin-left: 8rpx;
                        text-align: center;
                    }
                }
                .text {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                    .main_border {
                        color: #d9d9d9;
                        margin: 0 16rpx;
                    }
                }
            }
            .edit {
                width: 112rpx;
                height: 56rpx;
                background: #ffffff;
                border-radius: 28rpx;
                border: 1rpx solid #4566d5;
                font-size: 24rpx;
                font-weight: 400;
                color: #4566d5;
                line-height: 56rpx;
                text-align: center;
                flex-shrink: 0;
            }
        }
        .dormitory_manage {
            display: flex;
            justify-content: space-between;
            .dormitory_leader,
            .warden_chief {
                font-size: 28rpx;
                font-weight: 400;
                color: #999999;
                line-height: 40rpx;
                margin-top: 40rpx;
                .leader,
                .chief {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #333333;
                    line-height: 40rpx;
                }
            }
        }
    }
    .statistics {
        margin-top: 20rpx;
        height: 240rpx;
        background: #ffffff;
        padding: 0rpx 28rpx;
        display: flex;
        align-items: center;
        .moral_education {
            background: #fecb5f;
        }
        .attendance {
            background: #cede62;
            margin-left: 18rpx;
        }
        .statistics_item {
            width: 338rpx;
            height: 184rpx;
            border-radius: 20rpx;
            position: relative;
            .moral_education_title {
                background: url("https://alicdn.1d1j.cn/announcement/20230728/12ea85754ab544b28ac421e4e89de0aa.png") no-repeat;
                background-size: contain;
            }
            .attendance_title {
                background: url("https://alicdn.1d1j.cn/announcement/20230728/528eb07ba5414188879e3024e0228514.png") no-repeat;
                background-size: contain;
            }
            .left {
                position: absolute;
                top: 40rpx;
                left: 32rpx;

                .title_image {
                    width: 160rpx;
                    height: 32rpx;
                    margin-bottom: 40rpx;
                }
                .text_image {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 40rpx;

                    .fraction {
                        font-size: 36rpx;
                        line-height: 50rpx;
                    }
                }
            }
            .moral_education_background {
                background: url("https://alicdn.1d1j.cn/announcement/20230728/1fcbe921ab8a494b86b4445ee4088500.png") no-repeat;
                background-size: contain;
            }
            .attendance_background {
                background: url("https://alicdn.1d1j.cn/1531914651808833538/default/affc734715f9426fb55fa40696eb167e.png") no-repeat;
                background-size: contain;
            }
            .right {
                position: absolute;
                top: 20rpx;
                right: 8rpx;
                width: 110rpx;
                height: 124rpx;
            }
        }
    }
    .main_box {
        padding: 28rpx;
        background: #fff;
        margin-top: 20rpx;
        .top_text {
            padding-bottom: 28rpx;
            color: #333;
            font-size: 28rpx;
        }
        .main {
            display: flex;
            flex-wrap: wrap;
            .yd_select {
                width: 210rpx;
                box-sizing: border-box;
                border-radius: 12rpx;
                position: relative;
                cursor: pointer;
                border: 2rpx solid #d9d9d9;
                margin: 0rpx 28rpx 28rpx 0;
                &:nth-of-type(3n) {
                    margin-right: 0;
                }
                .item_box {
                    box-sizing: border-box;
                    padding-top: 16rpx;
                    text-align: center;
                    height: 280rpx;
                    color: #333;
                    border-radius: 10rpx;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                    .top {
                        width: 135rpx;
                        height: 56rpx;
                        font-size: 28rpx;
                        line-height: 56rpx;
                        border: 2rpx solid #d9d9d9;
                        border-radius: 10rpx;
                        margin: 0 auto;
                        margin-bottom: 16rpx;
                        font-weight: 600;
                        overflow: hidden;
                        background-color: #fff;
                    }
                    .bottom {
                        flex: 1;
                        font-size: 28rpx;
                        color: #333;
                        border-top: 2rpx solid #d9d9d9;
                        border-radius: 30rpx 30rpx 10rpx 10rpx;
                        display: flex;
                        flex-direction: column;
                        background: #fff;
                        .bottom_top {
                            height: calc(100% - 74rpx);
                            display: flex;
                            flex-direction: column;
                            justify-content: space-evenly;
                        }
                        .btm_top {
                            font-weight: 600;
                            font-size: 28rpx;
                            color: #333;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                        .middle_text {
                            color: #666;
                            font-size: 24rpx;
                        }
                        button {
                            height: 50rpx;
                            border-radius: 36rpx;
                            font-size: 24rpx;
                            line-height: 46rpx;
                            background-color: #fff;
                            display: inline-block;
                        }
                        .btn_btn {
                            position: absolute;
                            width: 100%;
                            bottom: 16rpx;
                            margin: 0 auto;
                            uni-button {
                                &::after {
                                    border: none;
                                }
                            }
                        }
                    }
                }
                .type_0 {
                    background-color: #ffa645;
                    .dormitory_number {
                        color: #fff;
                    }
                    button {
                        color: #ffa645;
                        border: 2rpx solid #ffa645;
                    }
                }
                .type_1 {
                    background-color: #4566d5;
                    border: 1rpx solid #4566d5;
                    .dormitory_number {
                        color: #4566d5;
                    }
                    button {
                        color: #4566d5;
                        border: 2rpx solid #4566d5;
                    }
                }
                .type_2 {
                    background: #1ec3a1;
                    .dormitory_number {
                        color: #1ec3a1;
                    }
                    button {
                        color: #1ec3a1;
                        border: 2rpx solid #1ec3a1;
                    }
                }
            }
        }
    }
    .type_0 {
        background-color: #ffa645 !important;
        .dormitory_number {
            color: #fff;
        }
        button {
            color: #ffa645;
            border: 2rpx solid #ffa645;
        }
    }
    .type_1 {
        background-color: #4566d5 !important;
        border: 1rpx solid #4566d5;
        .dormitory_number {
            color: #4566d5;
        }
        button {
            color: #4566d5;
            border: 2rpx solid #4566d5;
        }
    }
    .type_2 {
        background: #1ec3a1 !important;
        .dormitory_number {
            color: #1ec3a1;
        }
        button {
            color: #1ec3a1;
            border: 2rpx solid #1ec3a1;
        }
    }
}
</style>

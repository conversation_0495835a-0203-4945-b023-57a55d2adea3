<template>
    <view>
        <web-view src="/packages/static/local.html" @message="appcallBack" />
    </view>
</template>
<script setup>
const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()
const appcallBack = (res) => {
    const re = res.detail.data[0]
    //接收网页传给uniapp组件的参数
    uni.navigateBack({
        delta: 1,
        success: () => {
            eventChannel.emit("varify", {
                ticket: re.ticket,
                randStr: re.randStr
            })
        }
    })
}
</script>

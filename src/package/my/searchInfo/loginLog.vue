<template>
    <view class="login_log">
        <z-paging ref="paging" v-model="dataList" @query="queryList" :auto="false">
            <view class="container">
                <view class="item_box" v-for="(item, index) in dataList" :key="index">
                    <view class="top">{{ item.title }}</view>
                    <view class="item">登录时间：{{ item.createTime || "-" }}</view>
                    <view class="item">登录地址：{{ item.location || "-" }}</view>
                    <view class="item">登录设备：{{ item.clientModel || "-" }}</view>
                    <view class="item">系统型号：{{ item.clientSystem || "-" }}</view>
                </view>
            </view>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </view>
</template>

<script setup>
import dayjs from "dayjs"
import { nextTick } from "vue"
const paging = ref(null)
const dataList = ref([])
const obj = ref({})

// 前几天天日期区间（自定义）
function getLastDaysRange(num) {
    const sevenDaysAgo = dayjs().subtract(num, "day")
    return sevenDaysAgo.format("YYYY-MM-DD")
}
const queryList = async (pageNo, pageSize) => {
    const params = {
        pageNo,
        pageSize,
        ...obj.value
    }
    http.post("/app/userInfoCollect/loginPage", params).then((res) => {
        paging.value.complete(res.data.list)
    })
}

onLoad((options) => {
    obj.value = {
        // code: options.code,
        createEndTime: dayjs().format("YYYY-MM-DD"),
        createStartTime: getLastDaysRange(Number(options.time))
    }
    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.login_log {
    .container {
        padding: 20rpx 20rpx;
        background: #f9faf9;
        .item_box {
            padding: 0 30rpx;
            padding-bottom: 30rpx;
            background: $uni-bg-color;
            border-radius: 20rpx;
            box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(191, 191, 191, 0.1);
            margin-bottom: 20rpx;
            .top {
                font-size: 30rpx;
                font-weight: 600;
                height: 110rpx;
                line-height: 110rpx;
                border-bottom: 2rpx solid #d8d8d8;
            }
            .item {
                font-size: 26rpx;
                padding-top: 20rpx;
                .btn {
                    color: $uni-color-primary;
                }
            }
        }
    }
}
</style>

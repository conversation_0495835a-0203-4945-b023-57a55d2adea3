<template>
    <div class="sign">
        <uni-nav-bar left-icon="left" background-color="#fff" statusBar fixed title="电子签名" @clickLeft="back"
            :border="false"> </uni-nav-bar>

        <view class="body">
            <view class="preview" v-if="state.mode == 'preview'">
                <view class="title">签名预览</view>
                <view class="img_warp">
                    <img class="img" :src="state.url" />
                </view>
                <view class="btn_warp">
                    <view class="btn" @click="changeMode('edit')">{{ state.url ? "重新签名" : "签名" }}</view>
                </view>
            </view>
            <view class="edit" v-else>
                <view :disabled="state.disabled" type="primary" class="btn" :loading="state.loading"
                    @click="changeMode('preview')">
                    <text class="text">确定</text>
                </view>
                <view class="canvas_warp">
                    <VueSignaturePad :options="options" v-model="state.signatureData" ref="signaturePad">
                    </VueSignaturePad>
                </view>
                <view class="header">
                    <view style="display: flex; align-items: center;flex-direction: column;">
                        <uni-icons type="arrow-left" size="24" color="#00b781" style="transform: rotate(90deg);"
                            @click="state.mode = 'preview'"></uni-icons>
                        <view class="text" style="padding: 0;">签名预览</view>
                    </view>
                    <view style="padding: 10px 0;transform: rotate(90deg);" @click="clearSignature"> 清除 </view>
                </view>
                <!--  -->

                <!--  -->
            </view>
        </view>
        <!-- <view class="footer" v-if="state.mode == 'preview'">
            <button :disabled="state.disabled" type="primary" class="btn" :loading="state.loading" @click="submit">确定</button>
        </view> -->
        <canvas canvas-id="myCanvas" id="myCanvas" style="width:300px;height:300px;position:fixed;left:-9999px"/>
    </div>
</template>

<script setup>
import { onMounted, reactive, ref } from "vue"
import { VueSignaturePad } from "vue-signature-pad"
// https://github.com/neighborhood999/vue-signature-pad
const signaturePad = ref(null)

const options = {
    // penColor: "rgb(0, 0, 0)", // 笔的颜色
    // penWidth: 2, // 笔的宽度
    // backgroundColor: "#F4F6FA", // 背景颜色
    canvasWidth: 600, // canvas的宽度
    canvasHeight: 300, // canvas的高度
    rotate: -90
}

const state = reactive({
    disabled: false,
    loading: false,
    mode: "preview", //edit
    url: "",
    signatureData: ""
})

function back() {
    uni.navigateBack()
}

function changeMode(mode) {
    if (mode == "preview") {
        const { isEmpty, data } = signaturePad.value.saveSignature()
        if (isEmpty) {
            return uni.showToast({
                title: "签名为空，请书写后再进行添加",
                icon: "none"
            })
        } else {
            // 旋转90度
            rotateBase64Img(data, -90, (base64) => {
                const file = base64ToFile(base64, "signature.png")
                uni.showLoading({
                    title: "Loading..."
                })
                http.uploadFile("/file/common/upload", "", { folderType: "userSignature" }, file)
                    .then(async (url) => {
                        state.url = url
                        console.log(url)
                        // 保存签名
                        http.post("/app/user/e-signature/save", {
                            signatureUrl: url
                        })
                            .then((res) => {
                                state.mode = mode
                            })
                            .catch((err) => { })
                            .finally(() => {
                                uni.hideLoading()
                                // 获取新签名
                                querySignature()
                            })
                    })
                    .catch((err) => {
                        console.log(err)
                    })
                    .finally(() => {
                        nextTick(() => {
                            state.mode = mode
                        })
                    })
            })

            // console.log(data)
            // state.url = data
        }
    } else {
        state.mode = mode
    }
}

function clearSignature() {
    signaturePad.value.clearSignature()
}

function rotateBase64Img(src, edg, callback) {
    return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery();
        query.select("#myCanvas")
            .fields({ node: true, size: true })
            .exec((res) => {
                if (!res[0]?.node) {
                    reject(new Error("Canvas 节点未找到"));
                    return;
                }
                 // #ifdef H5
                const canvas =  document.createElement("canvas");
                const ctx = canvas.getContext('2d');
                // #endif

                // #ifdef MP-WEIXIN || MP-ALIPAY 
                const canvas = res[0].node;
                const ctx = canvas.getContext("2d");
                // #endif

                // 检查角度是否为 90 的倍数
                if (edg % 90 !== 0) {
                    reject(new Error("旋转角度必须是 90 的倍数！"));
                    return;
                }

                let imgW //图片宽度
                let imgH //图片高度
                let size //canvas初始大小

                if (edg % 90 != 0) {
                    console.error("旋转角度必须是90的倍数!")
                    throw "旋转角度必须是90的倍数!"
                }
                edg < 0 && (edg = (edg % 360) + 360)
                const quadrant = (edg / 90) % 4 //旋转象限
                const cutCoor = { sx: 0, sy: 0, ex: 0, ey: 0 } //裁剪坐标

                const image = new Image()

                image.onload = function () {
                    try {
                        imgW = image.width;
                        imgH = image.height;
                        size = imgW > imgH ? imgW : imgH;

                        canvas.width = size * 2;
                        canvas.height = size * 2;
                        switch (quadrant) {
                            case 0:
                                cutCoor.sx = size;
                                cutCoor.sy = size;
                                cutCoor.ex = size + imgW;
                                cutCoor.ey = size + imgH;
                                break;
                            case 1:
                                cutCoor.sx = size - imgH;
                                cutCoor.sy = size;
                                cutCoor.ex = size;
                                cutCoor.ey = size + imgW;
                                break;
                            case 2:
                                cutCoor.sx = size - imgW;
                                cutCoor.sy = size - imgH;
                                cutCoor.ex = size;
                                cutCoor.ey = size;
                                break;
                            case 3:
                                cutCoor.sx = size;
                                cutCoor.sy = size - imgW;
                                cutCoor.ex = size + imgH;
                                cutCoor.ey = size + imgW;
                                break;
                        }

                        ctx.translate(size, size);
                        ctx.rotate((edg * Math.PI) / 180);
                        ctx.drawImage(image, 0, 0);

                        var imgData = ctx.getImageData(
                            cutCoor.sx,
                            cutCoor.sy,
                            cutCoor.ex,
                            cutCoor.ey
                        );

                        if (quadrant % 2 == 0) {
                            canvas.width = imgW;
                            canvas.height = imgH;
                        } else {
                            canvas.width = imgH;
                            canvas.height = imgW;
                        }
                        ctx.putImageData(imgData, 0, 0);

                        // 确保绘制完成后再导出
                        requestAnimationFrame(() => {
                            try {
                                // H5 端导出 Base64
                                // #ifdef H5                                
                                const dataURL = canvas.toDataURL();                                
                                callback?.(dataURL);
                                resolve(dataURL);
                                // #endif

                                // 小程序端导出临时路径
                                // #ifdef MP-WEIXIN || MP-ALIPAY
                                // uni.canvasToTempFilePath({
                                //     canvas: canvas,
                                //     success: (res) => {
                                //         callback?.(res.tempFilePath);
                                //         resolve(res.tempFilePath);
                                //     },
                                //     fail: (err) => {
                                //         reject(err);
                                //     },
                                // }, this);
                                callback?.(dataURL);
                                resolve(dataURL);
                                // #endif
                            } catch (e) {
                                reject(e);
                            }
                        });
                    } catch (error) {
                        reject(error);
                    }
                };

                image.onerror = () => {
                    reject(new Error("图片加载失败"));
                };

                // 处理跨域问题（仅 H5 需要）
                if (src.startsWith("http") && !src.includes(location.host)) {
                    image.crossOrigin = "Anonymous";
                }
                image.src = src;
            });
    });
}

function base64ToFile(base64, filename) {
    // 将base64数据逗号之后的部分截取出来
    const arr = base64.split(",")
    // 获取文件的mime类型
    const mime = arr[0].match(/:(.*?);/)[1]
    // 将base64解码
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }

    // 返回File对象
    return new File([u8arr], filename, { type: mime })
}

function querySignature() {
    http.post("/app/user/e-signature/get")
        .then((res) => {
            console.log(res)
            state.url = res.data.signatureUrl
        })
        .catch((err) => { })
        .finally(() => { })
}

onMounted(() => {
    // 根据url绘制图像
    //    signaturePad.value.fromDataURL(data,{ ratio: 1, width: 400, height: 200, xOffset: 100, yOffset: 50 }, (url) => {
    //         console.log(url);
    //     })

    querySignature()
})
</script>

<style lang="scss" scoped>
.sign {
    position: relative;
    background: #f9faf9;
    height: 100vh;

    .body {
        // height: 100%;
    }

    .preview {
        background: #fff;
        padding: 15px 17px;
        margin: 10px 15px;
        border-radius: 8px;

        .title {
            font-size: 14px;
            color: #333;
        }

        .img_warp {
            border-radius: 8px;
            border: 1px solid #d8d8d8;
            margin-top: 11px;
            min-height: 200px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;

            .img {
                // width: auto;
                // height: 200px;
                // transform: rotate(270deg);
                // transform: rotate(270deg) scale(0.5);
                width: 100%;
                height: auto;
                display: block;
                // object-fit: contain;
            }
        }

        .btn_warp {
            text-align: center;
            margin-top: 14px;

            .btn {
                color: $uni-color-primary;
                padding: 10px 0;
                display: inline-block;
            }
        }
    }

    .edit {
        display: flex;
        background: #fff;
        padding: 15px 17px;
        margin: 10px 15px;
        border-radius: 8px;
        box-sizing: border-box;
        flex: 1;
        height: calc(100vh - 80px);
        position: relative;
        overflow: hidden;

        .btn {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            width: 40px;
            text-align: center;
            font-size: 16px;
            border-radius: 20px;
            height: 332px;
            margin: auto 0;
            line-height: 332px;

            .text {
                display: inline-block;
                transform: rotate(90deg);
            }
        }

        .canvas_warp {
            flex: 1;
            border-radius: 8px;
            border: 1px solid #d8d8d8;
            margin: 0 8px 0 11px;
        }

        .header {
            display: flex;
            align-items: center;
            flex-direction: column;            
            max-width: 32px;
            
            justify-content: space-between;

            .text {
                padding: 5px 0;
                writing-mode: sideways-rl;
                -webkit-writing-mode: sideways-rl; /* Chrome, Safari */
                -ms-writing-mode: tb-rl; /* IE */
                text-orientation: upright;
            }
        }

    }

    .footer {
        position: absolute;
        bottom: 25px;
        left: 16px;
        right: 16px;
        padding-bottom: env(safe-area-inset-bottom);

        .btn {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            height: 40px;
            line-height: 40px;
            font-size: 16px;
            border-radius: 20px;
        }
    }
}
</style>

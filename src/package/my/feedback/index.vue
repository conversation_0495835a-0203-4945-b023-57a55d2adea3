<template>
    <yd-page-view class="feedback" title="意见反馈" leftIcon="left">
        <div class="feedbackPage">
            <div class="contentBox">
                <div class="title">投诉反馈内容</div>
                <uni-easyinput :inputBorder="false" type="textarea" autoHeight v-model="state.content" placeholder="请输入内容"></uni-easyinput>
            </div>
            <div class="lineBox"></div>
            <div class="contentBox">
                <div class="title">上传图片</div>
                <uni-file-picker
                    limit="7"
                    fileMediatype="image"
                    :image-styles="{
                        width: 105,
                        height: 105
                    }"
                    @select="handleInputEvent"
                    @delete="handleInputDelete"
                ></uni-file-picker>
            </div>
            <div class="footBox">
                <button type="default" @click="submit" class="button">提交</button>
            </div>
        </div>
    </yd-page-view>
</template>

<script setup>
const state = reactive({
    content: "",
    imgPaths: []
})

const handleInputEvent = (e) => {
    uni.showLoading({
        title: "正在上传中..."
    })
    e.tempFiles?.forEach(async (v, index) => {
        await http
            .uploadFile("/file/common/upload", v.path, { folderType: "app" })
            .then((url) => {
                state.imgPaths.push(url)
                if (e.tempFiles.length - 1 == index) {
                    uni.hideLoading()
                }
            })
            .catch(() => {
                uni.hideLoading()
            })
            .finally(() => {
                uni.hideLoading()
            })
    })
}

const handleInputDelete = (item) => {
    state.imgPaths?.splice(item.index, 1)
}

function submit() {
    http.post("/cloud/app/feedback/create", { content: state.content, imgPaths: state.imgPaths?.join("，") }).then((res) => {
        uni.showToast({
            title: res.message,
            icon: "none"
        })
        setTimeout(() => {
            uni.navigateBack()
        }, 1500)
    })
}
</script>

<style lang="scss" scoped>
.feedbackPage {
    .contentBox {
        background-color: $uni-text-color-inverse;
        padding: 30rpx 32rpx;
        .title {
            background-color: $uni-text-color-inverse;
            font-weight: 400;
            font-size: 28rpx;
            color: #333333;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
            padding-bottom: 16rpx;
        }
    }
}
.lineBox {
    height: 20rpx;
    background: #f0f2f5;
}

.footBox {
    position: fixed;
    background-color: $uni-text-color-inverse;
    height: 166rpx;
    bottom: 0;
    left: 0;
    width: -webkit-fill-available;
    padding: 30rpx 30rpx 0 30rpx;
    .button {
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }
    .disabled_class {
        background: #ccc;
    }
}
:deep(.icon-del-box) {
    height: 36rpx;
    width: 36rpx;
}
:deep(.icon-del) {
    width: 20rpx;
}
</style>

<template>
    <yd-page-view pageBackground="#fff" :title="phone ? '设置新密码' : '修改密码'" leftIcon="left">
        <view class="edit_password_page">
            <view v-if="phone" class="new_password">
                <text class="phone_title">设置新密码</text>
                <text class="phone_content">系统监测到您的账号密码安全系数较弱，请设置新密码。</text>
            </view>
            <div class="user_phone">当前手机号:{{ phone || userInfo.phone }}</div>
            <!-- 修改手机号 -->
            <uni-forms :modelValue="formData" ref="changePhoneForm" autocomplete="off" :rules="rules" :border="true">
                <uni-forms-item name="verifyCode">
                    <uni-easyinput label="" :clearable="false" v-model="formData.verifyCode" :inputBorder="false" type="digit" placeholder="请输入验证码">
                        <template #right>
                            <a
                                @click="sendVerificationCode"
                                :style="{
                                    color: countdown > 0 ? '#CCCCCC' : '#00B781'
                                }"
                            >
                                {{ countdown > 0 ? `${countdown}秒后重新发送` : "发送验证码" }}</a
                            >
                        </template>
                    </uni-easyinput>
                </uni-forms-item>
                <uni-forms-item label="" name="newPwd">
                    <uni-easyinput :inputBorder="false" :clearable="false" :maxlength="20" type="password" v-model="formData.newPwd" placeholder="请输入包含字母、数字、特殊字符，长度为8-20个字符的密码"></uni-easyinput>
                </uni-forms-item>
                <uni-forms-item label="" name="confirmPwd" style="border-bottom: 1px #eee solid">
                    <uni-easyinput :inputBorder="false" :clearable="false" :maxlength="20" type="password" v-model="formData.confirmPwd" placeholder="请再次输入新密码"></uni-easyinput>
                </uni-forms-item>
            </uni-forms>
            <view class="change_submit">
                <button class="change_button" :class="loading ? 'disabled_class' : ''" :disabled="loading" :loading="loading" @click="subChangePwd" block type="primary" native-type="submit">确认</button>
            </view>
        </view>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import RSA from "@/utils/rsa.js"
import { onLoad } from "@dcloudio/uni-app"
const { user } = useStore()
// 用户信息
const userInfo = computed(() => user.userInfo)
const changePhoneForm = ref(null)
const phone = ref(null)

// 验证码倒计时
let countdown = ref(0)
const loading = ref(false)
const formData = ref({
    verifyCode: ""
})

const rules = ref({
    verifyCode: {
        rules: [
            {
                required: true,
                errorMessage: "验证码不能为空"
            }
        ]
    }
})

// 修改手机号
const subChangePwd = () => {
    // 表单验证
    changePhoneForm.value
        .validate()
        .then((res) => {
            const params = {
                phone: phone.value || userInfo.value.phone,
                verifyCode: formData.value.verifyCode,
                newPwd: formData.value.newPwd,
                confirmPwd: formData.value.confirmPwd
            }

            // 加密参数
            const encryptionPwd = {
                paramEncipher: RSA.encrypt(JSON.stringify(params))
            }
            loading.value = true
            http.post("/app/user/v3/updateNewPassword", encryptionPwd)
                .then((res) => {
                    formData.value = {}
                    uni.showToast({
                        title: res.message,
                        icon: "none"
                    })
                    setTimeout(() => {
                        uni.clearStorageSync()
                        navigateTo({ url: "/pages/login/index" })
                    }, 1000)
                })
                .finally(() => {
                    loading.value = false
                })
        })
        .catch((err) => {
            console.log("错误信息：", err)
        })
}

const sendVerificationCode = () => {
    if (countdown.value > 0) {
        return
    }

    http.post("/app/sms/external/message", {
        phone: phone.value || userInfo.value.phone
    }).then(({ data, message }) => {
        if (data) {
            // 发送验证码的过程
            uni.showToast({
                title: message,
                icon: "none",
                duration: 2000
            })

            countdown.value = 180
            const timer = setInterval(() => {
                countdown.value--
                if (countdown.value === 0) {
                    clearInterval(timer)
                }
            }, 1000)
        } else {
            uni.showToast({
                title: "验证码获取失败",
                icon: "none",
                duration: 2000
            })
            countdown.value = 0
        }
    })
}

onLoad((options) => {
    if (options && options.username) {
        phone.value = options.username
    }
})
</script>

<style lang="scss" scoped>
.edit_password_page {
    padding: 32rpx;
}
.new_password {
    display: flex;
    padding-bottom: 40rpx;
    flex-direction: column;
    .phone_title {
        font-weight: 400;
        font-size: 36rpx;
        color: #181818;
        padding-bottom: 10rpx;
    }
    .phone_content {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
    }
}
.user_phone {
    font-weight: 600;
    font-size: 32rpx;
    color: #181818;
    padding-bottom: 40rpx;
}

.change_submit {
    width: calc(100% - 64rpx);
    margin: 0rpx 32rpx;
    position: fixed;
    bottom: 32rpx;
    left: 0;
    .change_button {
        background: $uni-color-primary;
    }
    .disabled_class {
        background: #ccc;
    }
}
</style>

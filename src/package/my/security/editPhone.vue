<template>
    <yd-page-view class="editPhonePage" pageBackground="#fff" title="修改手机号" leftIcon="left">
        <view class="pageBox">
            <view class="phoneBox"> 当前绑定手机号：+86 {{ userInfo.phone }} </view>
            <view class="codeBox">
                <view class="areaCode">+86 </view>
                <input :maxlength="11" type="number" class="inputBox" placeholder="请输入手机号" v-model="formData.phone" />
                <div class="verification_code" v-if="codeNum != 180">{{ codeNum }} 秒</div>
                <view @click="handleCode" v-else class="verification_code">获取验证码</view>
            </view>
            <view class="codeBox">
                <input :maxlength="11" type="number" class="inputBox" placeholder="请输入验证码" v-model="formData.verifyCode" />
            </view>
            <view class="btn_class">
                <button type="default" :class="loading ? 'disabled_class' : ''" @click="changePhone" :disabled="loading" :loading="loading" class="button">确认</button>
            </view>
        </view>
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"

const { user } = useStore()
// 用户信息
const codeNum = ref(180) // 验证码倒计时
const loading = ref(false)
let time = null
const userInfo = computed(() => user.userInfo)
const formData = ref({
    phone: ""
})

// 获取验证码
function handleCode() {
    const { phone } = formData.value
    if (phone && phone.length == 11) {
        http.post("/app/sms/external/message", { phone }).then((res) => {
            clearInterval(time)
            time = setInterval(() => {
                if (codeNum.value <= 1) {
                    clearInterval(time)
                    codeNum.value = 180
                } else {
                    codeNum.value--
                }
            }, 1000)
        })
    } else {
        uni.showToast({
            title: "请输入正确的手机号！",
            duration: 1000,
            icon: "none"
        })
    }
}
const changePhone = () => {
    // 发接口
    const params = {
        oldPhone: userInfo.value.phone,
        phone: formData.value.phone,
        verifyCode: formData.value.verifyCode
    }
    loading.value = true
    http.post("/app/user/updatePhone", params)
        .then((res) => {
            formData.value = {}
            uni.showToast({
                title: res.message,
                icon: "none"
            })
            setTimeout(() => {
                uni.clearStorageSync()
                navigateTo({ url: "/pages/login/index" })
            }, 1000)
        })
        .finally(() => {
            loading.value = false
        })
}
</script>

<style lang="scss" scoped>
.editPhonePage {
    background: $uni-bg-color;
}

.pageBox {
    padding: 0 30rpx;
    .phoneBox {
        font-weight: 600;
        font-size: 32rpx;
        color: #181818;
        padding-top: 80rpx;
        padding-bottom: 40rpx;
    }
}

.codeBox {
    display: flex;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #d9d9d9;
    margin-bottom: 60rpx;
    .areaCode {
        width: 80rpx;
        border-right: 2rpx solid #333333;
    }
    .inputBox {
        flex: 1;
        padding-left: 24rpx;
    }
}

.verification_code {
    font-weight: 400;
    font-size: 30rpx;
    color: $uni-color-primary;
}

.btn_class {
    position: fixed;
    height: 100rpx;
    bottom: 40rpx;
    left: 0;
    width: calc(100vw - 88rpx);
    padding: 0rpx 44rpx;

    .button {
        width: 100%;
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }

    .disabled_class {
        background: #ccc;
    }
}
</style>

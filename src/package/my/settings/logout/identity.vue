<template>
    <z-paging>
        <view class="identity">
            <div class="lineBox"></div>
            <view class="identityPage">
                <div class="warnText">确保正在注销帐号的人是帐号拥有者本人，而不被他人恶意注销，需完成手机号验证。</div>

                <view class="codeBox">
                    <view class="fixText">当前手机 </view>
                    <view class="prefix"> {{ start }} </view>
                    <uv-code-input color="#00B781" borderColor="#00B781" :maxlength="4" v-model="formData.value"></uv-code-input>
                    <view class="tail"> {{ end }} </view>
                </view>
                <view class="codeBox">
                    <view class="areaCode">验证码 </view>
                    <input :maxlength="11" type="number" placeholder-class="inputBox" placeholder="请输入验证码" v-model="formData.verifyCode" />
                    <view class="verification_code" style="color: #dbdada" v-if="codeNum !== 60">{{ codeNum }}s</view>
                    <view @click="handleCode" v-else class="verification_code" style="color: #00b781">获取验证码</view>
                </view>
            </view>
        </view>
        <template #bottom>
            <view class="btn_class">
                <button type="default" @click="changePhone" :class="['button', formData.verifyCode ? '' : 'disabled_class']">确认注销</button>
            </view>
        </template>
    </z-paging>
</template>

<script setup>
import useStore from "@/store"
const { user } = useStore()
const start = user.userInfo.phone.slice(0, 3)
const end = user.userInfo.phone.slice(-4)

const formData = reactive({
    value: "",
    verifyCode: ""
})
const codeNum = ref(60) // 验证码倒计时
let time = null

const changePhone = async () => {
    await http.get("/app/user/createUserLogout", { verifyCode: formData.verifyCode })
    showModal({
        title: "",
        showCancel: false,
        content: "提交注册申请成功，15天内不再登录系统，自动注销！",
        success: () => {
            uni.clearStorageSync()
            uni.redirectTo({ url: "/pages/login/index" })
        }
    })
}
// 获取验证码
function handleCode() {
    const { value } = formData
    const phone = `${start}${value}${end}`

    if (phone == user.userInfo.phone) {
        http.get("/app/user/sendSmsCode", { phoneNumber: phone }).then((res) => {
            clearInterval(time)
            time = setInterval(() => {
                if (codeNum.value <= 1) {
                    clearInterval(time)
                    codeNum.value = 60
                } else {
                    codeNum.value--
                }
            }, 1000)
        })
    } else {
        uni.showToast({
            title: "请输入正确的手机号！",
            duration: 1000,
            icon: "none"
        })
    }
}
</script>

<style lang="scss" scoped>
.identity {
    background: $uni-bg-color;
}
.lineBox {
    height: 20rpx;
    background: #f0f2f5;
}

.identityPage {
    padding: 0 32rpx;
}

.warnText {
    font-weight: 400;
    font-size: 26rpx;
    color: #262626;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    padding-top: 40rpx;
    padding-bottom: 30rpx;
}
.codeBox {
    display: flex;
    align-items: center;
    padding-bottom: 20rpx;
    border-bottom: 1rpx solid #d9d9d9;
    margin-bottom: 60rpx;
    .prefix {
        font-weight: 400;
        font-size: 26rpx;
        color: #000000;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        padding-right: 16rpx;
    }
    .fixText {
        font-weight: 400;
        font-size: 26rpx;
        color: #000000;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        padding-right: 50rpx;
    }
    .tail {
        font-weight: 400;
        font-size: 26rpx;
        color: #000000;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        padding-left: 16rpx;
    }
    .areaCode {
        font-weight: 400;
        font-size: 26rpx;
        color: #000000;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        padding-right: 80rpx;
    }
    .verification_code {
        font-size: 26rpx;
    }
    .inputBox {
        flex: 1;
        font-size: 26rpx;
    }
}
.btn_class {
    padding: 30rpx 20rpx 40rpx 20rpx;

    .button {
        width: 100%;
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
        font-size: 28rpx;
        line-height: 90rpx;
    }
    .disabled_class {
        background: #ccc;
        pointer-events: none;
    }

    .disabled_class {
        background: #ccc;
    }
}
</style>

<template>
    <z-paging>
        <view class="logoutPage">
            <view class="lineBox"></view>
            <view class="logouMain">
                <div class="headBox">
                    <div>
                        <img src="@nginx/setting/warningImg.png" alt="" class="logoutImg" />
                    </div>
                    <div class="titleBox">注销账号将永久失效且不可恢复，注销后你账号下的所有数据信息将无法找回，请谨慎操作。</div>
                </div>
            </view>
        </view>
        <template #bottom>
            <div class="btn_class">
                <uni-data-checkbox style="padding-bottom: 20rpx" selectedColor="#00B781" :multiple="true" v-model="isAgree" :localdata="range"></uni-data-checkbox>
                <button type="default" @click="confirmlogoutPage" :class="['button', isAgree == 1 ? '' : 'disabled_class']">确认注销</button>
            </div>
        </template>
    </z-paging>
</template>

<script setup>
const isAgree = ref(0)

const range = [{ value: 1, text: "我已认真阅读并了解,且自愿放弃账号内全部数据、权益与服务。" }]

const confirmlogoutPage = () => {
    showModal({
        content: "确定注销账号吗？",
        success() {
            console.log("确认")
            navigateTo({
                url: "/package/my/settings/logout/identity"
            })
        }
    })
}

onMounted(() => {})
</script>

<style scoped lang="scss">
.logoutPage {
    // margin-top: 20rpx;
    background: $uni-bg-color;
    .logouMain {
        padding: 0 32rpx;
    }
}

.headBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80rpx;
}
.lineBox {
    height: 20rpx;
    background: #f1f2f6;
}

.logoutImg {
    width: 100rpx;
    height: 90rpx;
}

.titleBox {
    font-weight: 400;
    font-size: 28rpx;
    color: #2e2e2e;
    line-height: 44rpx;
    text-align: center;
    font-style: normal;
    padding-top: 40rpx;
}

.btn_class {
    padding: 30rpx 20rpx 40rpx 20rpx;
    :deep(.checklist-text) {
        font-size: 22rpx !important;
    }
    .button {
        width: 100%;
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
        font-size: 28rpx;
        line-height: 90rpx;
    }

    .disabled_class {
        background: #ccc;
        pointer-events: none;
    }
}

.tipText {
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 48rpx;
    text-align: left;
    font-style: normal;
}
</style>

export default function () {
    // 筛选数组子级字段
    function editFieldFn(data, isSubject) {
        // 等价于
        if (!data || data.length <= 0) {
            // 递归的出口
            return null
        }
        return data.map((i) => {
            if (!isSubject) {
                // 循环数据
                const model = {
                    // 把后端返回过来的数据里面的键给替换成我想要的键
                    id: i.id,
                    name: i.name,
                    pid: i.pid,
                    children: i.area
                }

                const children = editFieldFn(i.area) // 子级数据

                if (children) {
                    // 一直往下循环查找有没有children这个键，如果有就直接添加一个子级字段名，这个字段名就是存子级数据
                    model.children = children
                }
                return model // 返回这个数据
            } else {
                return {
                    // 把后端返回过来的数据里面的键给替换成我想要的键
                    value: i.id,
                    label: i.name,
                    ...i
                }
            }
        })
    }

    // 字典接口
    function getSystemDictFn(data) {
        return http.post("/app/SystemDict/get", data).then((res) => {
            return res.data[0]?.list || []
        })
    }

    // 获取籍贯
    function getNativePlace() {
        return http.get("/app/area/list").then((res) => {
            return editFieldFn(res.data)
        })
    }

    // 获取科目
    function getSubjectFn() {
        return http.get("/app/subject/subjectList").then((res) => {
            return res.data.map((i) => {
                return {
                    value: i.id,
                    label: i.name
                }
            })
        })
    }

    // 回显科目
    function echoSubjectFn(idList, list) {
        const arr = list?.filter((item) => {
            return idList?.some((i) => {
                return i === item.value
            })
        })
        return arr
    }

    // 筛选成枚举
    function listForEach(list) {
        return list.reduce((acc, item) => {
            acc[item.value] = item.label
            return acc
        }, {})
    }

    // 处理数据
    async function getData(list) {
        await list.forEach(async (i) => {
            // 字典
            if (i.type == "select") {
                const list = await getSystemDictFn([i.dictionary])
                i.columns = list || []
                i.mappingObj = listForEach(list)
            }
            // 科目
            if (i.type == "subject") {
                const list = await getSubjectFn()
                i.columns = list || []
                i.mappingObj = listForEach(list)
            }
            // 籍贯
            if (i.type == "cascade") {
                const list = await getNativePlace()
                i.columns = list || []
            }
            return i
        })
    }

    const studentInfoLabel = ref([
        {
            lable: "性别",
            code: "gender",
            dictionary: "gender",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "学号",
            code: "studentNo",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "卡号",
            code: "icCardNo",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "入学日期",
            code: "admissionDate",
            placeholder: "请选择",
            type: "date"
        },
        {
            lable: "学历",
            code: "education",
            type: "select",
            multiple: false,
            dictionary: "student_education_status",
            columns: [],
            placeholder: "请选择",
            mappingObj: {}
        },
        {
            lable: "学制",
            code: "academicSystem",
            type: "select",
            multiple: false,
            dictionary: "student_academic_system_status",
            columns: [],
            placeholder: "请选择",
            mappingObj: {}
        },
        {
            lable: "第二学位",
            code: "secondDegree",
            type: "select",
            multiple: false,
            dictionary: "student_second_degree_status",
            columns: [],
            placeholder: "请选择",
            mappingObj: {}
        }
    ])

    const studentIdentityLable = ref([
        {
            lable: "国籍",
            code: "country",
            dictionary: "country",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "籍贯",
            code: "placeBirths",
            type: "cascade",
            placeholder: "请选择",
            multiple: true,
            mappingObj: {},
            columns: []
        },
        {
            lable: "民族",
            code: "nation",
            dictionary: "nation",
            multiple: false,
            placeholder: "请选择",
            mappingObj: {},
            columns: [],
            type: "select"
        },
        {
            lable: "政治面貌",
            code: "politicalStatus",
            dictionary: "political_status",
            multiple: false,
            placeholder: "请选择",
            mappingObj: {},
            columns: [],
            type: "select"
        },
        {
            lable: "户口类别",
            code: "householdType",
            dictionary: "household_type",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "户口所在地",
            code: "householdAddress",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "家庭地址",
            code: "address",
            placeholder: "请输入",
            type: "input"
        }
    ])
    const teacherInfoLabel = ref([
        {
            lable: "性别",
            code: "gender",
            dictionary: "gender",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "工号",
            code: "jobNumber",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "卡号",
            code: "cardNumber",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "参加工作时间",
            code: "workingDate",
            placeholder: "请选择",
            type: "date"
        },
        {
            lable: "来校时间",
            code: "joinDate",
            placeholder: "请选择",
            type: "date"
        },
        {
            lable: "最高学历",
            code: "highestEducation",
            type: "select",
            multiple: false,
            dictionary: "highest_education",
            columns: [],
            placeholder: "请选择",
            mappingObj: {}
        },
        {
            lable: "从教时间",
            code: "teacheringDate",
            placeholder: "请选择",
            type: "yearDate"
        },
        {
            lable: "授教科目",
            code: "subjectName",
            type: "subject",
            placeholder: "请选择",
            multiple: true,
            mappingObj: {},
            columns: []
        }
    ])
    const teacherIdentityLable = ref([
        {
            lable: "籍贯",
            code: "placeBirths",
            type: "cascade",
            placeholder: "请选择",
            multiple: true,
            mappingObj: {},
            columns: []
        },
        {
            lable: "民族",
            code: "nation",
            dictionary: "nation",
            multiple: false,
            placeholder: "请选择",
            mappingObj: {},
            columns: [],
            type: "select"
        },
        {
            lable: "政治面貌",
            code: "politicalStatus",
            dictionary: "political_status",
            multiple: false,
            placeholder: "请选择",
            mappingObj: {},
            columns: [],
            type: "select"
        },
        {
            lable: "港澳台侨外码",
            code: "postalCode",
            dictionary: "postal_code",
            multiple: false,
            mappingObj: {},
            placeholder: "请选择",
            columns: [],
            type: "select"
        },
        {
            lable: "户口类别",
            code: "registeredResidenceType",
            dictionary: "registered_residence_type",
            multiple: false,
            placeholder: "请选择",
            mappingObj: {},
            columns: [],
            type: "select"
        },
        {
            lable: "家庭地址",
            placeholder: "请输入",
            code: "currentAddress",
            type: "input"
        }
    ])
    const parentInfoLabel = ref([
        {
            lable: "是否监护人",
            code: "isEltern",
            dictionary: "isEltern",
            multiple: false,
            mappingObj: {
                1: "是",
                0: "否"
            },
            columns: [
                {
                    label: "是",
                    value: 1
                },
                {
                    label: "否",
                    value: 0
                }
            ],
            placeholder: "请选择",
            type: "dataStatic"
        },
        {
            lable: "是否单亲监护",
            code: "isSingle",
            dictionary: "isSingle",
            multiple: false,
            mappingObj: {
                1: "是",
                0: "否"
            },
            columns: [
                {
                    label: "是",
                    value: 1
                },
                {
                    label: "否",
                    value: 0
                }
            ],
            placeholder: "请选择",
            type: "dataStatic"
        }
    ])

    const childInfoLabel = ref([
        {
            lable: "性别",
            code: "gender",
            dictionary: "gender",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "学号",
            code: "studentNo",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "卡号",
            code: "icCardNo",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "入学日期",
            code: "admissionDate",
            placeholder: "请选择",
            type: "date"
        },
        {
            lable: "住宿方式",
            code: "accommodation",
            dictionary: "accommodation",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "是否独生子女",
            code: "isChild",
            dictionary: "isChild",
            multiple: false,
            mappingObj: {
                1: "是",
                0: "否"
            },
            columns: [
                {
                    label: "是",
                    value: 1
                },
                {
                    label: "否",
                    value: 0
                }
            ],
            placeholder: "请选择",
            type: "dataStatic"
        }
    ])

    const childIdentityLabel = ref([
        {
            lable: "国籍",
            code: "country",
            dictionary: "country",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "籍贯",
            code: "nativePlaces",
            type: "cascade",
            placeholder: "请选择",
            multiple: true,
            mappingObj: {},
            columns: []
        },
        {
            lable: "民族",
            code: "nation",
            dictionary: "nation",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "政治面貌",
            code: "politicalStatus",
            dictionary: "political_status",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "户口类别",
            code: "householdType",
            dictionary: "household_type",
            multiple: false,
            mappingObj: {},
            columns: [],
            placeholder: "请选择",
            type: "select"
        },
        {
            lable: "户口所在地",
            code: "householdAddress",
            placeholder: "请输入",
            type: "input"
        },
        {
            lable: "家庭地址",
            code: "address",
            placeholder: "请输入",
            type: "input"
        }
    ])

    return {
        studentInfoLabel, // 大学生基础信息
        studentIdentityLable, // 大学生身份信息
        teacherInfoLabel, // 老师基础信息
        teacherIdentityLable, // 老师身份信息
        parentInfoLabel, // 家长基础信息
        childInfoLabel, // 家长下的孩子基础信息
        childIdentityLabel, // 家长下的孩子身份信息，
        getData, // 处理数据
        getSystemDictFn, // 获取字典
        getNativePlace, // 获取籍贯
        getSubjectFn, // 获取科目
        echoSubjectFn, // 回显科目
        listForEach // 筛选成枚举
    }
}

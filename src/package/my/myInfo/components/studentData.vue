<template>
    <div>
        <div class="teacher_data_class">
            <uni-section titleColor="#00b781" title="基础信息">
                <uni-list>
                    <uni-list-item v-for="item in studentInfoLabel" :key="item.code" showArrow clickable :title="item.lable" @click="clickItem(item)">
                        <template v-slot:footer>
                            <view class="right_text">
                                <span v-if="item.type == 'select'">
                                    {{ item.mappingObj[dataObj[item.code]] || item.placeholder }}
                                </span>
                                <view v-else-if="item.type == 'yearDate'">
                                    <picker class="year_date" fields="year" mode="date" :value="yearValue" @change="bindDateChange"> {{ dataObj[item.code] ? `${dataObj[item.code]}年` : item.placeholder }} </picker>
                                </view>
                                <span v-else @click="clickItem(item)">
                                    {{ dataObj[item.code] || item.placeholder }}
                                </span>
                            </view>
                        </template>
                    </uni-list-item>
                </uni-list>
            </uni-section>
            <uni-section titleColor="#11c685" title="身份信息">
                <uni-list>
                    <uni-list-item v-for="item in studentIdentityLable" :key="item.code" showArrow clickable :title="item.lable" @click="clickItem(item)">
                        <template v-slot:footer>
                            <div class="right_text">
                                <!-- 级联（籍贯） -->
                                <div v-if="item.type == 'cascade'">
                                    <uni-data-picker v-model="dataObj[item.code]" placeholder="请选择" popup-title="请选择" :localdata="item.columns" v-slot:default="{ data, error }" @change="selectPickerDataFn" :map="{ text: 'name', value: 'id', children: 'children' }" parent-field="area">
                                        <view v-if="error" class="error">
                                            <text>{{ error }}</text>
                                        </view>
                                        <view v-else-if="data.length" class="native_place_list">
                                            <view v-for="(item, index) in data" :key="index">
                                                <span v-if="index != 0">/</span>
                                                {{ item.text }}
                                            </view>
                                        </view>
                                        <view v-else>
                                            {{ dataObj[item.code] && dataObj[item.code].length > 0 ? dataObj[item.code] : item.placeholder }}
                                        </view>
                                    </uni-data-picker>
                                </div>
                                <span v-else-if="item.type == 'select'">
                                    {{ item.mappingObj[dataObj[item.code]] || item.placeholder }}
                                </span>
                                <span v-else>
                                    {{ dataObj[item.code] || item.placeholder }}
                                </span>
                            </div>
                        </template>
                    </uni-list-item>
                </uni-list>
            </uni-section>
        </div>
        <!-- 确认按钮 -->
        <div class="confirm_btn">
            <button class="btn_class" :loading="loading" :disabled="loading" @click="confirmFn">确认</button>
        </div>

        <!-- 选择器 -->
        <yd-select-popup :multiple="selectMultiple" ref="selectPopupRef" :list="dataColumns" title="请选择" @closePopup="closePopup" :selectId="selectId" />

        <!-- 输入框 -->
        <uni-popup ref="inputPopupRef" type="dialog">
            <uni-popup-dialog ref="inputClose" mode="input" :title="`请输入${inputTitle}`" v-model="inputValue" placeholder="请输入内容" @confirm="dialogInputConfirm"></uni-popup-dialog>
        </uni-popup>

        <!-- 日期 -->
        <uv-calendars color="#00b781" confirmColor="#00b781" ref="calendarsRef" mode="single" @confirm="confirmCalendar"></uv-calendars>
    </div>
</template>

<script setup>
// import useStore from "@/store"
// const { user, local } = useStore()
import useHook from "../hook/index"
const { getData, studentInfoLabel, studentIdentityLable } = useHook()

import dayjs from "dayjs"
const yearValue = dayjs().format("YYYY")
const dataObj = ref({})
const loading = ref(false)
const selectCode = ref(null)
const dataColumns = ref([])
const selectPopupRef = ref(null)
const selectMultiple = ref(false)
const inputPopupRef = ref(null)
const calendarsRef = ref(null)
const selectId = ref([])
const inputTitle = ref("")
const inputValue = ref("")

// 修改从教时间
function bindDateChange(e) {
    dataObj.value.teacheringDate = e.detail.value
}

async function clickItem(item) {
    const { type, code, columns, multiple, lable } = item
    selectCode.value = code
    const selectList = ["subject", "select"]
    // 输入框
    if (type == "input") {
        inputValue.value = dataObj.value[code]
        inputTitle.value = lable
        inputPopupRef.value.open()
    } else if (selectList.includes(type)) {
        // 选择框
        dataColumns.value = columns
        selectMultiple.value = multiple
        selectPopupRef.value.open()
        if (type == "subject") {
            selectId.value = dataObj.value.subjectList
        } else {
            selectId.value = multiple ? dataObj.value[code] : [dataObj.value[code]]
        }
        console.log(selectId.value)
    } else if (type == "date") {
        calendarsRef.value.open()
    }
}

// 获取大学生信息
async function getInfoFn() {
    uni.showLoading({
        title: "加载中"
    })
    await http
        .get("/app/invite/getStudentInfo")
        .then(async (res) => {
            dataObj.value = res.data
            console.log(dataObj.value, " dataObj.value")
        })
        .finally(() => {
            uni.hideLoading()
        })
}

// 确认选择
const closePopup = (obj, multiple) => {
    console.log(obj, multiple)
    if (!obj) return
    if (multiple) {
        // 科目
        if (selectCode.value === "subjectName") {
            dataObj.value.subjectName = obj.map((i) => i.label).join(",")
            dataObj.value.subjectList = obj.map((i) => i.value)
        }
    } else {
        // 字典
        dataObj.value[selectCode.value] = obj.value
    }
}

// 确认日期
function confirmCalendar(obj) {
    if (!obj) return
    dataObj.value[selectCode.value] = obj.fulldate
}

// 确认输入
function dialogInputConfirm(value) {
    if (!value) return
    dataObj.value[selectCode.value] = value
}

// 修改大学生信息
function confirmFn() {
    loading.value = true
    http.post("/app/invite/updateStudent", dataObj.value)
        .then((res) => {
            // TODO: 设置完成后是否把性别也存入缓存
            // user.setUserInfo({ ...user.userInfo, gender: dataObj.value.gender })
            uni.showToast({
                title: res.message,
                icon: "none"
            })
            uni.navigateBack()
        })
        .finally(() => {
            loading.value = false
        })
}

// 修改 籍贯
function selectPickerDataFn(item) {
    const list = item.detail.value
    const nowList = []
    list.forEach((i) => {
        nowList.push(i.value)
    })
    console.log(nowList, "nowList")
    dataObj.value.placeBirths = nowList
    dataObj.value.placeBirth = nowList.join(",")
}

onMounted(async () => {
    await getData(studentInfoLabel.value) // 基础信息
    await getData(studentIdentityLable.value) // 身份信息
    await getInfoFn()
})
</script>

<style lang="scss" scoped>
.teacher_data_class {
    padding-bottom: 200rpx;
    .native_place_list {
        display: flex;
        justify-content: flex-end;
    }
    .right_text {
        font-size: 28rpx;
        font-weight: 400;
        color: #999999;
        line-height: 40rpx;
        text-align: right;
        min-width: 50%;
    }
}
.confirm_btn {
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 30rpx;
    background: $uni-bg-color;
    width: calc(100vw - 60rpx);
    .btn_class {
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }
}

:deep(.uni-button-color) {
    color: $uni-color-primary !important;
}
:deep(.uni-list-item__content) {
    min-width: 180rpx;
}
</style>

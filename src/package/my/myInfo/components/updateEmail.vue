<template>
    <view>
        <uni-popup ref="popupRef" @click.stop type="right" :is-mask-click="false" :safe-area="false">
            <div class="popup_box">
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="close" title="邮箱"> </uni-nav-bar>
                <div class="input_box">
                    <uni-easyinput type="textarea" autoHeight :inputBorder="false" primaryColor="#00b781" v-model="emailValue" focus placeholder="请输入邮箱" @input="input"></uni-easyinput>
                </div>
                <div class="btn_box">
                    <button class="btn" @click="submit">保存</button>
                </div>
            </div>
        </uni-popup>
    </view>
</template>

<script setup>
const emit = defineEmits(["submit"])
const popupRef = ref(null)
const emailValue = ref("")

const open = (email) => {
    emailValue.value = email
    popupRef.value.open()
}

const close = () => {
    popupRef.value.close()
}

function input(value) {
    emailValue.value = value
}

function submit() {
    emit("submit", emailValue.value)
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
.popup_box {
    height: 100vh;
    width: 100vw;
    position: relative;
    background: $uni-bg-color;
    .input_box {
        padding: 30rpx;
        :deep(.uni-easyinput__content) {
            background-color: #f6f6f6 !important;
            border-radius: 10rpx;
        }
        :deep(.uni-easyinput__content-textarea) {
            margin: 12rpx !important;
        }
    }
    .btn_box {
        position: absolute;
        bottom: 0rpx;
        left: 0rpx;
        height: 100rpx;
        padding: 30rpx 30rpx 40rpx 30rpx;
        width: calc(100% - 60rpx);
        .btn {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
        }
    }
}
</style>

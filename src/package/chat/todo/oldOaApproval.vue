<template>
    <div>
        <!-- #ifdef H5-WEIXIN || H5 -->
        <iframe class="webview" :src="src" frameborder="0"></iframe>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN || APP-PLUS-->
        <web-view class="webview" :src="src"></web-view>
        <!-- #endif -->
    </div>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"
import { getToken } from "@/utils/storageToken.js"

const src = ref("")
const token = getToken()?.replace("Bearer ", "")
onLoad((options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    const { callId, status, identity, taskType } = options
    src.value = `${import.meta.env.VITE_BASE_WEBAPP}/#//toDo/oldTodoInfo?id=${callId}&myStatus=${status}&token=${token}&identity=${identity}&taskType=${taskType}`
})
</script>

<style lang="scss" scoped>
.webview {
    height: 100vh;
    width: 100vw;
}
</style>

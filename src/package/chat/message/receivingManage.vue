<template>
    <div class="receiving_page">
        <z-paging ref="paging" use-virtual-list @query="queryList" v-model="receivingList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="消息接收管理"> </uni-nav-bar>
            </template>
            <div class="receiving_list">
                <div class="receiving_item" v-for="item in receivingList" :key="item.moduleId">
                    <div class="title">{{ item.moduleName }}</div>
                    <div class="right_btn unsubscribe" v-if="item.msgControl == 0" @click="rightBtn(item)">退订</div>
                    <div v-else-if="item.msgControl == 1" class="right_btn subscribe" @click="rightBtn(item)">订阅</div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <!-- 确认删除框 -->
        <yd-popup ref="confirmRef" :titleflag="false" @confirm="dialogConfirm">
            <view style="padding: 33px 0px 10px 0px">退订后将不再接受此类型的消息 </view>
        </yd-popup>
    </div>
</template>

<script setup>
const confirmRef = ref(null)
const receivingList = ref([])
const paging = ref(null)
const moduleItem = ref({})
function queryList() {
    http.get("/app/v2/msg/control/queryUserMessageControlList")
        .then((res) => {
            paging.value.setLocalPaging(res.data)
        })
        .catch(() => {
            paging.value.setLocalPaging([])
        })
}

function updateUserMessageControl() {
    http.post("/app/v2/msg/control/updateUserMessageControl", {
        moduleId: moduleItem.value.moduleId,
        msgControl: moduleItem.value.msgControl == 0 ? 1 : 0
    }).then((res) => {
        uni.showToast({
            title: res.message || "退订成功",
            icon: "none"
        })
        queryList()
    })
}

function rightBtn(item) {
    moduleItem.value = item
    if (item.msgControl == 0) {
        // 退订二次确认
        confirmRef.value.open()
    } else {
        // 订阅
        updateUserMessageControl()
    }
}

function clickLeft() {
    uni.navigateBack()
}

// 确认退订
function dialogConfirm() {
    updateUserMessageControl()
}
</script>

<style lang="scss" scoped>
.receiving_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .receiving_list {
        padding: 0rpx 30rpx;
        background: $uni-bg-color;
        .receiving_item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30rpx 0rpx;
            border-bottom: 1rpx solid $uni-border-color;
            .title {
                font-weight: 500;
                font-size: 28rpx;
                color: $uni-text-color;
                line-height: 40rpx;
            }
            .right_btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 112rpx;
                height: 60rpx;
                border-radius: 6rpx;
                font-weight: 400;
                font-size: 26rpx;
                line-height: 36rpx;
            }
            .unsubscribe {
                background: $uni-bg-color;
                border: 1rpx solid #d8d8d8;
                color: #666666;
            }
            .subscribe {
                background: $uni-color-primary;
                color: $uni-text-color-inverse;
            }
        }
    }
}
</style>

<template>
    <div class="message_page">
        <z-paging ref="paging" @query="queryList" v-model="messageList">
            <template #top>
                <uni-nav-bar statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="消息通知">
                    <template #right>
                        <image @click="receivingManage" class="message_set_icon" src="@nginx/chat/message_set.png" alt="" />
                    </template>
                </uni-nav-bar>
            </template>
            <div class="message_list">
                <div class="message_item" v-for="item in messageList" :key="item.id">
                    <div class="time" v-if="item.sendTime">{{ item.sendTime }}</div>
                    <div class="box">
                        <div class="title_box">
                            <image class="massge_icon" src="@nginx/chat/message_logo.png" alt="" />
                            <text class="title">{{ moduleTitle[item.moduleType] || "消息通知" }}</text>
                        </div>
                        <div class="content_box">
                            <text v-if="item.title" class="content_title">{{ item.title }}</text>
                            <text class="content"> {{ item.content }} </text>
                        </div>
                        <!-- TODO: 查看详情跳转子应用相关的页面 -->
                        <!-- <div class="look_details">
                            <text>查看详情</text>
                            <uni-icons type="right" size="18" color="#00b781"></uni-icons>
                        </div> -->
                    </div>
                </div>
            </div>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
    </div>
</template>

<script setup>
const paging = ref(null)
const messageList = ref([])

const moduleTitle = {
    1: "闸机推送",
    2: "考勤",
    3: "考勤周报",
    4: "请假",
    5: "通知公告",
    6: "失物招领",
    7: "作业",
    8: "人脸采集",
    9: "日程提醒",
    10: "会议提醒",
    11: "课后作业布置提醒",
    12: "场地预约通知",
    13: "收集表",
    14: "OA审批",
    15: "个人信息录入通知",
    16: "系统通知",
    17: "考试成绩通知",
    18: "活动报名状态变更通知",
    19: "宿舍考勤通知",
    20: "场地预约通知"
}

// 调用List数据
function queryList(pageNo, pageSize) {
    http.post("/app/v2/push/pageUserMessage", { pageNo, pageSize }).then(({ data }) => {
        paging.value.complete(data.list)
    })
}

function clickLeft() {
    uni.navigateBack()
}

function receivingManage() {
    navigateTo({
        url: "/package/chat/message/receivingManage"
    })
}
</script>

<style lang="scss" scoped>
.message_page {
    background: $uni-bg-color-grey;
    min-height: 100vh;
    .message_set_icon {
        width: 44rpx;
        height: 44rpx;
    }
    .message_list {
        padding: 30rpx;
        .message_item {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            .time {
                font-weight: 400;
                margin-bottom: 10rpx;
                font-size: 24rpx;
                color: #b3b3b3;
                line-height: 34rpx;
                text-align: center;
            }
            .box {
                padding: 40rpx 30rpx;
                width: calc(100% - 80rpx);
                min-height: 100rpx;
                background: $uni-bg-color;
                border-radius: 20rpx;
                margin-bottom: 30rpx;

                .title_box {
                    display: flex;
                    align-items: center;
                    padding-bottom: 30rpx;
                    margin-bottom: 30rpx;
                    border-bottom: 1rpx solid $uni-border-color;
                    .massge_icon {
                        width: 44rpx;
                        height: 44rpx;
                        margin-right: 10rpx;
                    }
                    .title {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                    }
                }
                .content_box {
                    display: flex;
                    flex-direction: column;
                    .content_title {
                        font-weight: 600;
                        font-size: 30rpx;
                        color: $uni-text-color;
                        line-height: 42rpx;
                        margin-bottom: 30rpx;
                    }
                    .content {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: $uni-text-color;
                        line-height: 36rpx;
                    }
                }
                .look_details {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    margin-top: 30rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: $uni-color-primary;
                    line-height: 40rpx;
                    text-align: right;
                }
            }
        }
    }
}
</style>

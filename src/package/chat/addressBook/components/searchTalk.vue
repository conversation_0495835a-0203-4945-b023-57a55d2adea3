<template>
    <view>
        <view v-if="isNoData" class="search_talk">
            <view class="title">你可能想找：</view>
            <!-- 老师 -->
            <view class="teacher" v-if="isElternList">
                <view class="name">
                    <span class="text">老师</span>
                    <uni-icons type="down" color="#00b781" size="16"></uni-icons>
                </view>
                <uni-list :border="false" v-for="item in state.data.elternList" :key="item.id" @click="personnelInfo(item, 'dept')">
                    <view class="class_student">
                        <image mode="aspectFill" class="image" :src="item.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                        <uni-list-item :title="item.name" />
                    </view>
                </uni-list>
            </view>
            <!-- 学生 -->
            <view class="student" v-if="isStudentList">
                <view class="name">
                    <span class="text">学生</span>
                    <uni-icons type="down" color="#00b781" size="16"></uni-icons>
                </view>
                <uni-list :border="false" v-for="item in state.data.studentList" :key="item.id" @click="personnelInfo(item, 'class')">
                    <view class="class_student">
                        <image mode="aspectFill" class="image" :src="item.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                        <uni-list-item :border="false" v-if="item.elternAddBookDTOList">
                            <template #body>
                                <view class="body_box">
                                    {{ item.name }}
                                    <view class="label" v-for="cItem in item.elternAddBookDTOList" :key="cItem.id">
                                        <span> {{ cItem.name }}</span>
                                        <view class="tip" v-if="cItem.relations">
                                            {{ relationsText[cItem.relations] }}
                                        </view>
                                    </view>
                                </view>
                            </template>
                        </uni-list-item>
                        <uni-list-item :border="false" :title="item.name" v-else />
                    </view>
                </uni-list>
            </view>
        </view>
        <!-- 无数据 -->
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>

<script setup>
const emit = defineEmits(["personnel"])
const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})

const relationsText = {
    0: "",
    1: "父亲",
    2: "母亲",
    3: "爷爷",
    4: "奶奶",
    5: "外公",
    6: "外婆",
    7: "其它"
}

const state = reactive({
    data: {},
    bread: [],
    type: null // dept老师，class学生
})

const isNoData = computed(() => {
    return (state.data.elternList && state.data.elternList.length > 0) || (state.data.studentList && state.data.studentList.length > 0)
})

const isElternList = computed(() => {
    return state.data.elternList && state.data.elternList.length > 0
})
const isStudentList = computed(() => {
    return state.data.studentList && state.data.studentList.length > 0
})

// 人员信息
function personnelInfo(item, type) {
    emit("personnel", { ...item, type })
}

watch(
    () => props.data,
    (val) => {
        state.data = val
    }
)
</script>

<style lang="scss" scoped>
.search_talk {
    margin: 20rpx 0;
}

.title {
    font-size: 30rpx;
    color: #ccc;
    margin: 0 0 20rpx 20rpx;
}
.student,
.teacher {
    background: $uni-bg-color;
    .name {
        font-size: 32rpx;
        font-weight: 600;
        padding: 30rpx 30rpx 0rpx 20rpx;
        .text {
            padding-right: 10rpx;
        }
    }
}
.class_student {
    display: flex;
    .image {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
        margin: 20rpx 0rpx 20rpx 20rpx;
    }
    .body_box {
        width: 100%;
        font-size: 28rpx;
        color: #3b4144;
    }
    :deep(.uni-list-item) {
        flex: 1;
    }
    .label {
        margin-top: 10rpx;
        border-top: 1rpx solid #f3f4f5;
        padding-top: 16rpx;
        display: flex;
        align-items: center;
        .tip {
            min-width: 20rpx;
            background: #f3fcf9;
            color: $uni-color-primary;
            padding: 6rpx;
            margin-left: 40rpx;
        }
    }
}
</style>

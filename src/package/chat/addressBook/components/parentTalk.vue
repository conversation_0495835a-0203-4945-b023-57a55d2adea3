<template>
    <view>
        <view class="address_head">
            <view class="address_school">
                <image mode="aspectFill" class="img" :src="state.data.schoolBadgeUrl || '@nginx/chat/uploadImg.png'" alt="" />
                <text>{{ state.data.schoolName }}</text>
            </view>
            <view class="bread" v-if="state.bread && state.bread.length > 0">
                <view v-for="(item, key) in state.bread" :key="key" @click="breadClick(item, key)" class="bread_item">
                    <text class="item_text">{{ item.name }}</text>
                    <uni-icons type="right" :color="key == state.bread.length - 1 ? '#999999' : '#00b781'" size="16"></uni-icons>
                </view>
            </view>
        </view>
        <view v-if="state.data.adlist && state.data.adlist.length > 0">
            <!-- 班级 -->
            <uni-list :border="false" v-for="item in state.data.adlist" :key="item.id">
                <uni-list-item @click="handleClass(item)" :showArrow="!item.employeeSubjects" clickable>
                    <template #body>
                        <text class="adlist_text" v-if="item.children || item.arr">{{ item.name }}</text>
                        <view class="class_student" v-else>
                            <image mode="aspectFill" class="image" :src="item.avatar || '@nginx/chat/identity_teacher.png'" alt="" />
                            <view class="adlist_text ellipsis">
                                {{ item.name }}
                                <text v-if="item.employeeSubjects">
                                    {{ "（" + item.employeeSubjects + "）" }}
                                </text>
                            </view>
                        </view>
                    </template>
                </uni-list-item>
            </uni-list>
        </view>
        <!-- 无数据 -->
        <yd-empty text="暂无数据" :isMargin="true" v-else />
    </view>
</template>

<script setup>
const emit = defineEmits(["personnel"])
const props = defineProps({
    data: {
        type: Object,
        default: () => {}
    }
})
const state = reactive({
    data: {},
    bread: [],
    type: null // dept老师，class学生
})

watch(
    () => props.data,
    (val) => {
        state.data = val
        state.data.adlist = val.adlist.map((i) => {
            return {
                children: [
                    {
                        name: "老师",
                        id: 1,
                        code: "dept",
                        arr: i.elternList
                    },
                    {
                        name: "学生",
                        id: 2,
                        code: "class",
                        arr: i.studentList
                    }
                ],
                ...i
            }
        })
        state.bread.push({ name: val.schoolName, id: null, ...val })
    }
)

function breadClick(item, index) {
    if (index == 0) {
        state.data.adlist = item.adlist
        state.bread = [item]
    } else {
        state.data.adlist = item.children || item.arr
        state.bread = state.bread.slice(0, index + 1)
    }
}

function handleClass(item) {
    if (item.arr) {
        state.type = item.code
    }
    if (item.children || item.arr) {
        state.data.adlist = item.children || item.arr

        state.bread = [...state.bread, item]
    } else {
        personnelInfo(item)
    }
}

// 人员信息
function personnelInfo(eItem) {
    emit("personnel", { ...eItem, type: state.type })
}
</script>

<style lang="scss" scoped>
:deep(.uni-list-item) {
    border-bottom: 1rpx solid #ebedf0;
}
.address_head {
    min-height: 88rpx;
    margin: 20rpx 0rpx;
    display: flex;
    flex-direction: column;
}
.address_school {
    min-height: 88rpx;
    background: $uni-bg-color;
    padding: 0rpx 30rpx;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
    line-height: 42rpx;
    .img {
        width: 64rpx;
        height: 64rpx;
        padding-right: 20rpx;
    }
}
.bread::-webkit-scrollbar {
    display: none;
}
.bread {
    padding: 0rpx 40rpx;
    background: $uni-bg-color;
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    max-width: 100%;
    overflow: auto;
    padding-bottom: 20rpx;
    .bread_item {
        .item_text {
            white-space: nowrap;
            font-weight: 500;
            font-size: 28rpx;
            color: $uni-color-primary;
        }
    }
    :last-child {
        .item_text {
            color: #999999;
        }
    }
}

.adlist_text {
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 40rpx;
}
.class_student {
    display: flex;
    align-items: center;
    max-width: 72%;
    .image {
        width: 50rpx;
        height: 50rpx;
        border-radius: 50%;
    }
    .adlist_text {
        max-width: calc(100% - 50rpx);
        padding-left: 10rpx;
    }
}
</style>

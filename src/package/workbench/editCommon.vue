<template>
    <z-paging>
        <template #top>
            <uni-nav-bar left-icon="left" :border="false" backgroundColor="transparent" fixed statusBar title="管理应用" @clickLeft="back" />
        </template>
        <view class="workbench">
            <view class="app_content">
                <div class="app_category" v-for="(item, index) in tabs" :key="index">
                    <div v-if="existingApp(item.apps)">
                        <span class="category_title">{{ item.categoryName }}</span>
                        <div class="app_list">
                            <template v-if="index == 0">
                                <!-- 常用应用 -->
                                <div class="app_item" v-for="app in state.commonApps" :key="app.id">
                                    <image class="app_logo" :src="app.logo"></image>
                                    <span class="app_name">{{ app.name }}</span>
                                    <view class="close" @click="handleDele(app)"><uni-icons type="clear" size="24"></uni-icons></view>
                                </div>
                            </template>
                            <template v-else>
                                <div class="app_item" v-show="appShow.includes(app.routePath)" v-for="app in item.apps" :key="app.id">
                                    <image class="app_logo" :src="app.logo"></image>
                                    <span class="app_name">{{ app.name }}</span>
                                    <button :class="['item_btn', ids.includes(app.id) ? 'disabled' : '']" @click="handleAdd(app)">添加</button>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </view>
        </view>
        <template #bottom>
            <view class="footer">
                <button class="btn" @click="handleSubmit">保存</button>
            </view>
        </template>
        <yd-popup ref="confirmRef" :titleflag="false" @close="close" @confirm="handleSubmit">
            <view class="pop_container">编辑的内容还未保存,是否要保存？</view>
        </yd-popup>
    </z-paging>
</template>
<script setup>
import { existingApp, appShow } from "@/utils/existingApp"
const instance = getCurrentInstance().proxy
const eventChannel = instance.getOpenerEventChannel()

const confirmRef = ref(null)

const tabs = ref([])
const state = reactive({
    commonApps: [],
    isChange: false
})

const ids = computed(() => state.commonApps.map((i) => i.id))

const handleDele = (item) => {
    const index = state.commonApps.findIndex((i) => i.id == item.id)
    state.commonApps.splice(index, 1)
    state.isChange = true
}

const handleAdd = (item) => {
    state.commonApps.push(item)
    state.isChange = true
}

const handleSubmit = async () => {
    await http.post("/app/appCenter/updateApp", { appIds: ids.value })
    uni.navigateBack({
        success: () => {
            eventChannel.emit("refresh", {
                data: true
            })
        }
    })
}

const back = () => {
    if (state.isChange) {
        confirmRef.value.open()
    } else {
        uni.navigateBack({
            success: () => {
                eventChannel.emit("refresh", {
                    data: true
                })
            }
        })
    }
}

const close = () => {
    uni.navigateBack({
        success: () => {
            eventChannel.emit("refresh", {
                data: true
            })
        }
    })
}

onShow(() => {
    nextTick(async () => {
        await http.get("/app/appCenter/list").then((res) => {
            const list = res.data.map((i) => {
                return {
                    ...i,
                    name: i.categoryName
                }
            })
            tabs.value = list.filter((i) => existingApp(i.apps))
            state.commonApps = res.data.find((i) => i.categoryName == "常用应用").apps
        })
    })
})
</script>
<style lang="scss" scoped>
.workbench {
    .app_content {
        padding: 10rpx 30rpx;
        .app_category {
            margin-top: 30rpx;

            .category_title {
                font-weight: 500;
                font-size: 30rpx;
                color: $uni-text-color;
                line-height: 42rpx;
            }

            .app_list {
                margin-top: 20rpx;
                min-height: 100rpx;
                background: $uni-bg-color;
                box-shadow: 0rpx 16rpx 16rpx 0rpx #dcf5ee80;
                border-radius: 20rpx;
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 20rpx;
                padding: 20rpx;

                .app_item {
                    padding: 20rpx 0rpx;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    position: relative;
                    .close {
                        position: absolute;
                        top: -4rpx;
                        right: 34rpx;
                    }

                    .app_logo {
                        width: 80rpx;
                        height: 80rpx;
                    }

                    .app_name {
                        padding-top: 20rpx;
                        font-weight: 400;
                        font-size: 28rpx;
                        color: $uni-text-color;
                        line-height: 40rpx;
                    }
                    .item_btn {
                        margin-top: 20rpx;
                        width: 92rpx;
                        height: 52rpx;
                        line-height: 52rpx;
                        font-size: 26rpx;
                        color: $uni-text-color-inverse;
                        border-radius: 6rpx;
                        padding: 0;
                        background: $uni-color-primary;
                    }
                    .disabled {
                        background: #d8d8d8;
                        pointer-events: none;
                    }
                }
            }
        }
    }
}
.footer {
    padding: 40rpx 30rpx 60rpx 30rpx;
    background: $uni-bg-color;
    .btn {
        background: $uni-color-primary;
        color: $uni-text-color-inverse;
    }
}
.pop_container {
    padding-top: 60rpx;
    padding-bottom: 20rpx;
}
</style>

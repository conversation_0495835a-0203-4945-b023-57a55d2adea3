<template>
    <div class="cloud_login">
        <div class="login_logo">
            <image class="logo" src="@nginx/login/logo.png" alt="" />
            <span class="title">欢迎登录一加壹</span>
        </div>
        <div class="login_form">
            <div class="form">
                <input type="text" class="input" placeholder="请输入手机号" v-model="userInfo.username" />
            </div>
            <div class="form">
                <input :maxlength="11" class="input" v-model="userInfo.password" placeholder="请输入密码" v-if="isShow" type="text" />
                <input class="input" v-else v-model="userInfo.password" placeholder="请输入密码" type="password" />
                <div @click="isShow = !isShow" class="eye">
                    <image src="@nginx/login/openPassword.png" class="Fill" v-if="!isShow" />
                    <image src="@nginx/login/closePassword.png" class="Fill" v-else />
                </div>
            </div>
            <div class="forget_password">
                <span @click="forgetPassword">忘记密码</span>
            </div>
        </div>
        <div class="btn_class">
            <button class="button" :loading="loading" @click="logIn">登录</button>
            <div class="privacy_policy">
                <checkbox-group @change="checkboxChange">
                    <checkbox :value="checkVal" :checked="checked" backgroundColor="#fff" borderColor="#eee" activeBackgroundColor="#04B578" activeBorderColor="#04B578" iconColor="#fff" style="transform: scale(0.6)" />
                    <text class="text">我已认真阅读，理解并同意<text class="text_link" @click="userAgreement">《一加壹教育用户协议》</text><text class="text_link" @click="privacyPolicy">《一加壹隐私权政策》</text></text>
                </checkbox-group>
            </div>
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits(["logIn", "forgetPassword"])
const props = defineProps({
    // 按钮loading
    loading: {
        type: Boolean,
        default: false
    }
})
const checked = ref(false) // 是否勾选相关协议
const checkVal = ref("0")
const isShow = ref(false) // 密码是否可见
const userInfo = ref({
    username: "", // 13822974025
    password: "" // 12345678t
})

// 登录按钮loading
const loading = computed(() => {
    return props.loading
})

// 是否勾选相关协议
function checkboxChange(e) {
    if (e.detail.value == "0") {
        checked.value = true
    } else {
        checked.value = false
    }
}

function privacyPolicy() {
    navigateTo({
        url: "/package/my/privacyPolicy/privacyPolicy"
    })
}

function userAgreement() {
    navigateTo({
        url: "/package/my/privacyPolicy/userAgreement"
    })
}

// 点击登录按钮
const logIn = () => {
    if (checked.value) {
        emit("logIn", userInfo.value)
    } else {
        uni.showToast({
            title: "请阅读相关协议并勾选！",
            icon: "none",
            duration: 2000
        })
    }
}

const forgetPassword = () => {
    emit("forgetPassword")
}
</script>

<style lang="scss" scoped>
.cloud_login {
    height: calc(100vh - 200rpx);
    padding: 0rpx 44rpx;
    padding-top: 200rpx;

    .login_logo {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .logo {
            width: 132rpx;
            height: 132rpx;
        }

        .title {
            padding-top: 24rpx;
            font-weight: 600;
            font-size: 40rpx;
            color: #000000;
            line-height: 56rpx;
        }
    }

    .login_form {
        margin-top: 40rpx;
        background: $uni-bg-color;
        width: 100%;
        box-sizing: border-box;
        position: relative;

        .form {
            margin-top: 58rpx;
            position: relative;

            .eye {
                position: absolute;
                right: 0;
                bottom: -10rpx;
                z-index: 9;
                width: 100rpx;
                height: 100rpx;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .Fill {
                width: 40rpx;
                height: 40rpx;
            }
        }

        .input {
            border-bottom: 1rpx solid $uni-border-color;
            height: 80rpx;
            outline: none;
            color: $uni-text-color;
            line-height: 80rpx;
        }

        .forget_password {
            padding-top: 24rpx;
            display: flex;
            justify-content: flex-end;
            font-weight: 400;
            font-size: 24rpx;
            color: $uni-text-color-grey;
            line-height: 48rpx;
        }
    }

    .btn_class {
        margin-top: 100rpx;

        .button {
            background: $uni-color-primary;
            color: $uni-text-color-inverse;
            height: 92rpx;
            border-radius: 12rpx;
            border: none;
        }

        .privacy_policy {
            margin-top: 32rpx;

            .text {
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color-grey;
                line-height: 48rpx;

                .text_link {
                    color: $uni-color-primary;
                }
            }
        }
    }
}
</style>

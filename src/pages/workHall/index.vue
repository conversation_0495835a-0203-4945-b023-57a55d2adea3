<template>
	<view class="yd_work_hall">
		<view class="yd_work_hall_bg">
			<text class="user_name" @click="logoutFn">{{ user?.userInfo?.name || '未登录' }}</text>
			<text class="page_title">新乡市职业教育中心网上办事大厅</text>
			<div class="page_search">
				<uni-easyinput class="search_input" prefixIcon="search" v-model="keywords" placeholder="输入您想要搜索的关键词"
					@change="hadlerEasyinput" @clear="hadlerEasyinput"></uni-easyinput>
			</div>
		</view>
		<div class="tabs_box">
			<uv-tabs lineWidth="20" lineColor="#53ABFF" :current="tabsCurrent" :scrollable="false"
				:activeStyle="{ color: '#53ABFF' }" :inactiveStyle="{ color: '#262626' }"
				:customStyle="{ background: '#fff' }" :list="tabsList" @click="tabsClick"></uv-tabs>
		</div>
		<div class="work_hall_content">
			<div class="work_hall_list">
				<div class="work_hall_item" @click="openInfo(item)" :style="{
					backgroundColor: item.bgColor
				}" v-for="(item, index) in workHallList" :key="index">
					<image class="image_class" :src="item.iconImg" mode="scaleToFill" />
					<text>{{ item.title }}</text>
				</div>
			</div>
		</div>
		<uni-popup ref="contentPopup" type="bottom" :is-mask-click="false" :safe-area="false">
			<div class="content_popup">
				<div class="title">
					<span class="text">办事详情</span>
					<uni-icons class="closeempty_icons" type="closeempty" size="22" color="#333"
						@click="closeFn"></uni-icons>
				</div>
				<div class="content_box">
					<div class="title_box">
						<image class="image_class" :src="openItem.iconImg" mode="scaleToFill" />
						<div class="title_text">
							<text>{{ openItem.title }}</text>
							<text class="type_text">服务类型：{{ tabsKey == 'student' ? '学生办事' : '教师办事' }}</text>
						</div>
					</div>
					<text class="content_text">{{ openItem.content }}</text>
				</div>
				<view class="button_box" @click="goPerformTasks(openItem)">
					办事
				</view>
			</div>
		</uni-popup>
	</view>
</template>

<script setup>
import { studentList, teacherList } from './data'
import { getToken } from "@/utils/storageToken.js"
import useStore from "@/store"
const { user, home, system, local } = useStore()

const keywords = ref('')
const identityType = computed(() => {
	const roleCode = user?.identityInfo?.roleCode
	if (roleCode == "eltern") {
		return "eltern"
	} else if (roleCode == "student") {
		return "student"
	} else {
		return "teacher"
	}
})
const tabsCurrent = ref(0)
const contentPopup = ref(null)
const openItem = ref({})
const tabsKey = ref('student')
const tabsList = [
	{ name: "学生办事", id: 'student' },
	{ name: "教师办事", id: 'teacher' }
]
const workHallList = ref([])

const setTabs = async (item) => {
	tabsKey.value = item.id
	tabsCurrent.value = item.index
}

async function tabsClick(item) {
	await setTabs(item)
	getList()
}
function getList() {
	const listKey = {
		teacher: teacherList,
		student: studentList
	}
	workHallList.value = listKey[tabsKey.value] || []
}

function openInfo(item) {
	openItem.value = item
	contentPopup.value.open()
}

const closeFn = () => {
	contentPopup.value.close()
}
const hadlerEasyinput = () => {
	if (!keywords.value) {
		getList()
		return
	}
	const newList = workHallList.value.filter((i) => i.title.includes(keywords.value))
	workHallList.value = newList
}


const evalId = ref(null)
async function goPerformTasks(item) {
	const { user } = useStore()
	const token = getToken()
	// 如果没有登录直接去登录
	if (!user.identityInfo && !token) {
		setTimeout(() => {
			uni.clearStorageSync()
			system.setWorkHall(true)
			uni.reLaunch({ url: "/pages/login/index" })
		}, 1000)
	} else {
		// 有登录
		system.setWorkHall(false)
		// 如果是老师角色 不能看学生办事，如果是学生家长角色不能看老师办事
		if ((identityType.value == 'teacher' && tabsKey.value == 'teacher') || (identityType.value != 'teacher' && tabsKey.value == 'student')) {
			// 如果是评价需要调用接口去获取到评价的id
			if (item.path == 'evalActivity') {
				await http.get('/cloud/evalType/listBySchool').then((res) => {
					const evalObj = res.data.find((i) => i.name == '教师评价')
					evalId.value = evalObj.id
				})
			}
			// 老师修改个人信息路由
			const pathObj = {
				perfectMore: "/package/my/myInfo/perfectMore"
			}
			// 审批传参和评价传参（评价用的前面调用的接口获取到的ID）
			const codeObj = {
				oaApprove: `${item.fromKey}${user.schoolInfo.id}`,
				evalActivity: evalId.value,
			}
			navigateTo({
				url: pathObj[item.path] || `/apps/${item.path}/index`,
				query: {
					code: codeObj[item.path] || item.code,
					routePath: item.path,
				}
			})
		} else {
			uni.showToast({
				title: '当前仅支持学生或老师办事，请检查账号身份后使用！',
				icon: 'none'
			})
		}
	}
}

function logoutFn() {
	const { user } = useStore()
	const token = getToken()
	if (!user.identityInfo && !token) {
		system.setWorkHall(true)
		uni.redirectTo({ url: "/pages/login/index" })
	} else {
		uni.showModal({
			title: "提示",
			content: "确定要退出登录吗？",
			confirmColor: "#53ABFF",
			success(res) {
				if (res.confirm) {
					clearStore()
					uni.clearStorageSync()
					system.setWorkHall(true)
					uni.redirectTo({ url: "/pages/login/index" })
				}
			}
		})
	}
}


onMounted(() => {
	getList()
})

</script>

<style lang="scss" scoped>
.yd_work_hall {
	height: 100vh;
	background: #F9FAF9;

	.yd_work_hall_bg {
		width: 100vw;
		background: url('@nginx/workHall/workHallBg.png') no-repeat;
		background-size: cover;
		height: 312rpx;
		position: relative;

		.page_title {
			position: absolute;
			top: 100rpx;
			font-weight: 500;
			font-size: 34rpx;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: center;
			width: 100%;
			display: block;
		}

		.user_name {
			position: absolute;
			top: 20rpx;
			padding-right: 30rpx;
			font-size: 24rpx;
			color: #FFFFFF;
			line-height: 48rpx;
			text-align: right;
			width: calc(100% - 30rpx);
			display: block;
		}

		.page_search {
			position: absolute;
			bottom: 32rpx;
			width: calc(100% - 60rpx);
			display: flex;
			justify-content: center;
			padding: 0 30rpx;

			.search_input {
				:deep(.is-input-border) {
					border-radius: 36rpx;
					border: none;
				}
			}

		}
	}

	.tabs_box {
		margin-top: 20rpx;
	}

	.work_hall_content {}

	.work_hall_list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 34rpx;
		padding: 30rpx;
		background: #FFFFFF;

		.work_hall_item {
			height: 116rpx;
			display: flex;
			padding: 0rpx 32rpx;
			align-items: center;
			font-weight: 400;
			font-size: 28rpx;
			color: #262626;
			line-height: 40rpx;

			.image_class {
				min-width: 60rpx;
				width: 60rpx;
				height: 60rpx;
				margin-right: 16rpx;
			}
		}
	}

	.content_popup {
		min-height: 60vh;
		background: $uni-bg-color;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		padding-bottom: 120rpx;

		.title {
			height: 100rpx;
			display: flex;
			align-items: center;

			.text {
				font-size: 34rpx;
				font-weight: 600;
				color: $uni-text-color;
				line-height: 48rpx;
				width: 100vh;
				text-align: center;
				margin-left: 62rpx;
			}

			.closeempty_icons {
				margin-right: 18rpx;
			}

		}
	}

	.content_box {
		padding: 30rpx;
		position: relative;

		.title_box {
			display: flex;
			align-items: center;
			height: 96rpx;
			margin-bottom: 52rpx;

			.image_class {
				min-width: 96rpx;
				height: 96rpx;
				width: 96rpx;
				margin-right: 24rpx;
			}

			.title_text {
				display: flex;
				height: 96rpx;
				flex-direction: column;
				font-weight: 500;
				font-size: 30rpx;
				color: #262626;
				line-height: 42rpx;
				justify-content: space-between;
				height: 100%;

				.type_text {
					font-weight: 400;
					font-size: 26rpx;
					color: #666666;
					line-height: 36rpx;
				}
			}

		}

		.content_text {
			font-weight: 400;
			font-size: 28rpx;
			color: rgba(0, 0, 0, 0.65);
			line-height: 40rpx;
		}


	}

	.button_box {
		position: absolute;
		bottom: 30rpx;
		left: 30rpx;
		font-weight: 400;
		font-size: 32rpx;
		color: #FFFFFF;
		line-height: 44rpx;
		width: calc(100vw - 60rpx);
		height: 92rpx;
		background: #55ABFF;
		border-radius: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

}
</style>
export const studentList = [
    {
        bgColor: "#F4F9FD",
        id: 1,
        title: "学生异动申请",
        fromKey: "studentChange_",
        iconImg: "@nginx/workHall/student/anomalyIcon.png",
        content: "学生因特殊情况需对学籍状态进行调整，如休学、复学、退学、转学、转专业等，可通过该功能提交申请。申请休学的学生，需明确休学原因，像因病休学需附上县级以上医院开具的证明；创业休学则要提供创业计划书等相关材料。复学申请需说明复学依据，如退役复学要上传退役证。转专业申请需阐述转专业动机，以及自身符合转入专业要求的证明。提交后，申请按学校既定流程，依次审核，审核通过后，学籍状态相应变更，保障学生学业安排与学校管理秩序。",
        path: "oaApprove"
    },
    // {
    // 	bgColor: '#F4FCFB',
    // 	id: 2,
    // 	title: '学生个人信息修改',
    // 	iconImg: '@nginx/workHall/student/infoEditIcon.png',
    // 	content: '暂无说明'
    // },
    {
        bgColor: "#FCFAF4",
        id: 3,
        fromKey: null,
        title: "成绩查询",
        iconImg: "@nginx/workHall/student/resultInquiryIcon.png",
        content: "当社团因成员流失、活动难以开展、内部管理不善等原因，无法继续运营时，社团负责人可通过此功能提交注销申请。申请时，需详细说明注销原因，如连续多次活动参与人数过少、社团财务状况难以维持运转等，并提交社团近段时间的财务报表、活动总结等资料，以证明社团运营情况。提交申请后，由学校社团管理部门，如团委或学生会社团部，组织审核，评估社团注销必要性与后续事宜处理方案，审核通过后，社团正式注销，相关资源、场地等安排相应调整。",
        path: "scoreManage",
        code: "scoreManage"
    },
    {
        bgColor: "#FDF5F4",
        id: 4,
        title: "解除处分申请",
        fromKey: "studentDisciplinary_",
        iconImg: "@nginx/workHall/student/dismissPunishmentIcon.png",
        content: "学生在受到学校纪律处分，如警告、严重警告、记过、留校察看等，且处分期满后，若在考察期内表现良好，遵纪守法，无再犯违纪行为，可申请解除处分。申请时，学生需提交解除处分申请书，阐述自身对错误的深刻认识与改正表现；附上考察期内的思想汇报，展示思想转变与成长；提供成绩单，证明学业上的努力与进步；若有获得的荣誉证书、参与公益活动证明等能体现突出表现的材料，也一并上传。申请按流程经辅导员、院系学生工作负责人、学校学生管理部门等层层审核，审核通过，解除处分记录入档，学生恢复正常权益。",
        path: "oaApprove"
    },
    {
        bgColor: "#F4F9FD",
        id: 5,
        fromKey: "establishClub_",
        title: "成立社团申请",
        iconImg: "@nginx/workHall/student/addCommunityIcon.png",
        content: "有共同兴趣爱好、目标追求的学生，可发起成立社团申请。在申请页面，需填写社团名称、宗旨、活动范围、组织架构等基础信息，详细规划社团未来一年的活动计划，包括活动主题、时间、预期效果等。同时，明确社团指导老师，指导老师需具备相关专业知识或经验，为社团发展提供专业指导。提交申请时，还需附上不少于一定数量（如 10 名）学生签名的成员意向表。学校社团管理部门收到申请后，组织专家评审，从社团可行性、创新性、发展潜力等多方面评估，审核通过后，社团可正式筹备成立，开启招新与活动开展等工作。",
        path: "oaApprove"
    },
    // {
    // 	bgColor: '#F4FCFC',
    // 	id: 6,
    // 	title: '学生成绩单自助打印',
    // 	iconImg: '@nginx/workHall/student/gradePrintingIcon.png',
    // 	content: '暂无说明'
    // },
    {
        bgColor: "#FDFBF4",
        id: 7,
        title: "加入社团申请",
        fromKey: "joinClub_",
        iconImg: "@nginx/workHall/student/joinClubIcon.png",
        content: "学生对学校已有的社团感兴趣，可通过该功能提交加入申请。在社团列表中选择心仪社团，点击申请加入，填写个人简介，简要说明自己的兴趣特长、加入社团的动机，如对社团举办的某项活动感兴趣，期望在社团中锻炼自身组织能力等。部分社团可能要求申请人回答特定问题，如申请摄影社团，需回答对摄影技巧的理解等。提交申请后，社团负责人或社团管理团队在系统后台审核，根据社团招新计划、申请人条件等综合评估，审核通过，学生即成为社团成员，可参与社团组织的各类活动，拓展兴趣爱好与社交圈子。",
        path: "oaApprove"
    },
    // {
    // 	bgColor: '#F4FCFA',
    // 	id: 8,
    // 	title: '教学评价',
    // 	iconImg: '@nginx/workHall/student/teachingEvaluationIcon.png',
    // 	content: '暂无说明'
    // },
    {
        bgColor: "#FCF4F4",
        id: 9,
        title: "注销社团申请",
        fromKey: "logoutClub_",
        iconImg: "@nginx/workHall/student/cancelClubIcon.png",
        content: "登录系统，在成绩查询页面，可按学期、筛选查看各科成绩。成绩展示包括平时成绩、考试成绩等信息。若对成绩有疑问，可通过系统反馈渠道向任课教师或教学管理部门咨询，方便学生掌握学业进展，规划学习计划。",
        path: "oaApprove"
    }
]
export const teacherList = [
    {
        bgColor: "#F4F8FC",
        id: 1,
        fromKey: null,
        title: "教师个人信息修改",
        iconImg: "@nginx/workHall/teacher/infoEditIcon.png",
        content: "教师个人信息修改功能支持教师在线自主维护个人资料。教师可登录系统，对基本信息（如姓名、性别、联系方式）、职称职务等字段进行修改更新。系统自动校验信息格式（如身份证号、邮箱有效性）。",
        path: "perfectMore",
        code: "perfectMore"
    },
    {
        bgColor: "#F4FCFB",
        id: 2,
        title: "调课申请",
        fromKey: "adjustTimetable_",
        iconImg: "@nginx/workHall/teacher/adjustClassesIcon.png",
        content: "教师因个人事务、学术会议等特殊情况，无法按原定时间授课时，可通过该功能提交调课请求。在申请界面，教师需详细填写原课程信息，包括课程名称、授课班级、时间与地点。同时，注明调整后的时间、地点或更换的任课教师等变动内容，并阐述调课原因。提交后，系统按既定流程，依次推送至相关人员进行审核。审核通过。",
        path: "oaApprove"
    },
    {
        bgColor: "#FDF4F4",
        id: 3,
        title: "停课申请",
        fromKey: "stopTimetable_",
        iconImg: "@nginx/workHall/teacher/suspendClassesIcon.png",
        content: "当教师遭遇突发紧急状况，如生病住院、意外事故等，致使短期内无法授课，可使用此功能。申请时，教师要明确停课的课程、具体周次及时段，并清晰说明停课缘由。若后续确定补课计划，也可一并备注。提交的申请审核同意后，由相关人员发布停课通知，告知学生课程暂停安排，避免学生空等。",
        path: "oaApprove"
    },
    {
        bgColor: "#FCFAF4",
        id: 4,
        fromKey: null,
        title: "成绩录入",
        iconImg: "@nginx/workHall/teacher/scoreEntryIcon.png",
        content: "课程考核结束，教师登录成绩录入系统，选定所授课程与对应教学班。支持单个学生成绩手动输入，也可借助 Excel 模板批量导入，提升效率。录入过程中，教师可对成绩进行复查、修改。对于缺考、作弊等特殊情况，通过特定选项标记。完成录入并确认无误后提交，成绩随即进入系统数据库，学生后续能自行查询。",
        path: "scoreManage",
        code: "scoreManage"
    },
    {
        bgColor: "#F4F9FD",
        id: 5,
        title: "请假申请",
        fromKey: "teacherLeave_",
        iconImg: "@nginx/workHall/teacher/leaveIcon.png",
        content: "因事、因病需请假时均可使用。填写个人信息、请假类型（事假、病假等）、请假起止时间、详细事由。提交后，申请按预设流程流转，依次由相关人员审批，审批结果实时反馈给申请人，方便提前规划后续安排。",
        path: "oaApprove"
    },
    {
        bgColor: "#F4FCFC",
        id: 6,
        fromKey: null,
        title: "教评结果查看",
        iconImg: "@nginx/workHall/teacher/teachingEvaluationIcon.png",
        content: "学期末教学评价结束，教师可通过此功能查看学生对自身教学的评价数据。系统以量化分数呈现教学态度、教学方法、课程内容等维度评价，教师据此了解教学优势与不足，为后续教学改进提供方向，优化教学过程，提升教学质量。",
        path: "evalActivity",
        code: "evalActivity"
    },
    {
        bgColor: "#FCFAF4",
        id: 7,
        fromKey: null,
        title: "成绩查询",
        iconImg: "@nginx/workHall/teacher/resultInquiryIcon.png",
        content: "登录系统，在成绩查询页面，可按学期、筛选查看各科成绩。成绩展示包括平时成绩、考试成绩等信息。若对成绩有疑问，可通过系统反馈渠道向任课教师或教学管理部门咨询，方便学生掌握学业进展，规划学习计划。",
        path: "scoreManage",
        code: "scoreManage"
    },
    // {
    // 	bgColor: '#F5FCFA',
    // 	id: 8,
    // 	title: '教学日志提交',
    // 	iconImg: '@nginx/workHall/teacher/LogSubIcon.png',
    // 	content: '暂无说明'
    // },
    // {
    // 	bgColor: '#F5F9FD',
    // 	id: 9,
    // 	title: '教学任务查询',
    // 	iconImg: '@nginx/workHall/teacher/teachingTaskIcon.png',
    // 	content: '暂无说明'
    // },
    {
        bgColor: "#FDF5F4",
        id: 10,
        title: "课表查询",
        iconImg: "@nginx/workHall/teacher/schoolTimetableIcon.png",
        content: "暂无说明",
        path: "schoolTable",
        code: "schoolTable",
        fromKey: null
    },
    // {
    // 	bgColor: '#FCFBF4',
    // 	id: 11,
    // 	title: '上课点名册',
    // 	iconImg: '@nginx/workHall/teacher/attendanceSheetsIcon.png',
    // 	content: '学生因特殊情况需对学籍状态进行调整，如休学、复学、退学、转学、转专业等，可通过该功能提交申请。申请休学的学生，需明确休学原因，像因病休学需附上县级以上医院开具的证明；创业休学则要提供创业计划书等相关材料。复学申请需说明复学依据，如退役复学要上传退役证。转专业申请需阐述转专业动机，以及自身符合转入专业要求的证明。提交后，申请按学校既定流程，依次审核，审核通过后，学籍状态相应变更，保障学生学业安排与学校管理秩序。'
    // },
    {
        bgColor: "#F4FCFA",
        id: 12,
        title: "教学班成绩分析报告",
        iconImg: "@nginx/workHall/teacher/achievementIcon.png",
        content: "课程成绩录入完成，系统依据班级学生成绩数据自动生成统计报表。从平均分、中位数、最高分、最低分等统计成绩分布；以图表呈现各分数段人数占比，直观展示成绩分布形态。教师借助报告精准洞察教学效果，找出薄弱环节，为后续教学策略调整提供数据支撑。",
        fromKey: null,
        path: "scoreManage",
        code: "scoreManage"
    }
]

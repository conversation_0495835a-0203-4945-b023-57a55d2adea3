<template>
    <yd-page-view class="chat" :title="pageTitle" :hideBottom="false" :hideLeft="true">
        <!-- #ifdef H5-WEIXIN || H5 -->
        <template #right>
            <div class="address" @click="gotoAddressBook">
                通讯录
                <image class="address_logo" mode="widthFix" src="@nginx/chat/address_logo2.png" alt="" />
            </div>
        </template>
        <!-- #endif -->
        <!-- #ifdef APP-PLUS -->
        <template #right>
            <div class="address" @click="gotoAddressBook">
                通讯录
                <image class="address_logo" mode="widthFix" src="@nginx/chat/address_logo2.png" alt="" />
            </div>
        </template>
        <!-- #endif -->
        <!-- #ifdef MP-WEIXIN -->
        <div class="address" @click="gotoAddressBook">
            通讯录
            <image class="address_logo" mode="widthFix" src="@nginx/chat/address_logo2.png" alt="" />
        </div>
        <!-- #endif -->
        <div class="list">
            <div class="item" v-for="item in fixedList" :key="item.type" @click="gotoDetail(item)">
                <div class="left_avatar">
                    <uni-badge :customStyle="{ background: '#FB2D24' }" :text="item.type == 'todo' ? item.total : null" absolute="rightTop" size="small">
                        <image class="avatar" mode="widthFix" :src="item.url" alt="" />
                    </uni-badge>
                </div>
                <div class="content">
                    <div class="title">
                        <div class="name">{{ item.title }}</div>
                        <div class="time" v-if="item.time">{{ item.time }}</div>
                    </div>
                    <div class="content_text ellipsis" v-if="item.type != 'addressBook'">{{ item.content || `暂无${item.title}` }}</div>
                </div>
            </div>
        </div>
    </yd-page-view>
</template>

<script setup>
const pageTitle = ref("消息")
const fixedList = ref([
    {
        type: "todo",
        title: "待办",
        url: "@nginx/chat/todo_logo.png",
        content: "",
        time: "",
        total: 0
    },
    {
        type: "message",
        title: "消息通知",
        url: "@nginx/chat/message_logo.png",
        content: "",
        time: ""
    }
    // {
    //     type: "addressBook",
    //     title: "通讯录",
    //     url: "@nginx/chat/address_logo.png"
    // }
])

function gotoDetail(item) {
    navigateTo({
        url: `/package/chat/${item.type}/index`
    })
}

// 获取待办
function getTodo() {
    http.post("/app/ruTask/page", {
        pageNo: 1,
        pageSize: 1,
        status: 1 // 0全部 1待办 2已办
    }).then((res) => {
        const title = res.data?.list[0]?.title || res.data?.list[0]?.subtitle
        const startTime = res.data?.list[0]?.startTime
        fixedList.value = fixedList.value.map((i) => {
            return {
                ...i,
                total: i.type == "todo" ? res.data.total : i.total,
                time: i.type == "todo" ? startTime : i.time,
                content: i.type == "todo" ? title : i.content
            }
        })
    })
}

// 获取消息通知
function getMessage() {
    http.post("/app/v2/push/pageUserMessage", {
        pageNo: 1,
        pageSize: 1
    }).then((res) => {
        const title = res.data?.list[0]?.title || "-"
        const sendTime = res.data?.list[0]?.sendTime
        fixedList.value = fixedList.value.map((i) => {
            return {
                ...i,
                time: i.type == "message" ? sendTime : i.time,
                content: i.type == "message" ? title : i.content
            }
        })
    })
}

function gotoAddressBook() {
    navigateTo({
        url: "/package/chat/addressBook/index"
    })
}

onMounted(() => {
    getMessage()
    getTodo()
})
</script>

<style lang="scss" scoped>
.chat {
    .address {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-color-primary;
        line-height: 40rpx;
        .address_logo {
            margin-left: 10rpx;
            width: 44rpx;
            height: 44rpx;
        }
    }
    // #ifdef MP-WEIXIN
    .address {
        padding: 10rpx 30rpx;
        justify-content: flex-start;
    }
    // #endif
    .list {
        width: 100%;
        background: $uni-bg-color;
        .item {
            width: calc(100vw - 60rpx);
            display: flex;
            align-items: center;
            padding: 30rpx;
            .left_avatar {
                width: 100rpx;
                height: 100rpx;
                .avatar {
                    width: 100rpx;
                    height: 100rpx;
                }
            }
            .content {
                padding: 4rpx 0rpx;
                flex: 1;
                height: 100rpx;
                margin-left: 20rpx;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .title {
                    display: flex;
                    justify-content: space-between;
                    .name {
                        color: $uni-color-primary;
                        font-weight: 500;
                        font-size: 30rpx;
                        line-height: 42rpx;
                        text-align: left;
                    }
                    .time {
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #999999;
                        line-height: 34rpx;
                        text-align: right;
                    }
                }
                .content_text {
                    width: 100%;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #666666;
                    line-height: 40rpx;
                    text-align: left;
                    padding: 0rpx;
                }
            }
        }
    }
}
</style>

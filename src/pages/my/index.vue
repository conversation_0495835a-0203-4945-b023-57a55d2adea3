<template>
    <yd-page-view class="my" :hideBottom="false">
        <template #top>
            <!-- 用户信息 -->
            <div class="top_box">
                <image class="bg_img" mode="widthFix" src="@nginx/personalCenter/myTopBg.png" />
                <div class="user_info" @click.stop="gotoMyInfo">
                    <image class="info_avatar" :src="userInfo.avatar || userInfo.defaultAvatar" />
                    <div class="name_box">
                        <div>
                            <span class="name">{{ userInfo.name }}</span>
                            <uni-icons type="right" size="24"></uni-icons>
                        </div>
                        <div class="identities_info">
                            {{ identityInfo?.roleName }}
                        </div>
                    </div>
                    <div class="switch_identities" @click.stop="switchIdentities">切换身份</div>
                </div>
            </div>
            <div class="integration_box" v-if="identityInfo?.roleCode == 'eltern'">
                <div class="integration">
                    <div @click="handlerDetailsPage(item.code)" v-for="item in emsCountList" :key="item.code" class="item_integration">
                        <span class="num" v-if="item.code == 'totalScore'">{{ userEmsCount[item.code]?.toFixed(1) || 0 }}</span>
                        <span v-else class="num">{{ userEmsCount[item.code] || 0 }}</span>
                        <span class="title">{{ item.title }}</span>
                    </div>
                </div>
            </div>
        </template>

        <uni-list :border="false">
            <div v-for="item in list" :key="item.code">
                <uni-list-item v-if="item.isShow" showArrow :border="false" :title="item.title" clickable @click="goPage(item)">
                    <template v-slot:header>
                        <view class="slot-box">
                            <image class="img_class" :src="item.img" mode="widthFix"></image>
                        </view>
                    </template>
                </uni-list-item>
            </div>
        </uni-list>

        <!-- 二次确认人脸协议弹框 -->
        <yd-face ref="facePopupRef" />
    </yd-page-view>
</template>

<script setup>
import useStore from "@/store"
import { computed, onMounted } from "vue"

const { user, local } = useStore()
const facePopupRef = ref(false) // 人脸采集弹框
const userInfo = computed(() => user.userInfo) // 用户信息
const identityInfo = computed(() => user.identityInfo) // 角色信息

const userEmsCount = ref({})

const emsCountList = ref([
    {
        code: "totalScore",
        title: "我的积分"
    },
    {
        code: "scoreCardCount",
        title: "我的积分卡"
    },
    {
        code: "medalCount",
        title: "我的勋章"
    }
])

const list = [
    // {
    //     code: "scan",
    //     img: "@nginx/personalCenter/scan.png",
    //     title: "扫一扫",
    //     isShow: true
    // },
    {
        code: "studentInfo",
        img: "@nginx/personalCenter/studentInfo.png",
        title: "学生信息",
        isShow: identityInfo.value?.roleCode == "eltern" // eltern 家长
    },
    {
        code: "face",
        img: "@nginx/personalCenter/face.png",
        title: "人脸采集",
        isShow: identityInfo.value?.roleCode != "eltern" // 不等于家长
    },
    {
        code: "security",
        img: "@nginx/personalCenter/security.png",
        title: "安全中心",
        isShow: true
    },
    {
        code: "set",
        img: "@nginx/personalCenter/set.png",
        title: "设置",
        isShow: true
    },
    {
        code: "feedback",
        img: "@nginx/personalCenter/feedback.png",
        title: "意见反馈",
        isShow: true
    },
    {
        code: "help",
        img: "@nginx/personalCenter/help.png",
        title: "帮助中心",
        isShow: false // TODO: 暂不显示，有空要补上
    },
    {
        code: "sign",
        img: "@nginx/personalCenter/sign.png",
        title: "电子签名",
        isShow: true
    }

]

function gotoMyInfo() {
    navigateTo({
        url: "/package/my/myInfo/index"
    })
}

function switchIdentities() {
    clearStore()
    navigateTo({
        url: "/package/login/selectIdentity",
        query: {
            type: "switchIdentities"
        }
    })
}
// 路由跳转
function routerFn(code) {
    const routerUrl = {
        face: "/package/my/face/index", // 人脸采集
        studentInfo: "/package/my/studentInfo/index", // 学生信息
        security: "/package/my/security/index", // 安全中心
        set: "/package/my/settings/index", // 设置
        feedback: "/package/my/feedback/index", // 意见反馈
        sign: "/package/my/sign/index" // 电子签名
    }
    if (routerUrl[code]) {
        navigateTo({
            url: routerUrl[code]
        })
    }
}

function goPage(item) {
    if (item.code == "face" && !local.agreeFace) {
        facePopupRef.value.open()
    } else if (item.code == "scan") {
        scanCode()
    } else {
        routerFn(item.code)
    }
}

function handlerDetailsPage(code) {
    const params = {
        type: code,
        personId: user.studentInfo[0]?.studentId,
        isShowStudent: true
    }
    navigateTo({
        url: "/apps/evalActivity/eltern/detailsPage",
        query: params
    })
}

function getElternData() {
    http.post("/app/evalStatistic/getUserEmsCount", {
        personId: user.studentInfo[0]?.studentId,
        identity: 0 // 身份标识(0.学生 1.教职工 )
    }).then((res) => {
        userEmsCount.value = res.data
    })
}

onMounted(async () => {
    if (user?.identityInfo?.roleCode == "eltern") {
        getElternData()
    }
})
</script>

<style lang="scss" scoped>
.my {
    .top_box {
        position: relative;
        min-height: 432rpx;

        .bg_img {
            width: 100%;
            height: auto;
        }

        .user_info {
            width: calc(100% - 60rpx);
            height: 80%;
            position: absolute;
            top: 88rpx;
            padding: 0 30rpx;
            left: 0;
            display: flex;
            align-items: center;

            .info_avatar {
                width: 160rpx;
                height: 160rpx;
                border-radius: 50%;
            }

            .name_box {
                margin-left: 28rpx;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .name {
                    font-weight: 600;
                    font-size: 44rpx;
                    color: $uni-text-color;
                    line-height: 60rpx;
                }

                .identities_info {
                    padding-top: 16rpx;
                    font-weight: 400;
                    font-size: 32rpx;
                    color: $uni-text-color;
                    line-height: 44rpx;
                }
            }

            .switch_identities {
                top: 50%;
                right: 30rpx;
                position: absolute;
                width: 152rpx;
                height: 56rpx;
                background: $uni-color-primary;
                border-radius: 28rpx;
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color-inverse;
                line-height: 56rpx;
                text-align: center;
            }
        }
    }
    .integration_box {
        height: 114rpx;
        position: relative;
        .integration {
            position: absolute;
            top: -76rpx;
            left: 0;
            margin: 0rpx 30rpx;
            width: calc(100% - 180rpx);
            height: 110rpx;
            background: $uni-bg-color;
            box-shadow: 0rpx 0rpx 12rpx 0rpx rgba(0, 0, 0, 0.1);
            border-radius: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30rpx 60rpx;
            .link {
                background: $uni-border-color;
                height: 60rpx;
                width: 2rpx;
            }
            .item_integration {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                .num {
                    font-size: 40rpx;
                    color: $uni-text-color;
                    line-height: 58rpx;
                    text-align: left;
                    text-align: center;
                    padding-bottom: 8rpx;
                }
                .title {
                    padding-top: 8rpx;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #00000073;
                    line-height: 40rpx;
                    text-align: center;
                }
            }
        }
    }

    .img_class {
        width: 44rpx;
        height: 44rpx;
        margin-right: 20rpx;
    }

    .face_popup {
        width: 90vw;
        border-radius: 20rpx;

        .face_title {
            text-align: center;
            height: 108rpx;
            background: $uni-bg-color;
            border-radius: 20rpx 20rpx 0rpx 0rpx;
            line-height: 108rpx;
            font-weight: 500;
            font-size: 34rpx;
            color: $uni-text-color;
        }

        .face_content {
            height: 650rpx;
            background: $uni-bg-color-grey;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            :deep(.uni-checkbox-input) {
                border-radius: 50%;
            }

            .face_img {
                width: 380rpx;
                height: 380rpx;
            }

            .text {
                font-weight: 400;
                font-size: 24rpx;
                color: $uni-text-color-grey;
                line-height: 48rpx;

                .text_link {
                    color: $uni-color-primary;
                }
            }
        }

        .face_footer {
            height: 100rpx;
            background: $uni-bg-color;
            border-radius: 0rpx 0rpx 20rpx 20rpx;
            display: flex;
            justify-content: center;

            .btn {
                width: 50%;
                text-align: center;
                font-weight: 400;
                font-size: 28rpx;
                color: $uni-color-primary;
                line-height: 100rpx;
            }
        }
    }
}
</style>

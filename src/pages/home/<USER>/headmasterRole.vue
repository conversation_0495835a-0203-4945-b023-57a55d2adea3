<template>
    <yd-page-view ref="page" :refresherOnly="true" @onRefresh="onRefresh" page-background="#0000" background-color="#0000" :hideBottom="false" color="#fff" :hideTop="true">
        <view class="container">
            <!-- 任课班级 -->
            <view class="my_class_box">
                <view class="class_list" @click="changeClass">
                    <div class="top_info">
                        <image :src="classesInfo.classesIcon || '@nginx/components/class.png'" class="class_icon" mode="scaleToFill" />
                        <div class="class_info">
                            <view class="name_box" @click.stop="selectOpenClass">
                                <text class="class_name">{{ classesInfo.classesName }} </text>
                                <image src="@nginx/components/selectClesses.png" mode="scaleToFill" class="select_clesses" />
                            </view>

                            <text class="watchword ellipsis">
                                {{ classesInfo && classesInfo.watchwordList ? classesInfo.watchwordList[0].projectValue : "-" }}
                            </text>
                        </div>
                    </div>
                    <div class="classes_info">
                        <view class="info">
                            <text class="info_num">{{ classesInfo.studentNums || 0 }}</text>
                            <text class="info_title">班级人数</text>
                        </view>
                        <view class="split_line"></view>
                        <view class="info">
                            <text class="info_num ellipsis">{{ classesInfo.master || "-" }}</text>
                            <text class="info_title">班主任</text>
                        </view>
                    </div>
                </view>
            </view>
            <!-- 快捷应用 -->
            <scroll-view :scroll-x="true" class="app_content">
                <view class="app_category" v-for="(item, index) in appItems" :key="index" @click="jumpApp(item)">
                    <image :src="`@nginx/home/<USER>/${item.img}.png`" class="img"></image>
                    <view>{{ item.name }}</view>
                </view>
            </scroll-view>
            <!-- 考勤数据 -->
            <view class="attendance_content">
                <swiper style="height: 500rpx" :current="state.attendanceCurrent" circular :indicator-dots="true" indicator-active-color="#00B781" @change="(e) => change(e, 'attendanceCurrent')">
                    <swiper-item v-for="(item, index) in state.attendanceList" :key="index">
                        <view class="attendance_box">
                            <view class="main">
                                <view class="top_msg">{{ item.ruleStatus == 0 ? "签到" : item.ruleStatus == 1 ? "签退" : "暂无考勤" }}</view>
                                <l-circularProgress class="yd_circularProgress" :canvasId="'canvasId' + index" :bgCanvasId="'bgCanvasId' + index" :fontShow="false" :percent="percent(item)" type="halfCircular" :lineWidth="14" progressColor="#00B781" bgColor="#F0F2F5" :boxWidth="240" :boxHeight="150">
                                    <view class="yd_circular_box">
                                        <view>
                                            <text class="left">实到 {{ item.actualNum || "-" }}</text> / <text>应到 {{ item.totalNum || "-" }}</text>
                                        </view>
                                    </view>
                                </l-circularProgress>
                            </view>
                            <view class="footer">
                                <!-- TODO: 字段未确认 -->
                                <view class="footer_box">迟到 {{ item.absenteeismNum || "-" }}</view>
                                <view class="footer_box">请假 {{ item.leaveNum || "-" }}</view>
                                <view class="footer_box danger" :style="{ color: dayjs().isBefore(dayjs(item.endTime)) ? '#f5222d' : '#8C8C8C' }">
                                    {{ dayjs().isBefore(dayjs(item.endTime)) ? "缺勤" : "未签到" }}
                                    {{ item.absenteeismNum || "-" }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </swiper>
            </view>
            <!-- 课程 -->
            <view class="course_content">
                <view class="l_content_box">
                    <view class="course_box">
                        <view
                            class="top_text"
                            v-if="state.session.subjectName && !isMoreEndTime"
                            :style="{
                                background: isTimeInRange(state.session.startTime, state.session.endTime) ? '#f5faf8' : '#FFF6E5',
                                color: isTimeInRange(state.session.startTime, state.session.endTime) ? '#00B781' : '#FAAD14'
                            }"
                        >
                            <text v-if="isTimeInRange(state.session.startTime, state.session.endTime)">上课中</text>
                            <view v-else class="countdown_box"><uni-countdown color="#FAAD14" :show-day="false" :hour="state.countTime.hour" :minute="state.countTime.minute" :second="state.countTime.second" @timeup="timeup"></uni-countdown>后上课</view>
                        </view>
                        <view class="top_text" v-else style="background: #d8d8d8; color: #fff"> 今日课程已完结 </view>
                        <view class="main">
                            <view class="l_box">
                                <view class="img_box"><image :src="`@nginx/home/<USER>/${imgMap[state.session.subjectName]}.png`" class="img" /></view>
                                <view>{{ state.session.subjectName || "暂无课程" }}</view>
                            </view>
                        </view>
                        <view class="footer_box"
                            >{{ state.session.startTime || "00:00" }}-{{ state.session.endTime || "00:00" }} |
                            <text v-if="state.session?.teacherList.length">
                                <text v-for="(item, index) in state.session.teacherList" :key="index">{{ item.name }}</text>
                            </text>
                            <text v-else>---</text>
                        </view>
                    </view>
                </view>
                <view class="r_content_box">
                    <swiper style="height: 360rpx" :current="state.courseCurrent" circular :indicator-dots="true" indicator-active-color="#00B781" @change="(e) => change(e, 'courseCurrent')">
                        <swiper-item v-for="(item, index) in state.courseList" :key="index">
                            <view class="course_box">
                                <view
                                    class="top_text"
                                    :style="{
                                        background: courseCss[item.type].bg,
                                        color: courseCss[item.type].color
                                    }"
                                    >{{ courseType[item.type] }}</view
                                >
                                <view class="main">
                                    <scroll-view>
                                        <view
                                            class="item_box"
                                            v-for="(i, idx) in item.list"
                                            :key="idx"
                                            :style="{
                                                background: isTimeInRange(i.startTime, i.endTime) ? '#f5faf8' : '',
                                                color: isTimeInRange(i.startTime, i.endTime) ? '#00B781' : ''
                                            }"
                                        >
                                            <view
                                                class="l_text"
                                                :style="{
                                                    color: isTimeInRange(i.startTime, i.endTime) ? '#00B781' : ''
                                                }"
                                                >{{ i.name }}</view
                                            >
                                            <view class="r_text">{{ i.subjectName || "暂无课程" }}</view>
                                        </view>
                                    </scroll-view>
                                </view>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
            </view>
            <!-- 相册 -->
            <view class="album_content">
                <view v-if="Object.keys(state.classify).length" class="album_box">
                    <view class="header_box">
                        <view>{{ state.classify.name }}</view>
                        <image class="img" src="@nginx/home/<USER>/xiangji.png"></image>
                    </view>
                    <image class="img" :src="state.classify.url"></image>
                </view>
                <view class="empty_box" v-else>
                    <image class="img" src="@nginx/components/empty.png" />
                    <view class="text">暂无班级相册，<view style="color: #00b781">点击上传</view>照片</view>
                </view>
            </view>
            <!-- 值日 -->
            <view class="duty_content">
                <view class="l_box"><image class="img" src="@nginx/home/<USER>/zhiri.png" /></view>
                <view class="r_box">
                    <view class="top_text">今日值日</view>
                    <view class="empty_msg" v-if="state?.dutyList.length == 0">今日暂无值日,可<text style="color: #00b781">前往设置</text></view>
                    <view v-else>
                        <view
                            class="main_box"
                            v-for="(item, index) in state.dutyList"
                            :key="index"
                            :style="{
                                borderColor: index > 0 ? '#db9448' : '#00b781',
                                background: index > 0 ? '#fdf7f0' : '#e5f9f2'
                            }"
                        >
                            <view>{{ item.beginTime }}-{{ item.endTime }}</view>
                            <view class="msg"
                                ><view class="text" v-for="(i, idx) in item.students" :key="idx">{{ i.studentName }}</view></view
                            >
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </yd-page-view>
    <yd-select-popup :fieldNames="{ value: 'id', label: 'showName' }" ref="selectPopupRef" title="请选择班级" :list="classesMaterList" @closePopup="closePopup" :selectId="[classesInfo.classesId]"></yd-select-popup>
</template>

<script setup>
import dayjs from "dayjs"
import LCircularProgress from "@/subModules/components/l-circular-progress/components/l-circular-progress/l-circular-progress.vue"
import { useShowLoading } from "@/hooks"
import useStore from "@/store"

let today = dayjs().format("d")
today = today == 0 ? 6 : today - 1
const selectPopupRef = ref(null)
const { startLoading, stopLoading } = useShowLoading()
const { user } = useStore()
const page = ref(null)

const classesMaterList = computed(() => user.userInfo.classesMaterList)

const selectClassId = ref(null)
const classesInfo = ref({})

const appItems = ref([
    { code: "campusStyle", routePath: "campusStyle", img: "xiangce", name: "相册" },
    { code: "videoAlbum", routePath: "videoAlbum", img: "shipin", name: "视频" },
    { code: "none", routePath: "none", img: "rongyu", name: "荣誉" },
    { code: "none", routePath: "none", img: "gonggao", name: "通知" },
    { code: "myStudent", routePath: "myStudent", img: "xuesheng", name: "学生" },
    { code: "none", routePath: "none", img: "daojishi", name: "倒计时" }
])

const state = reactive({
    attendanceCurrent: 0,
    courseCurrent: 0,
    workCurrent: 0,
    punchCurrent: 0,
    session: {
        teacherList: []
    },
    attendanceList: [],
    courseList: [],
    workList: [{}, {}],
    punchList: [{}, {}],
    dutyList: [],
    allCourseList: [], // 课程列表
    countTime: {
        hour: 0,
        minute: 0,
        second: 0
    },
    classify: {}
})

const courseCss = {
    earlySelfStudyList: {
        bg: "#FFFBFB",
        color: "#7D282C"
    },
    morningList: {
        bg: "#E8FBFA",
        color: "#028980"
    },
    afternoonList: {
        bg: "#F6FBFF",
        color: "#115C87"
    },
    lateSelfStudyList: {
        bg: "#F8F8FF",
        color: "#0A0D85"
    },
    nightList: {
        bg: "#F8F8FF",
        color: "#0A0D85"
    }
}

const imgMap = {
    语文: "yuwen",
    数学: "shuxue",
    英语: "yingyu",
    物理: "wuli",
    化学: "huaxue",
    生物: "shengwu",
    政治: "zhengzhi",
    历史: "lishi",
    地理: "dili",
    体育: "tiyu",
    美术: "meishu",
    音乐: "yinyue",
    计算机课: "computed"
}

const isMoreEndTime = computed(() => {
    const item = state.allCourseList[state.allCourseList.length - 1]
    return isCurrentTimeAfter(item.endTime)
})

const percent = computed(() => (val) => {
    if (Object.keys(val).length === 0) return 0
    return Math.floor((actualNum / totalNum) * 100)
})

const change = (e, type) => {
    state[type] = e.detail.current
}

function onRefresh() {
    startLoading("刷新中...")
    setTimeout(() => {
        // 1.5秒之后停止刷新动画
        page.value.paging.complete([])
        stopLoading()
    }, 1500)
}

function selectOpenClass() {
    selectPopupRef.value.open()
}

function closePopup(val) {
    if (!val && val.id == selectClassId.value) return
    selectClassId.value = val.id
    getClassesDetail()
}

function getClassesDetail() {
    http.get("/app/master/class/getClassesDetail", { classesId: selectClassId.value }).then((res) => {
        classesInfo.value = res.data
        console.log(classesInfo.value)
    })
}

const init = () => {
    getClassesDetail()
    getDutyList()
    getWeekCourse()
    getAttendance()
    getClassifyList()
}

onMounted(() => {
    if (classesMaterList.value && classesMaterList.value.length) {
        selectClassId.value = classesMaterList.value[0].id
        init()
    }
})

function changeClass() {
    navigateTo({
        url: "/apps/myStudent/lecturer/index",
        query: {
            classesId: selectClassId.value
        }
    })
}

// 获取值日列表
function getDutyList() {
    http.post("/brand/app/v3/studentduty/list", { classesId: selectClassId.value }).then((res) => {
        state.dutyList = res.data[today][0]?.infoList || []
    })
}

// 获取学生考勤
function getAttendance() {
    http.post("/app/master/attendance/homepage", { classesId: selectClassId.value }).then((res) => {
        state.attendanceList = res.data.length ? res.data : [{}]
    })
}

// 获取班级相册
function getClassifyList() {
    http.get("/app/master/class/classifyList", { classesId: selectClassId.value }).then((res) => {
        state.classify = res.data.length ? res.data[0] : {}
    })
}

const courseType = {
    earlySelfStudyList: "早自习",
    morningList: "上午",
    afternoonList: "下午",
    lateSelfStudyList: "晚自习",
    nightList: "晚上"
}

// 倒计时时间到了刷新周课程列表
const timeup = () => {
    getWeekCourse()
}

// 获取周课表
function getWeekCourse() {
    http.post("/app/timetable/getWeekTime", { type: 1, typeId: selectClassId.value }).then((res) => {
        const {
            data: { sectionList, timetableList }
        } = res
        const date = dayjs().format("YYYY-MM-DD")
        const dateList = timetableList.filter((i) => i.date == date)
        state.courseList = Object.keys(courseType)
            .map((i) => ({
                type: i,
                list: sortData(sectionList[i], dateList)
            }))
            .filter((i) => i.list.length)
        let arr = Object.keys(courseType).map((i) => sortData(sectionList[i], dateList).map((item) => ({ ...item, type: i })))
        arr = arr.flat(Infinity)
        console.log("arr:", arr)
        // 获取当前课程
        state.allCourseList = []
        state.courseList.forEach((item) => {
            state.allCourseList.push(...item.list)
        })
        state.session = findCurrentOrNextSession(state.allCourseList, new Date())
        state.countTime = getTimeDifference(state.session?.startTime)
    })
}

function sortData(arr, sourceData) {
    const sourceMap = new Map(sourceData.map((item) => [item.section, item]))
    return arr.map((item) => {
        const matched = sourceMap.get(item.sequence)
        return matched ? { ...matched, ...item } : item
    })
}

// 判断当前时间是否在指定时间范围内
function isTimeInRange(start, end) {
    // 获取当前时间
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    const currentTime = currentHour * 60 + currentMinute // 转换为总分钟数便于比较

    // 解析开始时间
    const [startHour, startMinute] = start.split(":").map(Number)
    const startTime = startHour * 60 + startMinute

    // 解析结束时间
    const [endHour, endMinute] = end.split(":").map(Number)
    const endTime = endHour * 60 + endMinute

    // 判断是否在时间范围内
    return currentTime >= startTime && currentTime <= endTime
}

// 判断当前时间是否大于指定的 HH:mm 格式的时间
function isCurrentTimeAfter(timeStr) {
    // 容错处理：如果传入为 null 或非字符串，直接返回 null
    if (timeStr === null || typeof timeStr !== "string") {
        return null
    }

    // 正则校验格式是否正确（HH:mm，24小时制）
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/
    if (!timeRegex.test(timeStr)) {
        return null
    }

    // 获取当前时间
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()

    // 解析传入的时间
    const [hourStr, minuteStr] = timeStr.split(":")
    const inputHour = parseInt(hourStr, 10)
    const inputMinute = parseInt(minuteStr, 10)

    // 比较时间
    return currentHour > inputHour || (currentHour === inputHour && currentMinute > inputMinute)
}

// 用于查找当前时间所在的会话或下一个会话
function findCurrentOrNextSession(schedule, currentTime) {
    let currentSession = null
    let nextSession = null

    // 用于记录最后一个时间段
    let lastSession = schedule[0]

    for (let session of schedule) {
        const startParts = session.startTime.split(":").map(Number)
        const endParts = session.endTime.split(":").map(Number)

        const startTime = new Date(currentTime.getTime())
        startTime.setHours(startParts[0], startParts[1], 0, 0)

        const endTime = new Date(currentTime.getTime())
        endTime.setHours(endParts[0], endParts[1], 0, 0)

        // 更新最后一个会话
        lastSession = session

        // 判断是否是当前时间段
        if (currentTime >= startTime && currentTime < endTime) {
            currentSession = session
            break // 找到后直接退出循环
        }

        // 如果还没找到当前会话，尝试找下一个时间段（比现在时间晚的）
        if (currentTime < startTime) {
            if (!nextSession || startTime < new Date(nextSession.startTime)) {
                nextSession = session
            }
        }
    }

    // 如果没找到当前时间段，并且也没有未来的 nextSession，则取最后一个时间段
    if (!currentSession && !nextSession) {
        return lastSession
    }

    return currentSession || nextSession
}

//  获取时间差
function getTimeDifference(targetTimeStr) {
    // 如果targetTimeStr为undefined、null、''，则直接返回默认值
    if (!targetTimeStr) {
        return { hour: 0, minute: 0, second: 0 }
    }

    const now = new Date()

    // 默认返回值
    const defaultResult = { hour: 0, minute: 0, second: 0 }

    // 验证目标时间格式是否为 HH:mm
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/
    if (!timeRegex.test(targetTimeStr)) {
        return defaultResult
    }

    // 解析目标时间字符串
    const [hours, minutes] = targetTimeStr.split(":").map(Number)

    // 设置目标时间（注意：月份是从0开始的）
    const targetTime = new Date(now)
    targetTime.setHours(hours, minutes, 0, 0)

    // 如果目标时间在当前时间之前，则加一天
    if (targetTime < now) {
        targetTime.setDate(targetTime.getDate() + 1)
    }

    // 计算时间差
    const diffMs = targetTime - now

    // 转换为小时、分钟、秒
    const totalSeconds = Math.floor(diffMs / 1000)
    const hoursDiff = Math.floor(totalSeconds / 3600)
    const minutesDiff = Math.floor((totalSeconds % 3600) / 60)
    const secondsDiff = totalSeconds % 60

    return {
        hour: hoursDiff,
        minute: minutesDiff,
        second: secondsDiff
    }
}
</script>

<style lang="scss" scoped>
.container {
    padding: 20rpx 28rpx;
    .my_class_box {
        height: 248rpx;
        background: #00b781;
        border-radius: 40rpx;
        padding: 28rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-bottom: 50rpx;
        .class_list {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            .top_info {
                display: flex;

                .class_icon {
                    width: 140rpx;
                    min-width: 140rpx;
                    height: 140rpx;
                    margin-right: 28rpx;
                    border-radius: 50%;
                }
                .class_info {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    .name_box {
                        display: flex;
                        align-items: center;
                        .class_name {
                            font-weight: 600;
                            font-size: 36rpx;
                            color: #ffffff;
                            line-height: 50rpx;
                        }
                        .select_clesses {
                            width: 44rpx;
                            height: 44rpx;
                        }
                    }
                    .watchword {
                        font-weight: 400;
                        font-size: 28rpx;
                        color: #ffffff;
                        line-height: 40rpx;
                    }
                }
            }
            .classes_info {
                width: 100%;
                justify-content: space-between;
                align-items: center;
                display: flex;
                .split_line {
                    width: 1rpx;
                    height: 64rpx;
                    background: #ffffff;
                    opacity: 0.5;
                    margin: 0rpx 20rpx;
                }
                .info {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    justify-content: space-between;
                    flex: 1;
                    .info_num {
                        font-size: 32rpx;
                        color: #ffffff;
                        line-height: 46rpx;
                    }
                    .info_title {
                        font-weight: 400;
                        font-size: 26rpx;
                        color: #ffffff;
                        line-height: 36rpx;
                    }
                }
            }
        }
    }
    .app_content {
        width: 100%;
        white-space: nowrap;
        margin-bottom: 30rpx;
        .app_category {
            width: 130rpx;
            display: inline-flex;
            align-items: center;
            flex-direction: column;
            .img {
                width: 80rpx;
                height: 80rpx;
                margin-bottom: 20rpx;
            }
        }
    }
    .attendance_content {
        border-radius: 16rpx;
        border: 4rpx solid #e7e9e8;
        margin-bottom: 40rpx;
        .attendance_box {
            padding: 30rpx;
            .top {
                display: flex;
                justify-content: space-between;
                .right_text {
                    position: relative;
                    text-align: right;
                    width: 250rpx;
                    padding-right: 30rpx;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    -o-text-overflow: ellipsis;
                    &::after {
                        content: "";
                        width: 0;
                        height: 0;
                        display: inline-block;
                        border: 14rpx solid transparent;
                        border-top-color: #00b781;
                        position: absolute;
                        right: 0rpx;
                        top: 15rpx;
                    }
                }
            }
            .main {
                display: flex;
                flex-direction: column;
                align-items: center;
                .top_msg {
                    text-align: center;
                    margin-bottom: 10rpx;
                }
                .yd_circularProgress {
                    margin: 0 auto;
                    position: relative;
                    .yd_circular_box {
                        z-index: 999;
                        width: 480rpx;
                        height: 150rpx;
                        position: absolute;
                        bottom: 0rpx;
                        left: 0;
                        display: flex;
                        align-items: center;
                        justify-content: space-evenly;
                        flex-direction: column;
                        .top_text {
                            position: relative;
                            text-align: right;
                            width: 250rpx;
                            padding-right: 30rpx;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            -o-text-overflow: ellipsis;
                            margin-bottom: 20rpx;
                            &::after {
                                content: "";
                                width: 0;
                                height: 0;
                                display: inline-block;
                                border: 14rpx solid transparent;
                                border-top-color: #00b781;
                                position: absolute;
                                right: 0rpx;
                                top: 15rpx;
                            }
                        }
                        .left {
                            color: #00b781;
                        }
                    }
                }
            }
            .footer {
                display: flex;
                .footer_box {
                    width: 230rpx;
                    text-align: center;
                }
                .center_box {
                    border-left: 1px solid #d9d9d9;
                    border-right: 1px solid #d9d9d9;
                }
            }
        }
    }
    .course_content {
        display: flex;
        justify-content: space-between;
        margin-bottom: 30rpx;
        .l_content_box {
            width: 48%;
            border: 1px solid #d9d9d9;
            border-radius: 16rpx;
            .course_box {
                display: flex;
                flex-direction: column;
                height: 100%;
                .top_text {
                    text-align: center;
                    font-size: 24rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    margin-bottom: 10rpx;
                    .countdown_box {
                        display: flex;
                        justify-content: center;
                    }
                }
                .main {
                    display: flex;
                    flex: 1;
                    .l_box {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: space-between;
                        .img_box {
                            border-radius: 50%;
                            overflow: hidden;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            .img {
                                width: 160rpx;
                                height: 160rpx;
                            }
                        }
                    }
                }
                .footer_box {
                    text-align: center;
                    height: 80rpx;
                    line-height: 80rpx;
                }
            }
        }
        .r_content_box {
            width: 48%;
            border: 1px solid #d9d9d9;
            border-radius: 16rpx;
            overflow: hidden;
            .course_box {
                display: flex;
                flex-direction: column;
                height: 280rpx;
                .top_text {
                    text-align: center;
                    font-size: 24rpx;
                    height: 60rpx;
                    line-height: 60rpx;
                    margin-bottom: 10rpx;
                }
                .main {
                    display: flex;
                    flex: 1;
                    .l_box {
                        flex: 1;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: space-between;
                        .img_box {
                            border-radius: 50%;
                            overflow: hidden;
                            width: 160rpx;
                            height: 160rpx;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            background: #cdf1e7;
                            .img {
                                width: 110rpx;
                                height: 110rpx;
                            }
                        }
                    }
                    .item_box {
                        display: flex;
                        color: #232323;
                        height: 60rpx;
                        line-height: 60rpx;
                        font-size: 28rpx;
                        .l_text {
                            color: #6e6e6e;
                            width: 50%;
                            padding-left: 20rpx;
                        }
                        .r_text {
                            flex: 1;
                        }
                    }
                }
            }
        }
    }
    .album_content {
        height: 500rpx;
        border-radius: 16rpx;
        border: 4rpx solid #e7e9e8;
        margin-bottom: 40rpx;
        .album_box {
            padding: 20rpx;
            display: flex;
            flex-direction: column;
            .header_box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-bottom: 20rpx;
                .img {
                    width: 60rpx;
                    height: 60rpx;
                }
            }
            .img {
                width: 100%;
                height: 360rpx;
            }
        }
        .empty_box {
            height: 100%;
            display: flex;
            flex-direction: column;
            color: #acacae;
            align-items: center;
            justify-content: center;
            .img {
                width: 460rpx;
                height: 260rpx;
            }
            .text {
                display: flex;
            }
        }
    }
    .duty_content {
        border-radius: 16rpx;
        border: 4rpx solid #e7e9e8;
        display: flex;
        padding: 20rpx;
        .l_box {
            margin-right: 20rpx;
            .img {
                width: 88rpx;
                height: 88rpx;
                border-radius: 8rpx;
            }
        }
        .r_box {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            flex: 1;
            .top_text {
                margin-bottom: 20rpx;
            }
            .empty_msg {
                color: #8a8a8a;
            }
            .main_box {
                display: flex;
                flex-direction: column;
                overflow: hidden;
                flex: 1;
                border-left: 8rpx solid #00b781;
                color: #737e7a;
                padding: 10rpx;
                line-height: 60rpx;
                justify-content: space-around;
                margin-bottom: 20rpx;
                .msg {
                    white-space: nowrap; /* 防止文本换行 */
                    overflow: hidden; /* 超出部分隐藏 */
                    text-overflow: ellipsis;
                    width: 100%;
                    .text {
                        display: inline;
                        margin-right: 10rpx;
                    }
                }
            }
        }
    }
}
</style>

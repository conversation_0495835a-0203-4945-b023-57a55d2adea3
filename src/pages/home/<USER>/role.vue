<template>
    <yd-page-view ref="page" :clickLeft="openUserPage" :refresherOnly="true" @onRefresh="onRefresh" class="home-page" :title="schoolName" page-background="#0000" background-color="#0000" :hideBottom="false" color="#fff" :leftWidth="50" :rightWidth="50">
        <!--  -->
        <template #left>
            <view>
                <!--  #ifdef MP-WEIXIN -->
                <!-- <view class="scan_box" @click="openScanCode">
                    <image class="scan_img" mode="widthFix" src="@nginx/home/<USER>" />
                </view> -->
                <!-- <view class="user_box">
                	<image class="user_img" mode="widthFix" :src="avatar" />
            	</view> -->
                <!-- #endif -->
            </view>
        </template>
        <!--  #ifdef  H5 || H5-WEIXIN -->
        <!-- <template #right>
            <view class="scan_box" @click="openScanCode">
                <image class="scan_img" mode="widthFix" src="@nginx/home/<USER>" />
            </view>
        </template> -->
        <!-- #endif -->
        <!--  #ifdef APP-PLUS-->
        <!-- <template #right>
            <view class="scan_box" @click="openScanCode">
                <image class="scan_img" mode="widthFix" src="@nginx/home/<USER>" />
            </view>
        </template> -->
        <!-- #endif -->
        <!--  -->
        <view class="container">
            <!-- 快捷入口 -->
            <view class="quick_warp" v-if="quickList && quickList.length">
                <uni-swiper-dot class="swiper-dot-box" :current="state.current" mode="dot" :info="quickList" :dots-styles="dotsStyles">
                    <swiper class="swiper-box" @change="change" :current="state.current">
                        <swiper-item v-for="(item, index) in quickList" :key="index">
                            <view class="swiper-item" :class="'swiper-item' + index">
                                <!--  -->
                                <view class="item-app" v-for="(app, appIdx) in item" :key="appIdx" @click="openApp(app)">
                                    <view
                                        class="app-icon"
                                        :style="{
                                            background: `url(${app.logo}) center center / 100% 100% no-repeat`
                                        }"
                                    ></view>
                                    <text class="app-name">{{ app.name }}</text>
                                </view>
                                <!--  -->
                            </view>
                        </swiper-item>
                    </swiper>
                </uni-swiper-dot>
            </view>
            <!-- card -->
            <!-- 通知公告 -->
            <Notice />
            <!-- 考勤 -->
            <Attendance />
            <!-- 今日作业 -->
            <Homework v-if="roleCode != 'student'" />
            <!-- 课表 -->
            <Classschedulecard />
            <!-- 投票活动 -->
            <VoteCard />
            <!-- 图书馆 -->
            <LibraryCard />
            <!-- 添加组件 -->
            <!-- <view class="add_warp" @click="openComponentPage">
                <uni-icons class="add_icon" type="plusempty" size="13" color="#fff"></uni-icons>
                <text class="add_text">添加组件</text>
            </view> -->
            <!--  -->
        </view>
    </yd-page-view>
</template>

<script setup>
import VoteCard from "./voteCard.vue"
import Classschedulecard from "./classschedulecard.vue"
import Homework from "./homework.vue"
import Notice from "./notice.vue"
import Attendance from "./attendance.vue"
import LibraryCard from "./libraryCard.vue"

import useStore from "@/store"
import { useShowLoading } from "@/hooks"
import { beikeGroup } from "@/utils/index"

const { startLoading, stopLoading } = useShowLoading()
const { user, home } = useStore()

const state = reactive({
    current: 0
})

const roleCode = computed(() => user?.identityInfo?.roleCode) // 用户信息
const schoolName = computed(() => user?.userInfo?.schoolName)
const quickList = computed(() => {
    state.current = 0
    return home.quickList[0] && home.quickList[0].apps ? beikeGroup(home.quickList[0]?.apps, 4) : []
})

const page = ref(null)
const dotsStyles = {
    backgroundColor: "#DDDDDD",
    color: "#05B878",
    border: "none",
    selectedBackgroundColor: "#05B878",
    selectedBorder: "none",
    width: 5,
    height: 5
}

function change(e) {
    state.current = e.detail.current
}

function onRefresh() {
    startLoading("刷新中...")
    home.queryQuickList()
    setTimeout(() => {
        // 1.5秒之后停止刷新动画
        page.value.paging.complete([])
        stopLoading()
    }, 1500)
}

function openUserPage() {
    console.log("打开用户信息")
}

function openApp(app) {
    jumpApp(app)
}

function openComponentPage() {
    console.log("打开添加组件")
    uni.navigateTo({
        url: "/package/home/<USER>"
    })
}

function openScanCode() {
    scanCode()
}

function init() {
    home.queryQuickList()
}

onMounted(() => {
    init()
})
</script>

<style lang="scss" scoped>
.home-page {
    &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 800rpx;
        background: url("@nginx/home/<USER>") no-repeat;
        background-size: 100% 340rpx;
        z-index: -1;
    }

    .user_box {
        .user_img {
            width: 50rpx;
            height: 50rpx;
            display: block;
            border-radius: 50%;
            background-color: #eee;
        }
    }

    .scan_box {
        .scan_img {
            width: 44rpx;
            height: 44rpx;
            display: block;
        }
    }

    //
    .quick_warp {
        height: 180rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 14rpx 16rpx 0rpx #e3e3e380;
        border-radius: 10px;
        margin: 80rpx 30rpx 40rpx 30rpx;

        .swiper-dot-box,
        .swiper-box {
            height: 180rpx;
        }
        .swiper-item {
            height: 100%;
            display: flex;

            .item-app {
                width: 25%;
                display: flex;
                flex-direction: column;
                align-items: center;
                padding-top: 20rpx;
                .app-icon {
                    width: 72rpx;
                    height: 72rpx;
                }
                .app-name {
                    padding-top: 20rpx;
                    font-size: 28rpx;
                    color: $uni-text-color;
                }
            }
        }
    }
    //
    .add_warp {
        height: 120rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 14rpx 16rpx 0rpx #e3e3e380;
        border-radius: 20rpx;
        line-height: 120rpx;
        text-align: center;
        margin: 60rpx 30rpx 40rpx 30rpx;
        .add_icon {
            background-color: $uni-primary;
            border-radius: 50%;
            padding: 4rpx;
        }
        .add_text {
            font-weight: 500;
            font-size: 30rpx;
            color: $uni-primary;
            margin-left: 6rpx;
        }
    }
}
</style>

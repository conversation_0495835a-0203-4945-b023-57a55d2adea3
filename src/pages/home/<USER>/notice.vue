<template>
    <div class="notice_card">
        <text class="card_title">通知公告</text>
        <div class="notice_swiper">
            <swiper class="swiper" autoplay="3000" indicator-color="white">
                <swiper-item v-if="state.announcementList.length === 0">
                    <div class="swipe_box">
                        <div class="swipe_box_img">
                            <image class="img" mode="heightFix" src="@nginx/home/<USER>/notice_banner.png" />
                        </div>
                        <div class="swipe_box_titel" style="padding-left: 30rpx; text-align: left">暂无通知公告</div>
                    </div>
                </swiper-item>
                <swiper-item v-for="(item, index) in state.announcementList" :key="item.id + 'announcement' + index" @click="noticeFn(item)">
                    <div class="swipe_box">
                        <div class="swipe_box_img" v-if="item.coverImg">
                            <image class="img" mode="widthFix" :src="item.coverImg" />
                        </div>
                        <div class="swipe_box_text" v-else>
                            <div class="text_box">
                                <div class="title">{{ item.title }}</div>
                                <div class="division"></div>
                                <div class="content" v-html="item.content"></div>
                            </div>
                        </div>
                        <div class="swipe_box_titel" :style="!item.coverImg ? 'background: #f3fcf9' : ''">
                            {{ item.identityUserName + "发布于" + item.timerDate }}
                        </div>
                    </div>
                </swiper-item>
            </swiper>
        </div>
    </div>
</template>

<script setup>
const state = reactive({
    announcementList: []
})

function noticeFn(item) {
    console.log(item)

    navigateTo({
        url: "/apps/notice/announcement/details",
        query: {
            id: item.id,
            contentType: item.contentType,
            messType: item.messType,
            type: "receive"
        }
    })
}

// 通知公告轮播
function announcementFn() {
    let obj = {
        pageNo: 1,
        pageSize: 6,
        identifier: "announcement"
    }
    http.post("/cloud/mobile/mess/receive/pageIndex", obj).then((res) => {
        state.announcementList = res.data.list
        console.log(res)
    })
}

onMounted(() => {
    announcementFn()
})
</script>

<style lang="scss" scoped>
.notice_card {
    margin-bottom: 20rpx;
    .card_title {
        padding-left: 30rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: $uni-text-color;
    }
    .notice_swiper {
        height: 420rpx;
        width: 90%;
        margin: auto;
        border-radius: 20rpx 20rpx 20rpx 20rpx;
        overflow: hidden;
        background: $uni-bg-color;
        box-shadow: 0px 16rpx 16rpx 0px #dcf5ee80;
        margin-top: 20rpx;

        .swiper {
            height: 420rpx;
        }
        .swipe_box {
            max-height: 420rpx;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;

            .swipe_box_img {
                width: 100%;

                max-height: 348rpx;
                .img {
                    width: 100%;
                    max-height: 348rpx;
                }
            }

            .swipe_box_titel {
                flex: 1;
                height: 72rpx;
                width: 100%;
                border-radius: 0 0 10rpx 10rpx;
                padding-right: 20rpx;
                text-align: right;
                font-size: 24rpx;
                font-weight: 500;
                color: $uni-text-color;
                line-height: 72rpx;
            }

            .swipe_box_text {
                height: 348rpx;
                width: 100%;
                background: url("@nginx/home/<USER>/notice_banner_text.png");
                background-size: cover;
                position: relative;
                overflow: hidden;

                .text_box {
                    height: 348rpx;
                    width: 95%;
                    padding: 20rpx;

                    .title {
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                        font-size: 30rpx;
                        font-weight: 500;
                        color: $uni-text-color;
                        margin: 0rpx;
                    }

                    .division {
                        width: 95%;
                        height: 2rpx;
                        margin: 20rpx 0rpx 0rpx 0rpx;
                        background: url("@nginx/home/<USER>/division.png");
                    }

                    .content {
                        font-size: 28rpx;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 4;
                        line-clamp: 4;
                        -webkit-box-orient: vertical;
                    }

                    :deep(.content img) {
                        display: none !important;
                    }
                }
            }
        }
    }
}
</style>

<template>
    <div class="attendance_homecard">
        <text class="card_title">考勤</text>
        <div class="attendance_banner" @click="goAttendance">
            <!-- 学生端 -->
            <swiper v-if="identityType !== 'teacher'" class="attendance_swipe" autoplay="3000" indicator-color="#ffffff52" indicator-active-color="#fff" :indicator-dots="true">
                <swiper-item v-for="(item, index) in state.attendanceList" :key="item.id + 'attendance' + index">
                    <view class="swipe_attendance_box">
                        <view class="swipe_attendance_title">
                            考勤统计 <text v-if="item.name">{{ "-" + item.name }}</text>
                        </view>
                        <view class="attendance_statistics">
                            <view class="division"></view>
                            <view class="attendance_statistics_title">
                                <text class="title frequency">正常次数</text>
                                <view class="title abnormal">异常次数</view>
                            </view>
                            <view class="attendance_statistics">
                                <view class="attendance_statistics_title statistics_num" v-for="(numItem, numIndex) in item.countList" :key="numIndex + 'countList'">
                                    <text class="frequency">
                                        {{ numItem.normalNum }}
                                    </text>
                                    <view class="abnormal">
                                        {{ numItem.errorNum }}
                                        <view class="name">
                                            {{ ["出入校", "事件考勤", "课程考勤", "场地课表考勤"][numItem.attendanceType] }}
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
            <!-- 老师端 -->
            <swiper v-else class="attendance_swipe" :autoplay="false" indicator-color="#ffffff52" indicator-active-color="#fff" :indicator-dots="true">
                <!-- 教师端 —— 学生 -->
                <swiper-item>
                    <view class="swipe_attendance_box">
                        <view class="swipe_attendance_title">
                            <text>考勤统计 - 学生</text>
                        </view>
                        <view class="attendance_proportion">
                            <view class="attendance_proportion_item" v-for="(item, index) in state.studentAttendance" :key="item.id + 'studentAttendance' + index" :style="index === 0 ? 'border: none' : ''">
                                <text class="num">{{ item.normalRate || "0.00%" }}</text>
                                <text class="name">{{ ["出入校", "事件考勤", "课程考勤", "场地课表考勤"][item.attendanceType] || "考勤" }}</text>
                            </view>
                        </view>
                    </view>
                </swiper-item>
                <!-- 教师端 —— 老师 -->
                <swiper-item>
                    <view class="swipe_attendance_box">
                        <view class="swipe_attendance_title">
                            <text>考勤统计 - 教师</text>
                        </view>
                        <view class="attendance_proportion">
                            <view class="attendance_proportion_item" v-for="(item, index) in state.teacherAttendance" :key="item.id + 'teacherAttendance' + index" :style="index === 0 ? 'border: none' : ''">
                                <text class="num">{{ item.normalRate || "0.00%" }}</text>
                                <text class="name">{{ ["", "事件考勤", "课程考勤"][item.attendanceType] || "考勤" }}</text>
                            </view>
                        </view>
                    </view>
                </swiper-item>
            </swiper>
        </div>
    </div>
</template>

<script setup>
import dayjs from "dayjs"
import useStore from "@/store"
const { user } = useStore()

const state = reactive({
    attendanceList: [],
    studentAttendance: [],
    teacherAttendance: [],
    arr: []
})

const identityType = computed(() => {
    const roleCode = user?.identityInfo?.roleCode
    if (roleCode == "eltern") {
        return "eltern"
    } else if (roleCode == "student") {
        return "student"
    } else {
        return "teacher"
    }
})

function diff(arr1, arr2) {
    var newArr = []
    var checked = []
    var found = false
    for (var j = 0; j < arr1.length; j++) {
        for (var i = 0; i < arr2.length && !found; i++) {
            //如果已经检查过是相同就跳过
            if (!checked.includes(i)) {
                //如果相同就记住下次不查
                if (arr2[i] === arr1[j]) {
                    checked.push(arr2.indexOf(arr2[i]))
                    found = true //已经找到相同
                }
            }
        }
        if (!found) {
            newArr.push(arr1[j])
        } else {
            found = false
        }
    }
    //检查arr1中有arr2中没有的
    for (var k = 0; k < arr2.length; k++) {
        if (!checked.includes(k)) {
            newArr.push(arr2[k])
        }
    }
    return newArr
}

// 考勤卡片数据
async function getList() {
    // 获取今天的日期
    const day = dayjs().format("YYYY-MM-DD")
    const nowTime = {
        startDate: day,
        endDate: day
    }
    //	type: 1  事件1 课堂2
    //	userType: 1 学生1 老师2
    //  state.info.identity 1为教师 2为学生

    if (identityType.value == "teacher") {
        // 学生
        await http
            .post("/attweb/app/eventStatistical/teacherViewHome", {
                ...nowTime,
                userType: 1 // 学生1 老师2
            })
            .then((res) => {
                let { data } = res
                if (data.length < 3) {
                    const arr = data.map((i) => i.attendanceType)
                    const isbuqi = diff(arr, [0, 1, 2, 3])
                    const list = isbuqi.map((attendanceType) => {
                        return {
                            attendanceType,
                            normalRate: "0.00%"
                        }
                    })

                    data = [...list, ...data]
                }
                //  item.attendanceType === 3 场地课表考勤
                const attendanceList = data.filter((item) => {
                    if (item.attendanceType !== 3) {
                        return item
                    }
                })
                state.studentAttendance = attendanceList
            })
        // 老师
        await http
            .post("/attweb/app/eventStatistical/teacherViewHome", {
                ...nowTime,
                userType: 2, // 学生1 老师2
                type: 1
            })
            .then((res) => {
                let data = res.data
                if (res.data.length < 1) {
                    const arr = res.data.map((i) => i.attendanceType)
                    const isbuqi = diff(arr, [1])
                    const list = isbuqi.map((attendanceType) => {
                        return {
                            attendanceType,
                            normalRate: "0.00%"
                        }
                    })
                    data = [...data, ...list]
                }
                state.arr = data
            })
            .catch(() => {
                state.arr = [
                    {
                        attendanceType: 1,
                        normalRate: "0.00%"
                    }
                ]
            })
        await http
            .post("/attweb/app/eventStatistical/teacherViewHome", {
                ...nowTime,
                userType: 2, // 学生1 老师2
                type: 2
            })
            .then((res) => {
                let data = res.data
                if (res.data.length < 1) {
                    const arr = res.data.map((i) => i.attendanceType)
                    const isbuqi = diff(arr, [2])
                    const list = isbuqi.map((attendanceType) => {
                        return {
                            attendanceType,
                            normalRate: "0.00%"
                        }
                    })
                    data = [...data, ...list]
                }
                state.teacherAttendance = [...state.arr, ...data]
            })
            .catch(() => {
                state.teacherAttendance = [
                    {
                        attendanceType: 2,
                        normalRate: "0.00%"
                    }
                ]
            })
    } else {
        http.post("/attweb/app/eventStatistical/studentView", { ...nowTime }).then((res) => {
            res.data = res.data && res.data.length > 0 ? res.data : [{ countList: [] }]
            state.attendanceList = res.data.map((item) => {
                let data = item.countList
                if (item.countList) {
                    if (item.countList.length < 3) {
                        const arr = item.countList.map((i) => i.attendanceType)
                        const isbuqi = diff(arr, [0, 1, 2])
                        const list = isbuqi.map((attendanceType) => {
                            return {
                                attendanceType,
                                normalNum: 0,
                                errorNum: 0
                            }
                        })
                        data = [...data, ...list]
                    }
                    item.countList = data
                } else {
                    const isbuqi = diff([], [0, 1, 2])
                    const list = isbuqi.map((attendanceType) => {
                        return {
                            attendanceType,
                            normalNum: 0,
                            errorNum: 0
                        }
                    })
                    item.countList = [...list]
                }
                return item
            })
        })
    }
}

function goAttendance() {
    navigateTo({
        url: "/apps/studentAttendance/index",
        query: {
            code: "studentAttendance",
            routePath: "studentAttendance"
        }
    })
}

onMounted(() => {
    getList()
})
</script>

<style lang="scss" scoped>
.attendance_homecard {
    padding: 20rpx 0rpx;
    background: $uni-bg-color-grey;

    .card_title {
        padding-left: 30rpx;
        font-weight: 500;
        font-size: 30rpx;
        color: $uni-text-color;
    }
}
.attendance_banner {
    margin-top: 20rpx;
    height: 296rpx;
    width: 100%;

    .swipe_attendance_box {
        height: 240rpx;
        width: calc(100% - 96rpx);
        // margin: 0 auto;
        background: url("@nginx/home/<USER>/attendance_banner.png") no-repeat;
        margin: 0rpx 24rpx;
        padding: 30rpx 24rpx;
        background-size: contain;

        .swipe_attendance_title {
            font-size: 30rpx;
            color: $uni-text-color-inverse;
            line-height: 36rpx;
            margin: 0rpx;
            font-weight: 400;
        }

        .attendance_proportion {
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 200rpx;
            margin-top: 10rpx;

            .attendance_proportion_item {
                display: flex;
                justify-content: space-around;
                align-items: center;
                flex-direction: column;
                color: $uni-text-color-inverse;
                border-left: 1px #ffffff8c solid;
                width: 50%;

                .num {
                    margin: 0rpx 0rpx 18rpx 0rpx;
                    font-size: 34rpx;
                    font-weight: 500;
                    line-height: 36rpx;
                }

                .name {
                    font-size: 24rpx;
                    font-weight: 400;
                    line-height: 26rpx;
                }
            }
        }

        .attendance_statistics {
            position: relative;
            margin-top: 10rpx;
            width: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 80%;
            font-size: 24rpx;

            font-weight: 400;
            color: $uni-text-color-inverse;
            line-height: 26rpx;

            .division {
                position: absolute;
                top: 86rpx;
                left: 4rpx;
                width: 96%;
                height: 1rpx;
                background: #ffffff80;
                border-radius: 7rpx;
            }

            .attendance_statistics_title {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                min-width: 100rpx;
                height: 100%;

                .frequency {
                    height: 40rpx;
                    width: 100rpx;
                    margin: 0rpx 0rpx 20rpx 0rpx;
                    text-align: center;
                }

                .title {
                    border: none;
                    padding-right: 0rpx;
                    width: 100rpx;
                    margin: 0rpx 0rpx 20rpx 0rpx;
                }

                .abnormal {
                    text-align: center;
                    margin-top: 8rpx;
                }

                .name {
                    font-size: 24rpx;
                    border: none;
                    text-align: center;
                    margin-top: 14rpx;
                }
            }

            .statistics_num {
                font-size: 36rpx;
            }
        }
    }
}
:deep(.uni-swiper-dot) {
    width: 12rpx !important;
    height: 12rpx !important;
}
</style>

<template>
    <div class="vote_homecard" v-if="voteList && voteList.length > 0">
        <text class="card_title">投票</text>
        <swiper class="swiper" autoplay="3000" indicator-color="green">
            <swiper-item v-for="(item, index) in voteList" :key="index">
                <div class="vote_item">
                    <div class="item_image">
                        <image class="url_img" mode="aspectFill" v-if="item.url && item.url != ''" :src="item.url" alt="" />
                        <image class="url_img" mode="aspectFill" v-else src="@nginx/home/<USER>/no_url_vote.png" alt="" />
                    </div>
                    <div class="item_info">
                        <div class="item_info_title">
                            <span class="title_text">{{ item.title }}</span>
                        </div>
                        <div class="item_countdown">
                            <span class="title">
                                <div class="class_schedule_icon"></div>
                                距离结束还剩：</span
                            >
                            <uni-countdown :font-size="14" :day="item.voteDataInfo.day" :hour="item.voteDataInfo.hour" :minute="item.voteDataInfo.min" :second="item.voteDataInfo.sec" color="#FFFFFF" background-color="#333" />
                        </div>
                    </div>
                </div>
            </swiper-item>
        </swiper>
    </div>
</template>

<script setup>
const voteList = ref([])

onMounted(() => {
    let params = {
        status: 1, // 状态 0：未开始 1：进行中 2：已结束 3：已暂停
        pageNo: 1,
        pageSize: 3
    }
    http.post("/cloud/vote/page", params).then((res) => {
        voteList.value = res.data?.list || []
    })
})
</script>

<style lang="scss" scoped>
.vote_homecard {
    padding: 20rpx 30rpx;
    background: $uni-bg-color-grey;

    .card_title {
        font-weight: 500;
        font-size: 30rpx;
        color: $uni-text-color;
    }
    .swiper {
        height: 420rpx;
        margin-top: 20rpx;
    }

    .vote_item {
        width: 690rpx;
        height: 428rpx;
        background: $uni-bg-color;
        border-radius: 20rpx;
        display: flex;
        flex-direction: column;
        margin-bottom: 30rpx;

        .item_image {
            width: 100%;
            height: 280rpx;

            .url_img {
                width: 100%;
                height: 100%;
            }
        }

        .item_info {
            display: flex;
            flex-direction: column;
            padding: 20rpx 30rpx;

            .item_info_title {
                display: flex;
                align-items: center;
                justify-content: flex-start;

                .info_tag {
                    width: 80rpx;
                    height: 36rpx;
                    line-height: 36rpx;
                    background: $uni-color-primary;
                    border-radius: 8rpx;
                    font-size: 20rpx;
                    font-weight: 500;
                    color: $uni-text-color-inverse;
                    text-align: center;
                    padding: 4rpx;
                    margin-right: 10rpx;
                }

                .title_text {
                    flex: 1;
                    font-size: 30rpx;
                    font-weight: 600;
                    color: $uni-text-color;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                }
            }

            .item_countdown {
                margin-top: 20rpx;
                display: flex;
                align-items: center;

                .title {
                    display: flex;
                    align-items: center;
                    font-size: 26rpx;
                    font-weight: 400;
                    color: $uni-text-color-grey;
                    line-height: 36rpx;

                    .class_schedule_icon {
                        width: 36rpx;
                        height: 36rpx;
                        background: url("@nginx/home/<USER>/class_schedule_icon.png") no-repeat;
                    }
                }

                .colon {
                    font-size: 26rpx;

                    font-weight: 400;
                    color: $uni-text-color;
                    line-height: 36rpx;
                    padding: 0rpx 10rpx;
                }

                .block {
                    background: $uni-text-color;
                    border-radius: 4rpx;
                    font-size: 26rpx;

                    font-weight: 600;
                    color: $uni-text-color-inverse;
                    padding: 5rpx 10rpx;
                }
            }
        }
    }
}
</style>

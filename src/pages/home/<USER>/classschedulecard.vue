<!--
 * @Descripttion:
 * @version: 1.0.0
 * @Author: jingrou
 * @Date: 2022-08-24 17:51:58
 * @LastEditors: jingrou
 * @LastEditTime: 2022-09-03 18:44:28
-->
<template>
    <div class="class_schedule">
        <text class="card_title">课表</text>
        <div class="class_schedule_card" @click="gotoSchoolTable">
            <div class="class_schedule_label" v-if="state.homeTime.subjectName">下节课</div>
            <div class="class_schedule_title">{{ state.homeTime.subjectName || "暂无科目" }}</div>
            <div class="class_schedule_text">
                <span class="text">{{ state.homeTime.showName || "暂无班级" }}</span>
                <span class="text">|</span>
                <span class="text">{{ state.homeTime.startTime || "00:00" }}-{{ state.homeTime.endTime || "24:00" }}</span>
            </div>
            <div class="class_schedule_time">
                <div class="class_schedule_icon"></div>
                <span>距离上课时间还有： {{ state.time }}</span>
            </div>
        </div>
    </div>
</template>
<script setup>
import useStore from "@/store"
const state = reactive({
    time: "",
    homeTime: {}
})
const { user } = useStore()
const roleCode = computed(() => user?.identityInfo?.roleCode) // 用户信息

const showtime = (time) => {
    if (time === undefined) {
        state.time = "00小时 00 分钟 00 秒"
    } else {
        const nowtime = new Date(), //获取当前时间
            endtime = Date.parse(time.replace(/-/g, "/")) //定义结束时间
        const lefttime = endtime - nowtime.getTime(), //距离结束时间的毫秒数
            leftd = Math.floor(lefttime / (1000 * 60 * 60 * 24)), //计算天数
            lefth = Math.floor((lefttime / (1000 * 60 * 60)) % 24), //计算小时数
            leftm = Math.floor((lefttime / (1000 * 60)) % 60), //计算分钟数
            lefts = Math.floor((lefttime / 1000) % 60) //计算秒数
        switch (leftd) {
            case 0:
                state.time = lefth + "小时" + leftm + "分钟" + lefts + "秒"
                break
            default:
                state.time = leftd + "天" + lefth + "小时" + leftm + "分钟" + lefts + "秒"
                break
        }
    }
    // return leftd + "天" + lefth + ":" + leftm + ":" + lefts;  //返回倒计时的字符串
}

function gotoSchoolTable() {
    navigateTo({ url: "/apps/schoolTable/index", query: { isHome: true } })
}

onMounted(() => {
    http.get("/app/timetable/v1/getHomeTimetable").then((res) => {
        state.homeTime = res.data
        if (state.homeTime.startDate !== "" && state.homeTime.startDate !== undefined) {
            setInterval(() => {
                showtime(state.homeTime.startDate), 1000
            })
        } else {
            state.time = "00小时 00 分钟 00 秒"
        }
    })
})
</script>

<style scoped lang="scss">
.class_schedule {
    padding: 20rpx 30rpx;
    background: $uni-bg-color-grey;

    .card_title {
        padding-left: 4rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: $uni-text-color;
    }

    .class_schedule_card {
        .noData {
            text-align: center;
            line-height: 240rpx;
            color: #ccc;
        }

        font-size: 30rpx;
        font-weight: 500;
        color: $uni-text-color;
        margin-top: 20rpx;
        width: 100%;
        height: 218rpx;
        background: $uni-bg-color;
        box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(220, 245, 238, 0.5);
        border-radius: 20rpx;
        position: relative;

        .class_schedule_label {
            width: 112rpx;
            position: absolute;
            top: 30rpx;
            right: 0rpx;
            height: 42rpx;
            border-radius: 40rpx 0rpx 0rpx 40rpx;
            font-size: 24rpx;
            font-weight: 600;
            color: $uni-text-color-inverse;
            background: $uni-color-warning;
            text-align: center;
            line-height: 42rpx;
        }

        .class_schedule_title {
            padding: 30rpx 0rpx 0rpx 30rpx;
        }

        .class_schedule_text {
            font-size: 28rpx;
            padding: 20rpx 0rpx 0rpx 30rpx;

            .text {
                padding-right: 20rpx;
            }
        }

        .class_schedule_time {
            padding: 20rpx 0rpx 0rpx 30rpx;
            display: flex;
            align-items: center;

            .class_schedule_icon {
                width: 36rpx;
                height: 36rpx;
                background: url("@nginx/home/<USER>/class_schedule_icon.png") no-repeat;
            }
        }
    }
}
</style>

<template>
    <view class="demo-page">
        <view class="demo-header">
            <text class="demo-title">倒计时组件演示</text>
        </view>
        
        <view class="demo-section">
            <view class="section-title">
                <text>完整显示（天时分秒）</text>
            </view>
            <view class="demo-item">
                <text class="demo-label">距离2025年7月1日还剩：</text>
                <Countdown 
                    :end-time="'2025-07-01 00:00:00'"
                    :show-day="true"
                    :show-hour="true"
                    :show-minute="true"
                    :show-second="true"
                    number-color="#333"
                    label-color="#666"
                    @timeup="onTimeUp1"
                />
            </view>
        </view>
        
        <view class="demo-section">
            <view class="section-title">
                <text>只显示时分秒</text>
            </view>
            <view class="demo-item">
                <text class="demo-label">距离今天23:59:59还剩：</text>
                <Countdown 
                    :end-time="todayEndTime"
                    :show-day="false"
                    :show-hour="true"
                    :show-minute="true"
                    :show-second="true"
                    number-color="#07c160"
                    label-color="#999"
                    @timeup="onTimeUp2"
                />
            </view>
        </view>
        
        <view class="demo-section">
            <view class="section-title">
                <text>只显示分秒</text>
            </view>
            <view class="demo-item">
                <text class="demo-label">距离5分钟后还剩：</text>
                <Countdown 
                    :end-time="fiveMinutesLater"
                    :show-day="false"
                    :show-hour="false"
                    :show-minute="true"
                    :show-second="true"
                    number-color="#ff6b6b"
                    label-color="#999"
                    number-size="32rpx"
                    label-size="28rpx"
                    @timeup="onTimeUp3"
                />
            </view>
        </view>
        
        <view class="demo-section">
            <view class="section-title">
                <text>使用时间戳</text>
            </view>
            <view class="demo-item">
                <text class="demo-label">距离时间戳结束还剩：</text>
                <Countdown 
                    :end-time="timestampEndTime"
                    :show-day="true"
                    :show-hour="true"
                    :show-minute="true"
                    :show-second="true"
                    number-color="#4dabf7"
                    label-color="#868e96"
                    @timeup="onTimeUp4"
                />
            </view>
        </view>
        
        <view class="demo-section">
            <view class="section-title">
                <text>自定义样式</text>
            </view>
            <view class="demo-item">
                <text class="demo-label">大号字体倒计时：</text>
                <Countdown 
                    :end-time="customEndTime"
                    :show-day="true"
                    :show-hour="true"
                    :show-minute="true"
                    :show-second="true"
                    number-color="#e03131"
                    label-color="#495057"
                    number-size="40rpx"
                    label-size="32rpx"
                    @timeup="onTimeUp5"
                />
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import Countdown from '../components/Countdown.vue'

// 今天结束时间
const todayEndTime = computed(() => {
    return dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
})

// 5分钟后的时间
const fiveMinutesLater = ref('')

// 时间戳格式的结束时间（30分钟后）
const timestampEndTime = ref(0)

// 自定义结束时间（2小时后）
const customEndTime = ref('')

onMounted(() => {
    // 设置5分钟后的时间
    fiveMinutesLater.value = dayjs().add(5, 'minute').format('YYYY-MM-DD HH:mm:ss')
    
    // 设置30分钟后的时间戳
    timestampEndTime.value = dayjs().add(30, 'minute').valueOf()
    
    // 设置2小时后的时间
    customEndTime.value = dayjs().add(2, 'hour').format('YYYY-MM-DD HH:mm:ss')
})

// 倒计时结束回调函数
const onTimeUp1 = () => {
    uni.showToast({
        title: '2025年到了！',
        icon: 'success'
    })
}

const onTimeUp2 = () => {
    uni.showToast({
        title: '今天结束了！',
        icon: 'success'
    })
}

const onTimeUp3 = () => {
    uni.showToast({
        title: '5分钟到了！',
        icon: 'success'
    })
}

const onTimeUp4 = () => {
    uni.showToast({
        title: '时间戳倒计时结束！',
        icon: 'success'
    })
}

const onTimeUp5 = () => {
    uni.showToast({
        title: '自定义倒计时结束！',
        icon: 'success'
    })
}
</script>

<style lang="scss" scoped>
.demo-page {
    padding: 40rpx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.demo-header {
    text-align: center;
    margin-bottom: 60rpx;
    
    .demo-title {
        font-size: 48rpx;
        font-weight: bold;
        color: #333;
    }
}

.demo-section {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    
    .section-title {
        margin-bottom: 30rpx;
        
        text {
            font-size: 36rpx;
            font-weight: 600;
            color: #333;
        }
    }
    
    .demo-item {
        display: flex;
        flex-direction: column;
        gap: 20rpx;
        
        .demo-label {
            font-size: 28rpx;
            color: #666;
        }
    }
}
</style>

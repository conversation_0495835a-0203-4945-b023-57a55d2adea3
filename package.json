{"name": "cloud-mobile", "version": "1.0.0", "description": "云平台移动端应用", "scripts": {"dev:h5": "uni", "dev:mp-weixin": "uni -p mp-weixin", "build:h5": "uni build --mode uat", "build:h5:uatrelease": "uni build --mode uatrelease", "build:h5:production": "uni build --mode production", "build:mp-weixin": "uni build -p mp-weixin --mode uat", "build:mp-weixin:uatrelease": "uni build -p mp-weixin --mode uatrelease", "build:mp-weixin:production": "uni build -p mp-weixin --mode production", "build:app-android": "uni build -p app-android --mode uat", "build:app-android:uatrelease": "uni build -p app-android --mode uatrelease", "build:app-android:production": "uni build -p app-android --mode production", "dev:docs": "vitepress dev docs --port 8000", "build:docs": "vitepress build docs", "preview:docs": "vitepress preview docs", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:app-harmony": "uni -p app-harmony", "dev:custom": "uni -p", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-ios": "uni build -p app-ios", "build:app-harmony": "uni build -p app-harmony", "build:custom": "uni build -p", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "lint": "eslint src", "lint:fix": "eslint src --fix", "lint:css": "stylelint --fix \"src/**/*.{css,scss,vue}\""}, "uni-app": {"scripts": {"mp-weixin": {"title": "微信小程序", "env": {"UNI_PLATFORM": "mp-weixin"}, "define": {"MP-WEIXIN": true}}, "h5-weixin": {"title": "公众号", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5-WEIXIN": true}}, "h5": {"title": "浏览器H5", "env": {"UNI_PLATFORM": "h5"}, "define": {"H5": true}}, "app-android": {"title": "安卓APP", "env": {"UNI_PLATFORM": "app-plus"}, "define": {"APP-PLUS": true}}}}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4050720250324001", "@dcloudio/uni-app-harmony": "3.0.0-4050720250324001", "@dcloudio/uni-app-plus": "3.0.0-4050720250324001", "@dcloudio/uni-components": "3.0.0-4050720250324001", "@dcloudio/uni-h5": "3.0.0-4050720250324001", "@dcloudio/uni-mp-alipay": "3.0.0-4050720250324001", "@dcloudio/uni-mp-baidu": "3.0.0-4050720250324001", "@dcloudio/uni-mp-harmony": "3.0.0-4050720250324001", "@dcloudio/uni-mp-jd": "3.0.0-4050720250324001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4050720250324001", "@dcloudio/uni-mp-lark": "3.0.0-4050720250324001", "@dcloudio/uni-mp-qq": "3.0.0-4050720250324001", "@dcloudio/uni-mp-toutiao": "3.0.0-4050720250324001", "@dcloudio/uni-mp-weixin": "3.0.0-4050720250324001", "@dcloudio/uni-mp-xhs": "3.0.0-4050720250324001", "@dcloudio/uni-quickapp-webview": "3.0.0-4050720250324001", "@dcloudio/uni-ui": "^1.5.7", "@tinymce/tinymce-vue": "^6.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "clipboard": "^2.0.11", "cropperjs": "^1.6.2", "dayjs": "^1.11.13", "deep-pick-omit": "^1.2.1", "destr": "^2.0.3", "dingtalk-jsapi": "^3.0.42", "eventsource-parser": "^3.0.1", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "marked": "^15.0.9", "marked-highlight": "^2.2.1", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.3.0", "promise-polyfill": "^8.3.0", "vue": "^3.4.21", "vue-signature-pad": "^3.0.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4050720250324001", "@dcloudio/uni-cli-shared": "3.0.0-4050720250324001", "@dcloudio/uni-stacktracey": "3.0.0-4050720250324001", "@dcloudio/vite-plugin-uni": "3.0.0-4050720250324001", "@eslint/js": "^9.11.1", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.9.0", "@vue/eslint-config-standard": "^8.0.1", "@vue/runtime-core": "^3.4.21", "dingtalk-h5-remote-debug": "^0.1.3", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.1.0", "prettier-eslint": "^16.1.2", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.25.0", "sass-loader": "8.0.1", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-scss": "1.0.0-security", "stylelint-config-standard": "^36.0.1", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.2", "stylelint-scss": "^6.7.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vconsole": "^3.15.1", "vite": "5.2.8", "vite-plugin-html": "^3.2.2", "vitepress": "^1.3.4", "vue-eslint-parser": "^8.0.0"}}
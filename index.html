<!doctype html>
<html lang="ch">

<head>
	<meta charset="UTF-8" />
	<meta name="version" content="<%=version%>" />
	<script>
		var coverSupport = "CSS" in window && typeof CSS.supports === "function" && (CSS.supports("top: env(a)") || CSS.supports("top: constant(a)"))
		document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ", viewport-fit=cover" : "") + '" />')
	</script>
	<link rel="stylesheet" href="https://file.1d1j.cn/cloud-mobile/iconfont/font_3184698_ljh90qv37d/iconfont.css">
	<title></title>

	<!-- 引入 css -->
	<link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
	
	<!-- highlight.js  -->	
	<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/styles/default.min.css" as="style" onload="this.rel='stylesheet'">	
	<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.5.1/highlight.min.js" async></script>
	<!-- katex -->	
	<link rel="preload" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.css" as="style" onload="this.rel='stylesheet'">	
	<script src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/contrib/auto-render.min.js" async></script>
	<!--preload-links-->
	<!--app-context-->
</head>

<body>
	<div id="app"><!--app-html--></div>
	<!-- 滑块验证 -->
	<script src="https://turing.captcha.qcloud.com/TCaptcha.js"></script>
	<script src='https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js'></script>
	<script type="module" src="/src/main.js"></script>
</body>

</html>
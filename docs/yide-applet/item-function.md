# 项目分包功能模块
## 1. 巡逻巡查 patrol
+ 文件地址：src/patrol/inspection，包含了巡逻和巡课两个，传入的code不同，功能不同。
+ 提供给一加壹APP和公众号使用，其中调用一加壹APP中扫码功能，使用sendAppEvent("getQrResult", {})方法，打开扫码，获取到结果后，一加壹APP处理重新跳回uniapp其他页面，公众号调用扫码功能，基于公众号使用Iframe嵌套了页面，使用window.parent.postMessage传回公众号调用微信端的扫一扫

## 2. 邀请加入页面 invitation
+ 文件地址：src/invitation/index/index  只提供给一加壹App使用的

## 3. 个人信息录入 informationentry
+ 文件地址：src/informationentry/index/index  只提供给一加壹App使用的，在个人信息中有个信息补充可以录入个人信息

## 4. 学生考勤 attendance
+ 文件地址：src/attendance/classTeacher/index/index 只提供给一加壹App使用的

## 5. 我的学生 myStudent
+ 文件地址：src/myStudent/classTeacher/index/index 只提供给一加壹App使用的,在任课老师的角色中可以使用

## 6. 班级德育 moralEducation
+ 文件地址：src/moralEducation/ranking/index 给一加壹APP和公众号都有使用，可以对学生的每日进行评分，也可查看得分排名

## 7. 宿舍 dormManage
+ 文件地址：src/dormManage/home/<USER>

## 8. 访客系统 visitorSystem
+ 文件地址：src/visitorSystem/index  给一加壹App使用的，可以发起申请，审批，邀请等功能

## 9. 成绩管理 scoreManage
+ 文件地址：src/scoreManage/index 在一加壹APP和公众号中都有使用，用于管理学生的成绩，查看成绩表等

## 10. 活动报名 registration
+ 文件地址：src/registration/index 在一加壹APP和公众号中都有使用

## 11. 教师考勤 teacherAttendance
+ 文件地址：src/teacherAttendance/punchIn/index 在公众号，本地部署中使用，可以给老师进行考勤打卡，定位打卡，由于高德地图的API没有购买套餐，所以定位获取经纬度功能无效，本地部署写死紫琅一小的经纬度，云平台也一样

## 12. 社团管理 groupManage
+ 文件地址：src/groupManage/index/index 只有大学生才可看的社团管理，在公众号使用，可以新增编辑自己的社团，邀请社团为一加壹APP会跳转到这边进行


## 13. 打卡（我的任务） task
+ 文件地址：src/task/teacher/list，老师端跳转可以新增任务，学生端跳转只能打卡，家长端多个孩子可以切换自己的小孩，根据学生ID去打卡


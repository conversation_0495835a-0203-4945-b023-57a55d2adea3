
# 项目公共函数/配置
## 1. 获取静态文件地址 getStaticFile

+ 文件地址：/src/utils/common.js，函数：getStaticFile(fileName)，fileName传入你的静态资源地址

+ 使用了env配置的链接VITE_BASE_STATIC

## 2. 复制链接 copyUrl

+ 文件地址：/src/utils/common.js，使用uniapp的setClipboardData方法，直接在copyUrl(url) 传入链接即可

## 3. uniapp路由跳转方法 
+ 文件地址：/src/utils/common.js，函数：navigateTo({ url, query, ...option })

+ 使用了serializeToQueryString方法，可以拼接query中的参数，并且判断了路由是否已经跳转超过10个页面，如果超过10个页面，清空前面跳转了的页面

## 4. 防抖
+ 文件地址：/src/utils/common.js，函数：debounce(func, wait)

## 5. 数字转成汉字
+ 文件地址：/src/utils/getDate.js，函数：toChinesNum(num)

+ 可以把数字‘1234567890’转为零', '一', '二', '三', '四', '五', '六', '七', '八', '九'，'十', '百', '千', '万'

## 6. 判断当前手机的机型和浏览器
+ 文件地址：/src/utils/sendAppEvent.js，函数：checkPlatform()和sendAppEvent()

+ checkPlatform：根据navigator.userAgent获取字符串，判断MicroMessenger是否在微信浏览器中，判断yide-ios-app在IOS一加壹App中，判断yide-android-app在Android一加壹App中，注意安卓老版本使用的是Android值判断，IOS老版本使用的是IOS值判断

+ sendAppEvent： 如果根据checkPlatform判断在一加壹APP浏览器中，可以使用IOS和Android给的方法，传入一个和IOS和Android约定好的方法名，和一个参数即可

## 7. 设置获取缓存方法
+ 文件地址：/src/utils/Storage.js，函数：get(key)，set(key, value)，remove(key)，clear()移除全部永久缓存

+ 使用uniapp中的getStorageSync方法，setStorageSync方法，removeStorageSync方法和clearStorageSync方法，例子：Storage.get('token')



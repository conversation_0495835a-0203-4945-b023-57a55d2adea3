
# 项目公共函数/配置
## 1. 获取当前环境 checkPlatform
+ 文件地址：/src/utils/util.js，函数：checkPlatform()

+ 钉钉环境：按需引入dingtalk-jsapi包，根据钉钉官方API给的判断 dd.env.platform !== **"notInDingTalk"**  判断是否在钉钉浏览器中

+ 企业微信环境：根据navigator.userAgent判断是否有 **“wxwork”** 字符串

+ 微信环境：和企业微信不同，根据navigator.userAgent判断是否有 **“MicroMessenger”** 字符串

+ IOS一加壹App环境：原生IOS会在navigator.userAgent中添加一个 **“yide-android-app”** 给我们判断，根据这个值可以判断在IOS手机的一加壹App中（注意：app老版本中，app给的字段值为 **‘IOS’** ）

+ Android一加壹App环境：原生Android会在navigator.userAgent中添加一个 **“yide-android-app”** 给我们判断，根据这个值可以判断在Android手机的一加壹App中（注意：app老版本中，app给的字段值为 **‘Android’** ）

## 2. 登录时的RSA加密
+ 文件地址：/src/utils/rsa.js，函数：加密encrypt(), 解密decrypt()

+ 需要下载 **encryptlong** 包，需要存储加密公钥密钥串，如果是对象/数组的话，需要先JSON.stringify转换成字符串

## 3. 设置压缩加密缓存
+ 文件地址：/src/utils/index.js  函数：const { getItem,setItem,removeItem,removeAll } = useStorage()

+ 需要下载 **secure-ls** 包，获取缓存：getItem，设置缓存setItem，删除单个缓存removeItem，删除所有缓存removeAll

## 4. iconfont配置
+ 在index.html中引入，有一些包下载了在本地的/public文件中，有一些用的是在线链接引用

## 5. vant引用
+ 在main.js中引入**vant** 包，使用的是按需引入，所以有时候引入了组件不生效，需要在main.js中注册使用下

## 6. VConsole的使用
+ 在main中引入了 **vconsole** 包，写了监听判断连续敲击点击页面七次，会出现Vconsole，包括正式环境也一样

## 7. 版本更新
+ 在index.html页面中监听了 **error** 事件，会在window中多一个 **errorFn()** 函数，在main.js中会判断如果是旧的js文件报错，会有更新提示，然后刷新页面

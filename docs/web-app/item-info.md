# 项目信息（公众号H5）
## 1. 项目仓库地址
* [https://git.yyide.com/ydyjopen/h5/web-app](https://git.yyide.com/ydyjopen/h5/web-app)
* H5使用vue3 组件使用 vant3

## 2. 项目分支
* 正式：main
* 预发布：uatrelease
* 测试：uat
* 本地部署：2.0版本 — local，3.0版本 — local3.0，3.1版本 — local3.1

## 3. 域名信息
* 正式：一德：[https://mcloud.yyide.com](https://mcloud.yyide.com)  中性版：[https://mcloud.yyide.vip](https://mcloud.yyide.vip)
* 预发布：一德：[https://mclouduat.yyide.com](https://mclouduat.yyide.com)  中性版：[https://mclouduat.yyide.vip](https://mclouduat.yyide.vip)
* 测试：一德：[http://*************:8009](http://*************:8009)（穿透地址： [https://158-8009.yyide.vip](https://158-8009.yyide.vip)）
* 本地部署环境：[http://************:8009](http://************:8009)（穿透地址： [https://11-h5.yyide.vip](https://11-h5.yyide.vip)）
* 注意： 中性版和一德版本域名不同，登录页面样式数据不同，其他相同
* 注意： 穿透地址会用于调用微信官方的api使用，在一加壹公众号平台中，会使用微信扫一扫功能，有配置158穿透域名，可直接使用，主要如果后端显示“缺少配置”，有可能是一德后台的学校没有配置，或者是后端的端口白名单变更，需要重新配置端口白名单，可找商务“郑琪玲”上微信公众号平台配置白名单

## 4. env嵌套Iframe配置
* VITE_BASE_AppH5：appH5项目 [项目仓库](https://git.yyide.com/ydyjopen/h5/apph5) [项目文档](../appH5/index.md) 
* VITE_BASE_OAH5：OA审批 [项目仓库](https://git.yyide.com/ydyjopen/h5/oa-approve-h5)
* VITE_BASE_APPLET：uniapp 分包应用 [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-applet) [项目文档](../yide-applet/item-info.md) 
* VITE_XYF_API：校易付 [项目仓库](https://git.yyide.com/ydyjopen/h5/yide-pay-applet)

## 5. 本地部署
* 本地部署构建命令：build:locals ，本地部署的代码和正式环境的代码有出入，是给南通市紫琅第一小学使用的，不可用功能有：首页登录页面滑块，忘记密码中的获取验证码，消息页学生留言，项目中title为"南通市紫琅第一小学"，应用模块中不可用的有：学生作品，`请假，投票活动，班级德育，校易付，缴费系统，成绩管理，活动报名（注意：本地部署每个分支每个版本代码不同，不要乱改）

## 6. 使用的框架组件
* vue3 sass pinia2
* 组件使用：vant3
* 使用的包：
    + "@tinymce/tinymce-vue": "^4.0.4",
    + "axios": "^0.21.1",
    + "countup.js": "^2.0.8",
    + "crypto-js": "^4.1.1",
    + "dayjs": "^1.11.5",
    + "dingtalk-jsapi": "^3.0.14",
    + "driver.js": "^0.9.8",
    + "echarts": "^5.3.1",
    + "encryptlong": "^3.1.4",
    + "hammer": "^0.0.5",
    + "hammerjs": "^2.0.8",
    + "html2canvas": "^1.0.0-rc.7",
    + "install": "^0.13.0",
    + "js-base64": "^3.7.2",
    + "jsencrypt": "^3.2.1",
    + "md5": "^2.3.0",
    + "moment": "^2.29.1",
    + "npm": "^10.8.3",
    + "nprogress": "^0.2.0",
    + "pinia": "^2.0.33",
    + "secure-ls": "^1.2.6",
    + "standard-version": "^9.5.0",
    + "swiper": "^8.2.4",
    + "tinymce": "^5.10.3",
    + "vant": "^3.6.1",
    + "vconsole": "^3.14.6",
    + "vue": "^3.0.5",
    + "vue-meta": "^2.4.0",
    + "vue-router": "^4.0.9",
    + "vuex": "^4.0.2",
    + "weixin-js-sdk": "^1.6.5",
    + "xgplayer": "^2.32.0"
	+ "@types/node": "^17.0.41",
    + "@vitejs/plugin-vue": "^1.2.3",
    + "@vitejs/plugin-vue-jsx": "^1.1.5",
    + "@vue/compiler-sfc": "^3.0.5",
    + "amfe-flexible": "^2.2.1",
    + "autoprefixer": "^10.2.6",
    + "cross-env": "^7.0.3",
    + "postcss-pxtorem": "^6.0.0",
    + "prettier": "^2.3.1",
    + "qs": "^6.11.0",
    + "sass": "^1.35.1",
    + "vite": "^2.3.7",
    + "vite-plugin-style-import": "^1.4.1"
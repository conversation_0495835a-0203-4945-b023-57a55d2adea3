# 项目公共函数/配置 
## 1. 获取静态文件地址 getStaticFile 
+ 文件地址：/src/utils/common.js，函数：getStaticFile(fileName)，fileName传入你的静态资源地址

+ 使用了env配置的链接VITE_BASE_STATIC

+ 静态文件服务器域名地址：https://file.1d1j.cn/cloud-mobile
  | ftp           |     账号      |  密码            |  端口  |
  | ------------- | :-----------: | :------------:   | :----: |
  | 8.135.12.168  | upload        | IEplz7ijgWnyGIZS | 22    |
+ 前端部分静态资源放在/usr/local/www/file 目录下 通过https://file.1d1j.cn访问，ftp 协议为`SFTP` ，当前项目文件目录为`cloud-mobile`

## 2. 登录加密方法 RSA
+ 文件地址：/src/utils/rsa.js，H5中使用了encryptlong插件加密，在小程序中不支持下载encryptlong包，则下载引用了jsencrypt.js静态文件，文件地址/static/js/jsencrypt.js。encryptor引用的key值（存储加密公钥密钥串）放入了config中

## 3. request接口拦截器

## 4. 数据缓存
+ 用户数据，系统数据，缓存数据都放在store中，使用user，local，system。使用pinia可以选择是否缓存到localStorage中，用户信息，角色信息，学校信息都放在user里面，token单独使用storageToken.js缓存到localStorage中


# 项目业务
## 1. 登录页面
+ 点击登录后，小程序没有滑块验证，使用#ifdef判断处理了

# 页面搬移
## 有一些页面没有搬移过来 或者是部分逻辑暂时延期的 打上“TODO:”标识 到时候可以全局搜索 看看哪些地方逻辑没有写完需要修改
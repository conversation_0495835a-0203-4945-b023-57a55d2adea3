# appH5 原生APP嵌入H5
## 项目信息
### 1. 项目仓库地址
* [https://git.yyide.com/ydyjopen/h5/apph5](https://git.yyide.com/ydyjopen/h5/apph5)
* H5使用vue3 组件使用 vant3


### 2. 项目分支
* 正式：main
* 预发布：uatrelease
* 测试：uat
* 本地部署：2.0版本 — local，3.0版本 — local3.0，3.1版本 — local3.1

### 3. 域名信息
* 正式：[https://wap.yyide.com](https://wap.yyide.com)
* 预发布：[https://wapuat.yyide.com](https://wapuat.yyide.com)
* 测试：[http://*************:8007](http://*************:8007)（穿透地址： [https://test158.yyide.vip](https://test158.yyide.vip)）
* 本地部署环境：[http://************:8007](http://************:8007)（穿透地址： [https://11-8007.yyide.vip](https://11-8007.yyide.vip)）

### 5. 本地部署
* 本地部署构建命令：build:locals ，本地部署的代码和正式环境的代码有出入，是给南通市紫琅第一小学使用的（注意：本地部署每个分支每个版本代码不同，不要乱改）



## 项目公共函数/配置
### 1. 获取当前环境 checkPlatform
+ 文件地址：/src/utils/util.js，函数：checkPlatform()

+ 钉钉环境：按需引入dingtalk-jsapi包，根据钉钉官方API给的判断 dd.env.platform !== **"notInDingTalk"**  判断是否在钉钉浏览器中

+ 企业微信环境：根据navigator.userAgent判断是否有 **“wxwork”** 字符串

+ 微信环境：和企业微信不同，根据navigator.userAgent判断是否有 **“MicroMessenger”** 字符串

+ IOS一加壹App环境：原生IOS会在navigator.userAgent中添加一个 **“yide-android-app”** 给我们判断，根据这个值可以判断在IOS手机的一加壹App中（注意：app老版本中，app给的字段值为 **‘IOS’** ）

+ Android一加壹App环境：原生Android会在navigator.userAgent中添加一个 **“yide-android-app”** 给我们判断，根据这个值可以判断在Android手机的一加壹App中（注意：app老版本中，app给的字段值为 **‘Android’** ）

### 2. 调用一加壹APP的方法 sendAppEvent
+ 文件地址：/src/utils/util.js，函数：sendAppEvent()

+ 根据checkPlatform获取到当前在哪个环境中，如果在‘yide-android-app’安卓手机中的一加壹App中，根据window.android.postMessage获取到安卓传过来的方法，如果是‘yide-ios-app’ios手机中的一加壹App，则使用window.webkit?.messageHandlers.call.postMessage获取到那边的方法，两个都一样需要传两个参数enentName方法名，params参数

+ 使用方法：
	+ 安卓：**获取token:** sendAppEvent("getToken", {}) **获取用户信息：** sendAppEvent("getUserInfo", {})
	+ IOS: **获取token: ** window.prompt("getToken") **获取用户信息：**window.prompt("getUserInfo")
```js
switch (checkPlatform()) {
    case "yide-android-app":
        // 处理Android逻辑
        return (
            window.android &&
            window.android.postMessage &&
            window.android.postMessage(
                JSON.stringify({
                    enentName,
                    params,
                })
            )
        );
    case "yide-ios-app":
        // 处理IOS逻辑
        return window.webkit?.messageHandlers.call.postMessage(
            JSON.stringify({
                enentName,
                params,
            })
        );
}
```

### 3. 设置压缩加密缓存
+ 文件地址：/src/utils/index.js  函数：const { getItem, setItem, removeItem, removeAll } = useStorage()

+ 需要下载 **secure-ls** 包，获取缓存：getItem，设置缓存setItem，删除单个缓存removeItem，删除所有缓存removeAll

+ 获取设置token的函数：const { getToken, getRefreshToken, setToken, setRefreshToken, removeToken } = useToken()

### 4. iconfont配置
+ 在index.html中引入，有一些包下载了在本地的/public文件中，有一些用的是在线链接引用

### 5. vant引用
+ 在main.js中引入**vant** 包，使用的是按需引入，所以有时候引入了组件不生效，需要在main.js中注册使用下

### 6. VConsole的使用
+ 在App.vue中引入了 **vconsole** 包，判断了不在正式环境下都会显示

## 项目功能模块
### 1. 食堂预约
+ 路由:/canteenReservation/home，2021年的时候，对接的第三方接口，有订餐消费统计，还有微信代扣等页面，第三方接口域名：VITE_CANTEEN_API：https://wechatpay.hzleshun.com[第三方食堂API](./食堂%20K12团餐机%20项目云平台%20-%20一德外放接口%20v1.01.004.pdf)

### 2. 周报
+ 路由：/weekly/home，分为‘图书馆周报’和‘考勤周报’，也分了两个端，家长端，老师端，第一次进入的时候会有引导页，使用了driver.js包

### 3. 通知公告编辑功能
+ 给一加壹APP那边的单独的编辑公告的输入框，路由：/notice/edit/:tempId

### 4. 问卷 （监测平台使用）
+ 给监测平台使用的，路由：/questionnaires

### 5. 考试应用（监测平台使用）
+ 给监测平台使用的，路由： /examManage

### 6. 场地预约
+ 路由：/siteBooking，家长老师学生都可看

### 7. 倒计时
+ 路由： /countDown， 分为老师和家长，老师可以新增倒计时

### 8. 学生留言
+ 路由： /chat，使用了腾讯的聊天TUIKit，需要userID和userSig登录身份

### 9. 收集表
+ 路由： /collect，之前给疫情的时候做的收集表，可以收集学生的体温信息等

### 10. 投票
+ 路由： /vote，给一加壹App使用的，在web-app中已被注释隐藏了

### 11. 访客系统
+ 路由： /visitorSystem 

### 12. 通行，考勤，请假，校园视频，校园风采，待办，作业，我的课表，信息发布
+ [web-app有一套相同的迭代最新的代码，使用H5，vant组件 ](../web-app/item-function.md)


## 6. 使用的框架组件
* vue3 sass pinia2
* 组件使用：vant3
* 使用的包：
    * "@amap/amap-jsapi-loader": "^1.0.1",
    * "@tencentcloud/chat-uikit-vue": "^1.3.3",
    * "@tinymce/tinymce-vue": "^4.0.4",
    * "ali-oss": "^6.17.1",
    * "axios": "^0.21.1",
    * "browserslist": "^4.23.0",
    * "caniuse-lite": "^1.0.30001607",
    * "countup.js": "^2.0.8",
    * "crypto-js": "^4.1.1",
    * "dayjs": "^1.11.6",
    * "default-passive-events": "^2.0.0",
    * "dingtalk-jsapi": "^3.0.14",
    * "driver.js": "^0.9.8",
    * "echarts": "5.3.1",
    * "hammer": "^0.0.5",
    * "hammerjs": "^2.0.8",
    * "html2canvas": "^1.0.0-rc.7",
    * "js-base64": "^3.7.2",
    * "jsencrypt": "^3.2.1",
    * "md5": "^2.3.0",
    * "mockjs": "^1.1.0",
    * "moment": "^2.29.1",
    * "nprogress": "^0.2.0",
    * "pinia": "^2.0.33",
    * "secure-ls": "^1.2.6",
    * "standard-version": "^9.5.0",
    * "swiper": "^8.2.4",
    * "tinymce": "^5.10.3",
    * "vant": "^3.6.2",
    * "vconsole": "^3.14.6",
    * "vue": "^3.0.5",
    * "vue-meta": "^2.4.0",
    * "vue-router": "^4.0.9",
    * "vuedraggable": "^4.1.0",
    * "vuex": "^4.0.2"
	* "@types/node": "^17.0.41",
    * "@vitejs/plugin-vue": "^1.2.3",
    * "@vitejs/plugin-vue-jsx": "^1.1.5",
    * "@vue/compiler-sfc": "^3.0.5",
    * "@vue/eslint-config-prettier": "^6.0.0",
    * "amfe-flexible": "^2.2.1",
    * "autoprefixer": "^10.2.6",
    * "babel-eslint": "^10.1.0",
    * "cross-env": "^7.0.3",
    * "dingtalk-jsapi": "^3.0.14",
    * "eslint": "^7.29.0",
    * "eslint-plugin-prettier": "^3.4.0",
    * "eslint-plugin-vue": "^7.11.1",
    * "postcss-pxtorem": "^6.0.0",
    * "prettier": "^2.3.1",
    * "sass": "^1.35.1",
    * "vite": "^2.3.7",
    * "vite-plugin-html": "^3.2.0",
    * "vite-plugin-style-import": "^1.4.1",
    * "vue3-esign": "^1.0.6"